<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-15 15:44:37
-->
<template>
  <div class="device">
    <div class="product_bg">
      <h3 class="bg_title">设备管理</h3>
      <span class="bg_desc"
        >在完成产品建模后，物联网平台支持单个或批量导入方式添加物理设备，注册认证，支持设备全生命周期管理、监控、设备分组等</span
      >
    </div>

    <div class="content-top">
      <div
        class="box"
        style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
      >
        <div class="num">
          <img src="~@/assets/images/monitorMaintain/deviceTotal.png" alt="" />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ statusCount.totalNum || "0" }}</span>
            </p>
            <p>设备总数</p>
          </div>
        </div>
      </div>
      <div
        class="box"
        style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
      >
        <div class="num">
          <img src="~@/assets/images/monitorMaintain/deviceTotal.png" alt="" />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ statusCount.activeNum || "0" }}</span>
            </p>
            <p>设备激活数</p>
          </div>
        </div>
      </div>
      <div
        class="box"
        style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
      >
        <div class="num">
          <img src="~@/assets/images/monitorMaintain/deviceTotal.png" alt="" />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ statusCount.onlineNum || "0" }}</span>
            </p>
            <p>设备在线数</p>
          </div>
        </div>
      </div>
      <div
        class="box"
        style="background: linear-gradient(to bottom, #ffe8cd, #fffaf2)"
      >
        <div class="num">
          <img
            src="~@/assets/images/monitorMaintain/deviceoutline.png"
            alt=""
          />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ "0" }}</span>
            </p>
            <p>设备离线数</p>
          </div>
        </div>
      </div>
    </div>

    <div class="device-top">
      <div class="device-top-search">
        <el-select
          @change="fn_select"
          v-model="searchValue.productId"
          filterable
          placeholder="请选择产品"
        >
          <el-option
            v-for="item in productOptionsCopy"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
<el-select
  @change="fn_select"
  v-model="searchValue.deviceStatus"
  placeholder="请选择状态"
  style="margin-left: 16px;"
>
  <el-option
    v-for="item in deviceStatusOptions"
    :key="item.value"
    :label="item.label"
    :value="item.value"
  ></el-option>
</el-select>

        <div class="top-right">
          <!-- 搜索栏 -->
          <form-search
            :options="deviceOptions"
            defaultId="2"
            @search="fn_search_table_data"
            @clear="fn_clear_search_info"
            :inputHolder="inputHolder"
            :selectHolder="selectHolder"
          ></form-search>
        </div>
        <iot-button text="添加设备" @search="fn_open"></iot-button>
      </div>
    </div>

    <div class="device-table">
      <!-- 表格 -->
      <iot-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        @selection-change="fn_select_more_data"
        @selection-del="fn_del_more_data"
        @del-callbackSure="fn_del_sure"
      >
        <template slot="productName" slot-scope="{ row }">
          <div class="table-longText">
            <p v-if="calcul_long_text(row.productName) <= 29">
              {{ row.productName }}
            </p>
            <el-popover
              v-if="calcul_long_text(row.productName) > 29"
              placement="top-start"
              width="400"
              trigger="hover"
              :content="row.productName"
              popper-class="event-tooltip"
            >
              <p slot="reference">
                {{ row.productName }}
              </p>
            </el-popover>
          </div>
        </template>
        <template slot="aliasName" slot-scope="{ row }">
          <div class="table-longText">
            <p v-if="calcul_long_text(row.aliasName) <= 29">
              {{ row.aliasName }}
            </p>
            <el-popover
              v-if="calcul_long_text(row.aliasName) > 29"
              placement="top-start"
              width="400"
              trigger="hover"
              :content="row.aliasName"
              popper-class="event-tooltip"
            >
              <p slot="reference">
                {{ row.aliasName }}
              </p>
            </el-popover>
          </div>
        </template>
        <template slot="status" slot-scope="scope">
          <div class="table-status">
            <div class="status flex" v-if="scope.row.status == 6">
              <div class="red"></div>
              <div>离线</div>
            </div>
            <div class="status flex" v-if="scope.row.status == 5">
              <div class="green"></div>
              <div>在线</div>
            </div>
            <div class="status flex" v-if="scope.row.status == 4">
              <div class="yellow"></div>
              <div>未激活</div>
            </div>
          </div>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_check(scope.row, 1)" class="color2">
              查看
            </p>
            <p></p>
            <p @click="fn_del(scope.row.id)" class="color2">删除</p>
            <p></p>
            <!-- v-if="scope.row.nodeTypeKey == 1" -->
            <p @click="fn_check(scope.row, 0)" class="color2">
              子设备({{ scope.row.subDeviceNum }})
            </p>
          </div>
        </template>
      </iot-table>
    </div>

    <div class="device-bottom" v-if="tableData.length">
      <!-- 分页 -->
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      @callbackSure="fn_sure"
      @close="fn_close"
    >
      <template #body>
        <!-- 新增和修改 -->
        <iot-form v-if="type == 1">
          <el-radio-group
            v-model="radio1"
            size="medium"
            style="margin-bottom: 26px"
          >
            <el-radio-button label="0">单个添加</el-radio-button>
            <el-radio-button label="1">批量添加</el-radio-button>
          </el-radio-group>
          <!-- <div class="specialDesc flex">
            <img src="~@/assets/images/index/specialTips.png" alt />
            <span>
              特别说明:
              DeviceName可以为空，当为空时，钰辰云会颁发产品下的唯一标识作为DeviceName
            </span>
          </div> -->
          <el-form
            class="deviceForm"
            ref="deviceForm"
            :label-position="'top'"
            :model="deviceForm"
            :rules="rules"
            @validate="fn_validate"
            label-width="80px"
          >
            <el-form-item label="产品" prop="productId">
              <el-select
                v-model="deviceForm.productId"
                filterable
                placeholder="请选择产品"
              >
                <template slot="empty">
                  <div class="empty-project">
                    <span>产品列表里没有匹配的数据，请先去创建产品</span>
                  </div>
                </template>
                <el-option
                  v-for="item in productOptions"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                ></el-option>
              </el-select>
            </el-form-item>

            <div v-if="radio1 == '0'">
              <el-form-item label="DeviceName" prop="deviceName">
                <el-input v-model="deviceForm.deviceName"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持英文字母、数字、下划线（_），中划线（-）、点号（.）、半冒号（:）和特殊字符@、长度限制为6-16个字符；
              </div>

              <el-form-item label="备注名称" prop="aliasName">
                <el-input v-model="deviceForm.aliasName"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="aliasTrue">
                不支持特殊字符，字数不能超过32个字符
              </div>
              <el-form-item label="设备描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="deviceForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </div>

            <el-form-item label="批量上传文件" prop="upfile" v-if="radio1=='1'">
              <iot-upload
                ref="iotUpload"
                label="选择文件"
                btnWidth="120"
                uploadTips="·文件格式为：(逗号分隔值) (.csv)·单次最多添加500台"
                :errorMsg="importErrorMsg"
                :formData="importFormData"
                :autoUpload="false"
                :beforeUpload="importBefore"
                :success="importSuccess"
                :error="importError"
                @fileChange="importFileChange"
              />
            </el-form-item>
          </el-form>
        </iot-form>
        <div v-if="type == 2">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除该设备，设备删除后不可恢复，请确认是否删除该设备？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import FormSearch from "@/components/form-search";
import IotForm from "@/components/iot-form";
import IotButton from "@/components/iot-button";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotTable from "@/components/iot-table";
import IotUpload from "@/components/iot-upload";
import {
  getDeviceList,
  getDeviceSave,
  getDeviceDelete,
  getDeviceStatusNum,
} from "@/api/device";
import { getProductSelect } from "@/api/product";
import { reg_thirteen, reg_seven } from "@/util/util.js";
export default {
  name: "Device",
  components: {
    FormSearch,
    IotForm,
    IotButton,
    IotPagination,
    IotDialog,
    IotTable,
    IotUpload,
  },
  data() {
    return {
      columns: [
        {
          type: "selection",
          selectionText: false,
          title: "确定删除该设备？",
          text: "批量删除设备后，设备数据不可恢复，请确认是否删除？",
          visible: false,
          isShowdelete: true,
        },
        { label: "DeviceName", prop: "deviceName", width: 240 },
        {
          label: "备注名称",
          prop: "aliasName",
          width: 220,
          slotName: "aliasName",
        },
        {
          label: "设备所属产品",
          prop: "productName",
          width: 220,
          slotName: "productName",
        },
        { label: "节点类型", prop: "nodeType" },
        { label: "状态", prop: "status", slotName: "status" },
        { label: "最后上线时间", prop: "lastReportTime", width: 200 },
        {
          label: "操作",
          prop: "operation",
          slotName: "operation",
          // width: 200,
        },
      ],
      radio1: "0",
      importErrorMsg: "",
      importAction:getDeviceList,
      importFormData: {},
      tableData: [],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      // 加载效果开关
      loading: true,
      dialogWidth: "792px",
      type: 1, //1 查看 2 删除
      visible: false,
      inputHolder: "请输入搜索关键词",
      selectHolder: "请选择设备名称",
      searchValue: {
        productId: "",
        deviceStatus: null,
      },
      // 产品列表
      productOptions: [],
      productOptionsCopy: [],
      // 设备列表
      deviceOptions: [
        {
          id: "1",
          name: "备注名称",
        },
        {
          id: "2",
          name: "DeviceName",
        },
      ],
     // 设备状态选项
deviceStatusOptions: [
  {
    label: "全部状态",
    value: null,
  },
  {
    label: "在线",
    value: 5,
  },
  {
    label: "离线", 
    value: 6,
  },
  {
    label: "未激活",
    value: 4,
  },
],

      statusCount: {
        totalNum: 0,
        activeNum: 0,
        onlineNum: 0,
      },
      // 表单数据
      deviceForm: {
        productId: "", // 产品名称
        deviceName: "", // 设备标识
        aliasName: "", // 设备备注名称
        description: "", // 描述
      },
      nameTrue: true,
      aliasTrue: true,
      descTrue: true,
      rules: {
        productId: [
          {
            required: true,
            trigger: "change",
            message: "请选择产品",
          },
        ],
        deviceName: [{ required: false, validator: this.checkDevice }],
        aliasName: [{ required: false, validator: this.checkAliasName }],
        description: [
          {
            required: false,
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
        upfile: [{ require: true,trigger: "change", }],
      },
      title: "",
      delId: "",
      // 多选删除
      delIds: [],
    };
  },
  created() {},
  watch: {
    visible(val) {
      if (!val && this.type == 1) {
        this.$refs["deviceForm"] && this.$refs["deviceForm"].resetFields();
      }
    },
    delIds(val) {
      if (val.length == 0) {
        this.columns[0].selectionText = false;
      }
    },
  },
  // keepalive 生命周期      //组件激活时触发
  activated() {
    if (this.$route.query.id) {
      this.searchValue.productId = this.$route.query.id;
    }
    let data = {
      ...this.searchValue,
      current: this.pagination.current,
      size: this.pagination.size,
    };

    this.fn_get_table_data(data);
    this.fn_get_device_status_count();
    this.fn_get_product_select();
  },
  //  跳转非详情   清除 keep-alive 缓存数组中的缓存视图
  beforeRouteLeave(to, from, next) {
    if (to.path != "/deviceDetail") {
      // 取消缓存
      this.$clearKeepAlive(this, from.path);
    }
    next();
  },
  methods: {
    calcul_long_text(val = "") {
      return val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length;
    },
    // 产品或状态选择改变时触发
    fn_select() {
      this.pagination.current = 1;
      let data = {
        ...this.searchValue,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(data);
    },
    checkAliasName(rule, value, callback) {
      if (value != "") {
        if (!reg_seven(value, 32)) {
          return callback(new Error("最多不超过32个字符"));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    checkDevice(rule, value, callback) {
      if (value != "") {
        if (!reg_thirteen(value)) {
          return callback(
            new Error(
              "支持英文字母、数字、下划线（_），中划线（-）、点号（.）、半冒号（:）和特殊字符@、只能以英文开头、长度限制为6-16个字符；"
            )
          );
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
    fn_get_device_status_count() {
      getDeviceStatusNum().then((res) => {
        this.statusCount = res.data;
      });
    },
    // 产品列表下拉
    fn_get_product_select() {
      getProductSelect().then((res) => {
        if (res.code == 200) {
          this.productOptions = res.data;
          this.productOptionsCopy = [
            {
              name: "全部产品",
              id: "",
            },
            ...res.data,
          ];
        }
      });
    },
    // 设备列表-表格数据接口
    fn_get_table_data(params = {}) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      getDeviceList(others)
        .then((res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
            }, 300);
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 删除数据接口
    fn_del_table_data(data) {
      getDeviceDelete(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
          this.visible = false;
          this.columns[0].visible = false;
          this.fn_get_device_status_count();
          let data = {
            ...this.searchValue,
            size: this.pagination.size,
            current: 1,
          };
          this.fn_get_table_data(data);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "deviceName") {
        this.nameTrue = value;
      }
      if (name === "description") {
        this.descTrue = value;
      }
      if (name === "aliasName") {
        this.aliasTrue = value;
      }
    },
    // 多选数据
    fn_select_more_data(data) {
      // console.log(data)
      this.delIds = data.map((item) => {
        return item.id;
      });
      if (this.delIds.length !== 0) {
        this.columns[0].selectionText = true;
      } else {
        this.columns[0].selectionText = false;
      }
    },
    // 多选删除按钮
    fn_del_more_data(data) {
      // console.log("批量删除");
      if (this.delIds.length !== 0) {
        this.columns[0].selectionText = true;
        this.columns[0].visible = true
        this.delIds = data
      } else {
        return;
      }
    },
    // 多选删除确定按钮
    fn_del_sure() {
      let data = {
        ids: this.delIds.join(","),
      };
      console.log("data",data);
      this.fn_del_table_data(data);
    },
    // 打开dialog
    fn_open() {
      this.title = "添加设备";
      this.type = 1;
      this.dialogWidth = "792px";
      this.visible = true;
    },
    // 搜索
    fn_search_table_data(params) {
      console.log(params);
      if (params.id === "1") {
        this.searchValue.aliasName = params.value;
      } else {
        this.searchValue.deviceName = params.value;
      }
      let data = { ...this.searchValue };
      data.size = this.pagination.size;
      this.fn_get_table_data(data);
    },

    importBefore(fileList) {},
    importSuccess() {},
    importError(err) {},
    importFileChange(num) {},
















    // 当前页总条数
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.pagination.size = val;
      let params = {
        size: this.pagination.size,
        current: 1,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.pagination.current = val;
      // console.log(this.searchValue);
      let params = {
        ...this.searchValue,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    // 清除输入搜索
    fn_clear_search_info() {
      this.searchValue.aliasName = "";
      this.searchValue.deviceName = "";
    this.searchValue.deviceStatus = null; // 新增这行
    },
    // 查看详情的跳转
    fn_check(data, num) {
      this.$router.push({
        path: "/deviceDetail",
        query: {
          id: data.id,
          // title: data.deviceName,
          // status: data.status,
          // key: data.nodeTypeKey,
          num: num,
        },
      });
    },
    // 弹窗确认按钮
    fn_sure() {
      // 删除确认
      if (this.type === 2) {
        let data = {
          ids: this.delId,
        };
        this.fn_del_table_data(data);
      }
      // 新增确认
      else if (this.type === 1) {
        this.$refs["deviceForm"].validate((valid) => {
          if (valid) {
            getDeviceSave(this.deviceForm).then((res) => {
              if (res.code == 200) {
                this.$newNotify.success({
                  message: res.message,
                });
                this.pagination.current = 1;
                this.searchValue.productId = "";
                this.fn_get_device_status_count();
                this.fn_get_table_data({
                  size: this.pagination.size,
                  current: 1,
                });
                this.visible = false;
              } else {
                this.$newNotify.error({
                  message: res.message,
                });
              }
            });
          }
        });
      }
    },
    fn_close() {
      this.aliasTrue = true;
      this.nameTrue = true;
      this.descTrue = true;
    },
    // 行删除
    fn_del(id) {
      // console.log(id);
      this.delId = id;
      this.title = "确定删除该设备？";
      this.type = 2;
      this.dialogWidth = "550px";
      this.visible = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.device {
  padding-bottom: 20px;
  .product_bg {
    width: 100%;
    height: 136px;
    background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px 0px;
    background-size: 100%;
    padding: 35px 0px 0px 32px;
    .bg_title {
      font-weight: 500;
      font-size: 22px;
      line-height: 30px;
      color: #333333;
    }
    .bg_desc {
      font-weight: 400;
      font-size: 16px;
      line-height: 30px;
      color: #77797c;
    }
  }

  .content-top {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 32px 32px 0 32px;
    // padding-top: 30px;
    .box {
      width: 385px;
      height: 102px;
      display: flex;
      padding: 0px 60px 0px 32px;
      box-shadow: 0px 8px 18px 0px rgba(0, 0, 0, 0.03);
      border-radius: 2px;
      .num {
        width: 153px;
        display: flex;
        justify-content: left;
        align-items: center;
        img {
          width: 58px;
          height: 58px;
          vertical-align: middle;
          text-align: center;
        }
      }
      .name {
        // font-family: YSBT;
        width: 153px;
        text-align: right;
        display: flex;
        justify-content: right;
        align-items: center;
        p {
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 16.41px;
          color: #515151;
          font-family: HarmonyOS Sans SC;
          margin-top: 9px;
        }
        span {
          font-size: 28px;
          font-weight: 700;
          line-height: 28.13px;
        }
      }
    }
  }

  .device-top {
    padding: 0px 32px 0 32px;
    font-family: HarmonyOS Sans SC;
    .device-top-search {
      margin: 32px 0 16px 0;
      width: 100%;
      display: flex;
      justify-content: end;
      .el-select {
        margin-right: 12px;
      }
      :deep(.el-input__inner) {
        border-radius: 0;
      }
    }
  }
  .device-table {
    padding: 0px 32px 0 32px;
    .table-edit {
      display: flex;
      align-items: center;
      p {
        flex-shrink: 0;
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
      p:nth-child(4) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
    .table-status {
      .status {
        .red {
          background: #ff4d4f;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .green {
          background: #00c250;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .yellow {
          background: #e6a23c;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
      }
    }
  }
  .device-bottom {
    text-align: right;
    margin-top: 14px;
  }
  .del-tips {
    width: 420px;
  }
  .deviceForm {
    :deep(.el-form-item) {
      margin-bottom: 24px;
    }
    .el-form-tips {
      // margin-top: -17px;
      margin-top: -22px;
      margin-bottom: 6px;
    }
  }
  .specialDesc {
    padding: 11px 0 11px 14px;
    background-color: rgba(1, 138, 255, 0.08);
    margin-bottom: 18px;
    span {
      font-size: 12px;
      line-height: 14px;
    }
    img {
      display: inline-block;
      width: 14px;
      height: 14px;
      line-height: 14px;
      margin-right: 10px;
    }
  }
}
</style>
