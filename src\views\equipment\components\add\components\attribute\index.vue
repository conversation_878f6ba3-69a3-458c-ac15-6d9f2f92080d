<template>
  <div class="item">
    <data-type
      :form="form"
      key="attributeDataType"
      :enumTips="enumTips"
      :boolTips="boolTips"
      :objectTips="objectTips"
      :arrayTips="arrayTips"
      :unitList="unitList"
      @addAttribute="addAttribute"
      @editAttribute="editAttribute"
      @deleteAttribute="deleteAttribute"
      @change="valChange"
    />
    <el-form-item prop="mode">
      <div class="form-item">
        <p class="form-item-label">读写类型</p>
        <el-radio-group v-model="readOnly" @change="readOnlyChange">
          <el-radio label="rw">读写</el-radio>
          <el-radio label="r">只读</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
  </div>
</template>

<script>
import dataType from "../data-type";
export default {
  name: "form-attribute",
  data() {
    return {
      type: "int", //数据类型
      readOnly: "rw", //只读
    };
  },
  components: { dataType },
  props: {
    form: {
      type: Object,
    },
    enumTips: {
      type: String,
    },
    boolTips: {
      type: String,
    },
    arrayTips: {
      type: String,
    },
    objectTips: {
      type: String,
    },
    unitList: {
      type: Array,
    },
  },
  watch: {
    form: {
      deep: true,
      handler: "defaultSet",
    },
  },
  mounted() {
    this.defaultSet();
  },
  methods: {
    defaultSet() {
      if (this.form) {
        this.readOnly = this.form.mode;
      }
    },
    readOnlyChange(value) {
      this.$emit("change", {
        key: "mode",
        value,
      });
    },
    // 对象组件  传递的数据
    addAttribute(data) {
      this.$emit("addAttribute", data);
    },
    //对象组件 修改数据
    editAttribute(data) {
      this.$emit("editAttribute", data);
    },
    deleteAttribute(data) {
      this.$emit("deleteAttribute", data);
    },
    // 基本类型组件传递的数据
    valChange(data) {
      this.$emit("change", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.item {
  :deep(.el-radio-button__inner) {
    width: 110px;
  }
  :deep(.el-input__inner) {
    border-radius: 0;
  }
  :deep(.form-item-label) {
    line-height: 30px;
  }
  :deep(.el-form-item__content) {
    line-height: 15px;
  }
}
.gap {
  height: 6px;
}
</style>
