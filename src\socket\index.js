import SockJS from "sockjs-client";
import Stomp from "stompjs";
// import Token from '../store/util/token';

// import { Notification } from 'element-ui';

// const { fn_token__get } = Token;

// let defaultStatus = false
class Socket {
  client = null; // STOMP 协议的客户端对象
  monitorEvents = new Map(); // 消息订阅对象集合
  waitSubscribe = []; // WS未连接成功时，订阅的事件先缓存，连接成功后再自动订阅
  connected = false; // ws连接状态
  reconnect_num = 0; // 重连次数
  max_reconnect_num = 4; // 最大重连次数
  /**
   * @desc Socket websocket服务集成
   * @param {string} ws 链接地址
   * @param {object} [header] 自定义请求头 服务端 可利用 Header 里面的信息进行权限认证
   * @param {Function} [connectCallback] 连接成功的事件
   * @param {Function} [errorCallback] 连接失败的事件
   */
  constructor({ ws, header, connectCallback, errorCallback }) {
    this.wsOptions = {
      ws,
      header,
      connectCallback,
      errorCallback,
    };
    this.ws = ws;
    this.header = header;
    this.connectCallback = connectCallback;
    this.errorCallback = errorCallback;
  }

  // 建立连接 异步操作
  asyncConnect(ws = "") {
    // Notification.closeAll();
    // 已连接，直接返回 避免重复连接
    if (this.connected) {
      return {
        success: true,
        frame: null,
      };
    }
    // ws 不能为空
    ws = ws || this.ws;
    this.ws = ws;
    if (!ws) {
      throw "ws is must";
    }
    // 建立连接对象（还未发起连接）
    let socket = new SockJS(ws);
    // 获取 STOMP 子协议的客户端对象
    this.client = Stomp.over(socket);

    this.client.heartbeat.outgoing = 20000; // 若使用STOMP 1.1 版本，默认开启了心跳检测机制（默认值都是10000ms）
    this.client.heartbeat.incoming = 0; // 客户端不从服务端接收心跳包
    this.client.debug = function (str) {
      console.log(str);
    };
    // 开始建立连接 异步操作
    const $this = this;
    return new Promise((resolve, reject) => {
      this.client.connect(
        this.header || {},
        function (frame) {
          $this.connected = true;
          console.log("连接成功！", frame);

          // if (!defaultStatus) {
          // 	defaultStatus = true
          // 	// Notification({
          // 	// 	title: '成功',
          // 	// 	message: 'ws 连接成功！',
          // 	// 	type: 'success',
          // 	// })
          // 	console.log('ws 连接成功！')
          // }
          $this.waitSubscribe.forEach((item) => $this.subscribe(item));
          typeof $this.connectCallback === "function" &&
            $this.connectCallback(frame);
          resolve({
            success: true,
            frame,
          });
        },
        function (error) {
          // console.log('ws 连接失败！', error)
          // defaultStatus = false
          // Notification({
          // 	title: '错误',
          // 	message: 'ws 连接失败！',
          // 	type: 'warning',
          // })
          // console.log('ws 连接失败！')
          socket = null;
          typeof $this.errorCallback === "function" &&
            $this.errorCallback(error);
          //   const token = fn_token__get();
          //   if (token) {
          //     $this._reconnect();
          //   }
          reject({
            success: false,
            error,
          });
        }
      );
    });
  }

  // 异常断开 重连

  _reconnect() {
    this.connected = false;
    if (this.reconnect_num === this.max_reconnect_num) {
      this.reconnect_num = 0;
      this.monitorEvents.clear();
    }
    this.reconnect_num++;
    setTimeout(() => this.asyncConnect(), 3000);
  }

  // 客户端主动断开连接 异步操作
  asyncDisconnect(disconnectCallback) {
    const $this = this;
    new Promise((resolve) => {
      this.client.disconnect(function () {
        typeof disconnectCallback === "function" && disconnectCallback();
        $this.client = null;
        $this.connected = false;
        $this.monitorEvents.clear();
        resolve();
      });
    });
  }

  /**
   * @desc 发送消息
   * @param {string} destination 发送目的地（地址）
   * @param {object} [header] 其他消息头
   * @param {string|object} [body] 消息正文
   * @returns {object} res
   */
  send(destination, body = "", header = {}) {
    if (typeof destination !== "string" || !destination) {
      throw "destination 必须是一个有效的字符串";
    }
    if (typeof body === "object") {
      body = JSON.stringify(body);
    }
    // body 必须为字符串
    this.client.send(destination, header, body);
    return {
      success: true,
    };
  }

  /**
   * @desc 消息订阅 支持批量订阅
   * @param {object} params
   * @param {array|string} params.destination 订阅地址  [destination, {destination, [callback], [header]}, destination, {destination, [callback], [header]}]
   * @param {function} [params.callback(message[Object])] 接收消息的回调
   * @param {object} [params.headers] 其他消息头
   */
  // 重写消息订阅函数
  subscribe({ destination, callback, headers }) {
    // console.log(destination)
    const _callback = function (message) {
      // 用户消息处理事件
      callback(message, destination);
    };
    this.oldSubscribe({
      destination,
      callback: _callback,
      headers,
    });
  }
  oldSubscribe({ destination, callback, headers }) {
    if (!destination) {
      throw "destination is must";
    }
    if (typeof destination === "string") {
      // 判断ws是否已连接，未连接时缓存进待连接集合
      if (this.connected) {
        // 判断是否订阅
        if (this.monitorEvents.has(destination)) {
          this.unsubscribe(destination);
        }
        const subscription = this.client.subscribe(
          destination,
          function (iframe) {
            let data = null;
            const destination = iframe.destination;
            try {
              data = JSON.parse(iframe.body);
            } catch (error) {
              data = iframe.body;
            }
            typeof callback === "function" && callback(data, destination);
          },
          headers || {}
        );
        this.monitorEvents.set(destination, subscription);
      } else {
        this.waitSubscribe.push({
          destination,
          callback,
          headers,
        });
      }
    }
    if (Object.prototype.toString.call(destination) === "[object Array]") {
      for (const item of destination) {
        let temp_destination = "";
        let temp_callback = callback;
        let temp_header = headers;
        if (typeof item === "string") {
          temp_destination = item;
        } else if (
          Object.prototype.toString.call(destination) === "[object Object]"
        ) {
          temp_destination = destination.destination;
          temp_callback = destination.callback || callback;
          temp_header = destination.header || headers;
        }
        this.subscribe({
          destination: temp_destination,
          callback: temp_callback,
          header: temp_header,
        });
      }
    }
  }

  /**
   * @desc 取消订阅
   * @param {string|string[]} [destination] 订阅地址, 不传则取消全部订阅
   */
  unsubscribe(destination) {
    console.log("unsubscribe:取消订阅===>", destination);
    if (Object.prototype.toString.call(destination) === "[object Array]") {
      this.destination.forEach((destination) => this.unsubscribe(destination));
    } else if (typeof destination === "string") {
      const subscription = this.monitorEvents.get(destination);
      if (subscription) {
        subscription.unsubscribe();
        this.monitorEvents.delete(destination);
      }
    } else if (!destination) {
      this.monitorEvents.forEach((subscription) => {
        subscription.unsubscribe();
      });
      this.monitorEvents.clear();
    }
  }
}
export default Socket;
