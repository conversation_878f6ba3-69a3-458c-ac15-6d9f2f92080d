<template>
  <div>
    <div class="info-content">
      <div class="info-title">
        <div>设备信息</div>
        <!-- <div class="info-edit color2" @click="fn_open('edit')">
          <img src="~@/assets/images/index/edit.svg" />
          <span>编辑</span>
        </div> -->
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>产品名称</span>
          <span>{{ deviceFormData.productName || "-" }}</span>
        </div>
        <div class="item">
          <span>Productkey</span>
          <span>{{ deviceFormData.productKey || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>DeviceSecret</span>
          <span>{{ deviceFormData.deviceSecret || "-" }}</span>
        </div>
        <div class="item">
          <span>DeviceName</span>
          <span>{{ deviceFormData.deviceName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>IP地址</span>
          <span>{{ deviceFormData.ipAddress || "-" }}</span>
        </div>
        <div class="item">
          <span>备注名称</span>
          <span>{{ deviceFormData.aliasName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>节点类型</span>
          <span>{{ deviceFormData.nodeType || "-" }}</span>
        </div>
        <div class="item">
          <span>通讯方式</span>
          <span>{{ deviceFormData.networkWay || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>当前状态</span>
          <span>{{ deviceFormData.status | statusText }}</span>
        </div>
        <div class="item">
          <span>激活时间</span>
          <span>{{ deviceFormData.activeTime || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>创建时间</span>
          <span>{{ deviceFormData.createTime || "-" }}</span>
        </div>
        <div class="item">
          <span>最后上线时间</span>
          <span>{{ deviceFormData.lastReportTime || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>设备描述</span>
          <span>{{ deviceFormData.description || "-" }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: {
    deviceFormData:{
      type:Object,
    }
  },
  data() {
    return{}
  },
  filters: {
    statusText(val) {
      return ["未激活", "在线", "离线"][+val - 4];
    },
  },

  methods: {
    fn_open(type) {
      console.log("type", type);
    },
  },
};
</script>

<style scoped lang="scss">
.info-content {
  box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
  border-radius: 2px;
  padding: 20px 32px 10px;
  margin-top: 18px;

  .info-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    .info-edit {
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: H_Regular;
      img {
        width: 12px;
        height: 12px;
        margin-right: 6px;
      }
    }
  }
  .info-row {
    .item {
      padding: 10px 0;
      flex: 1;
      display: inline-flex;
      span {
        height: 16px;
        line-height: 16px;
        font-size: 14px;
        font-weight: 400;
        &:first-child {
          margin-left: 48px;
          flex-shrink: 0;
          color: #999999;
          width: 90px;
          text-align: right;
          // margin-left: 26px;
        }
        &:last-child {
          width: 100%;
          color: #515151;
          margin-left: 48px;
        }
      }
    }

    .tag {
      height: 300px;
      padding: 10px 0;
      flex: 1;
      display: inline-flex;
      span {
        height: 32px;
        line-height: 32px;
        font-size: 14px;
        font-weight: 400;
        &:first-child {
          margin-left: 48px;
          flex-shrink: 0;
          color: #999999;
          width: 90px;
          text-align: right;
          // margin-left: 26px;
        }
      }
      .spans {
        height: 280px;
        span {
          height: 32px;
          line-height: 32px;
          margin-left: 8px;
          padding: 0 10px 0 10px;
          font-size: 14px;
          font-weight: 400;
          border-radius: 50px;
          color: #515151;
          background: #edf1f7;
          &:first-child {
            margin-left: 48px;
            flex-shrink: 0;
            color: #515151;
            width: 90px;
            text-align: right;
            // margin-left: 26px;
          }
        }
      }
    }
  }
}
</style>
