import router from "@/router";
export default {
  state: () => {
    return {
      userInfo: {},
    };
  },
  mutations: {
    setUserInfo(state, data) {
      state.userInfo = data;
    },
  },
  actions: {
    setUserInfo({ commit }, data) {
      commit("setUserInfo", data);
    },
    loginOut() {
      localStorage.removeItem("access_token");
      localStorage.removeItem("tenant_id");
      localStorage.removeItem("userInfo");
      router.replace({ path: "/login" });
    },
  },
  getters: {},
};
