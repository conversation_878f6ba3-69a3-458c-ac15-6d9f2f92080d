<template>
  <iot-dialog :visible.sync="visible"
              :title="title"
              :width="dialogWidth"
              comfirmText="提交"
              @callbackSure="fn_sure"
              @close="handleClose">
    <template #body>
      <!-- 新增和修改 -->
      <el-form ref="form"
               :model="form"
               :rules="rules">
        <el-form-item prop="product">
          <div class="form-item-title">
            <p>订阅产品</p>
          </div>
          <el-select v-model="form.product"
                     disabled>
            <el-option value="1"
                       label="产品"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="event">
          <div class="form-item-title">
            <p>选择事件 <span>*</span></p>
          </div>
          <el-select v-model="form.event"
                     multiple
                     collapse-tags
                     placeholder="请选择订阅事件">
            <el-option v-for="item in eventList"
                       :key="item.id"
                       :value="item.id"
                       :label="item.dictValue"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
    </template>
  </iot-dialog>
</template>

<script>
import IotDialog from '@/components/iot-dialog'
import { mqttEventList, mqttEventUpdate } from '@/api/cloud'
export default {
  data() {
    return {
      title: '选择订阅事件',
      visible: false,
      dialogWidth: '480px',
      form: {
        product: '',
        event: '',
      },
      rules: {
        event: {
          required: true,
          message: '请选择订阅事件',
          trigger: 'change',
        },
      },
      eventList: [],
    }
  },
  components: { IotDialog },
  methods: {
    open(object) {
      this.getDict()
      this.visible = true
      this.id = object.id
      this.form.product = object.productName
      this.form.event = Object.keys(object.eventMap)
    },
    getDict() {
      mqttEventList().then((res) => {
        if (res.code == 200) {
          this.eventList = res.data
        }
      })
    },
    fn_sure() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          mqttEventUpdate({
            eventId: this.form.event.join(','),
            id: this.id,
          }).then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              })
              this.visible = false
              this.handleClose()
              this.$emit('ok')
            } else {
              this.$newNotify.warning({
                message: res.message,
              })
            }
          })
        } else {
          return false
        }
      })
    },
    handleClose() {
      this.$refs.form.resetFields()
    },
  },
}
</script>

<style lang="scss" scoped>
.form-item-title {
  line-height: 34px;
  p {
    color: #515151;
    font-size: 14px;
    span {
      color: #ff4d4f;
    }
  }
}
/deep/ {
  .el-form-item__content {
    line-height: 34px;
  }
  .el-select {
    width: 100%;
  }
  .el-form-item {
    margin-bottom: 16px;
  }
  .el-input__inner {
    border-radius: 0;
  }
}
</style>
