<template>
  <div id="main">
    <div class="product_bg">
      <h3 class="bg_title">设备分组</h3>
      <span class="bg_desc"
        >设备分组可以根据业务需求实现设备资源的重新组合及权限控制，每个设备只能划分进一个分组。</span
      >
    </div>
    <div class="deviceGroup">
      <div class="deviceGroup-nav">
        <el-select
          placeholder="请选择分组名称"
          v-model="selectedGroupName"
          @change="handleChange(selectedGroupName)"
        >
          <el-option
            v-for="item in groupNames"
            :key="item"
            :value="item"
            :label="item"
          >
          </el-option>
        </el-select>
        <Iotinput
          placeholder="请输入分组名称"
          :value="searchParams.name"
          @input="handleInput"
        ></Iotinput>
        <Iot-button text="搜索" @search="search"></Iot-button>
        <Iot-button
          text="重置"
          type="grey"
          @search="reset"
          style="margin-left: 12px; margin-right: 12px"
        ></Iot-button>
        <Iot-button text="+ 新增分组" @search="fn_open"></Iot-button>
      </div>
      <div class="deviceGroup-table">
        <iot-table :columns="columns" :data="tableData" :loading="loading">
          <template slot="operation" slot-scope="{ row }">
            <div class="flex table-edit">
              <p class="color2" @click="deviceGropuDetail(row)">详情</p>
              <p class="table-line"></p>
              <p class="color2" @click="deviceGroupEdit(row)">编辑</p>
              <p class="table-line"></p>
              <p class="color2" @click="deviceGroupDelete(row)">删除</p>
            </div>
          </template>
        </iot-table>
      </div>

      <iot-dialog
        :visible.sync="visible"
        :title="title"
        :width="dialogWidth"
        :showLoading="true"
        @callbackSure="fn_sure"
        @close="fn_close"
      >
        <template #body>
          <iot-form v-if="type == 1 || type == 2">
            <template #default>
              <el-form
                class="produceForm"
                ref="produceForm"
                :label-position="'top'"
                :model="produceForm"
                :rules="rules"
                @validate="fn_validate"
                label-width="80px"
              >
                <el-form-item label="分组名称" prop="name">
                  <el-input v-model="produceForm.name"></el-input>
                </el-form-item>
                <div class="el-form-tips" v-if="nameTrue">
                  1-64位字符、支持中文、英文、数字及特殊符号，必须中文或者英文字母开头
                </div>
                <el-form-item label="分组简介">
                  <el-input
                    :maxlength="100"
                    type="textarea"
                    v-model="produceForm.introduce"
                  ></el-input>
                </el-form-item>
                <div class="el-form-tips" v-if="descTrue">
                  最多不超过100个字符
                </div>
              </el-form>
            </template>
          </iot-form>

          <div v-if="type == 3">确定删除该分组么？删除后无法还原！</div>
        </template>
      </iot-dialog>

      <!-- 分页 -->
      <div class="produce-bottom" v-if="tableData.length">
        <iot-pagination
          :pagination="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button";
import Iotinput from "@/components/iot-input";
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";

import {
  getDeviceGroundList,
  addDeviceGroup,
  editDeviceGroup,
  deleteDeviceGroup,
} from "@/api/equipment";

import { reg_twentyThree } from "@/util/util.js";
export default {
  name: "deviceGroup",
  components: {
    IotButton,
    Iotinput,
    IotTable,
    IotPagination,
    IotDialog,
    IotForm,
  },
  data() {
    return {
      columns: [
        { label: "序号", prop: "index", width: "150px" },
        { label: "分组名称", prop: "name" },
        { label: "分组ID", prop: "groupId" },
        { label: "设备数量", prop: "deviceNumber" },
        { label: "创建时间", prop: "createTime" },
        { label: "操作", slotName: "operation" },
      ],
      tableData: [], // 表格数据
      groupNames: [], // 添加 groupNames 数组来保存去重后的分组名称
      selectedGroupName: "", //下拉框绑定的值

      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      searchParams: {
        //搜索关键字
        name: "",
      },

      dialogWidth: "718px", //弹出框长度
      title: "", //弹出框标题
      visible: false, //是否显示

      produceForm: {
        //新增分组所需参数
        id: null,
        name: "",
        introduce: "",
      },
      // editForm:{
      //     id:null,
      //     name:'',
      //     introduce:''
      // },

      nameTrue: true,
      descTrue: true,
      type: null, //1 创建 2修改 3删除

      rules: {
        name: [
          {
            required: true,
            // message: "分组名称不能为空！",
            trigger: "blur",
            validator: this.checkName,
          },
        ],
      },
      loading: true,
    };
  },
  activated() {
    let data = {
      ...this.searchParams,
      current: this.pagination.current,
      size: this.pagination.size,
    };
    this.getTableList(data);
  },
  //  跳转非详情   清除 keep-alive 缓存数组中的缓存视图
  beforeRouteLeave(to, from, next) {
    if (to.path != "/groupDetail") {
      // 取消缓存
      this.$clearKeepAlive(this, from.path);
    }
    next();
  },
  methods: {
    /**
     * 获取表格数据
     */
    getTableList(params) {
      getDeviceGroundList(params)
        .then((res) => {
          console.log(res);
          if (200 == res.code) {
            const { data } = res;
            this.tableData = data.records;
            this.pagination.total = data.total;
            this.pagination.current = data.current;
            this.pagination.pages = data.pages;
            this.pagination.size = data.size;
            // 添加序号并自动递增
            this.tableData.forEach((item, index) => {
              item.index = index + 1;
            });

            this.groupNames = [
              ...new Set(this.tableData.map((item) => item.name)),
            ]; // 使用 Set 对 groupNames 数组进行去重
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    /**
     * 下拉框选择事件
     * @param value 下拉框选中的值
     */
    handleChange(value) {
      let params = {
        size: this.pagination.size,
        current: 1,
        name: value,
      };
      this.getTableList(params);
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.size = val;
      let params = {
        size: this.pagination.size,
        current: 1,
        ...this.searchParams,
      };
      this.getTableList(params);
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        ...this.searchParams,
      };
      this.getTableList(params);
    },
    /**
     * 输入框触发事件
     * @param value 输入的值
     */
    handleInput(value) {
      this.searchParams.name = value;
    },
    //搜索
    search() {
      let params = {
        current: 1,
        size: this.pagination.size,
        ...this.searchParams,
      };
      this.getTableList(params);
    },
    //重置
    reset() {
      this.getTableList();
      this.selectedGroupName = "";
      this.searchParams.name = "";
    },
    // 清空表单
    fn_clear() {
      this.produceForm.id = null;
      this.produceForm.name = "";
      this.produceForm.introduce = "";
    },
    // 打开弹出框
    fn_open() {
      this.type = 1;
      this.title = "新增分组";
      this.dialogWidth = "718px";
      this.visible = true;
      this.fn_clear();
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
    },
    // 分组名称校验
    checkName(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入设备分组名称！"));
      } else if (!reg_twentyThree(value)) {
        return callback(
          new Error(
            "1-64位字符、支持中文、英文、数字及特殊符号，必须中文或者英文字母开头"
          )
        );
      } else {
        callback();
      }
    },
    //效验输入框是否为空
    fn_notNull(val) {
      return val !== 0 && !val;
    },
    //新增设备分组
    fn_get_add_Device_Group(data) {
      let loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
        target: document.querySelector(".iot-dialog-content"),
      });
      addDeviceGroup(data)
        .then((res) => {
          // console.log(res);
          if (res.code == 200) {
            this.getTableList();
            this.visible = false;
            this.fn_clear();
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    //编辑设备分组
    fn_get_edit_Device_Group(data) {
      let loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
        target: document.querySelector(".iot-dialog-content"),
      });
      editDeviceGroup(data)
        .then((res) => {
          if (res.code == 200) {
            this.getTableList();
            this.visible = false;
            this.produceForm.id = null;
            this.fn_clear();
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            loading.close();
          });
        });
    },

    //删除当前行
    deviceGroupDelete(row) {
      this.type = 3;
      this.visible = true;
      this.title = "删除设备";
      this.produceForm.id = row.id;
    },
    //编辑当前行
    deviceGroupEdit(row) {
      const { name, introduce, id } = row;
      this.visible = true;
      this.title = "编辑分组";
      this.type = 2;
      Object.assign(this.produceForm, { name, introduce, id });
    },
    // 弹出框确认按钮
    fn_sure() {
      if (this.type == 1) {
        //当type类型为1时调用新增设备分组方法
        this.$refs["produceForm"].validate((valid) => {
          if (valid) {
            this.fn_get_add_Device_Group(this.produceForm);
          } else {
            return false;
          }
        });
      } else if (this.type == 2) {
        //当type类型为2时调用编辑设备分组方法
        this.$refs["produceForm"].validate((valid) => {
          if (valid) {
            this.fn_get_edit_Device_Group(this.produceForm);
          } else {
            return false;
          }
        });
      } else if (this.type == 3) {
        //当type类型为3时调用删除设备分组方法
        deleteDeviceGroup(this.produceForm).then((res) => {
          if (res.code == 200) {
            this.getTableList();
            this.visible = false;
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        });
      }
    },
    // 跳转详情
    deviceGropuDetail(row) {
      console.log(row);
      this.$router.push({
        path: "/groupDetail",
        query: {
          ...row
        },
      });
    },
    //取消按钮
    fn_close() {},
  },
  mounted() {
    this.getTableList();
  },
};
</script>

<style lang="scss" scoped>
#main {
  width: 100%;
  background-color: rgba(255, 255, 255, 1);

  .product_bg {
    width: 100%;
    height: 136px;
    background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px 0px;
    background-size: 100%;
    padding: 35px 0px 0px 32px;
    .bg_title {
      font-weight: 500;
      font-size: 22px;
      line-height: 30px;
      color: #333333;
    }
    .bg_desc {
      font-weight: 400;
      font-size: 16px;
      line-height: 30px;
      color: #77797c;
    }
  }

  .deviceGroup-nav {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: end;
    padding: 12px 0px 12px 0px;
      .el-select {
        width: 181px;
        border-radius: 0px;
      }
      .el-input {
            width: 181px !important;
          }
  }
  .deviceGroup {
    width: 100%;
    background-color: rgba(255, 255, 255, 1);
    margin-top: 13px;
    padding: 0px 32px 0 32px;
    .deviceGroup-table {
      .table-edit {
        display: flex;
        align-items: center;
        p {
          cursor: pointer;
        }
        .table-line {
          margin: 0px 12px;
          width: 1px;
          height: 13px;
          border: 1px solid #ededed;
        }
      }
    }
    .produce-bottom {
      text-align: right;
      margin-top: 14px;
      padding-bottom: 10px;
    }
    .produceForm {
      :deep(.el-form-item) {
        margin-bottom: 17px;
      }
      .el-form-tips {
        margin-top: -17px;
      }
    }
  }
}
</style>