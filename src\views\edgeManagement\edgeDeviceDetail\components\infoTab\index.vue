<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:49:46
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2023-05-20 10:34:52
-->
<template>
  <div class="info">
    <div class="info-content">
      <div class="info-title">
        <div>设备信息</div>
        <div class="info-edit color2"
             @click="fn_open('edit')">
          <img src="~@/assets/images/index/edit.svg" />
          <span>编辑</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>产品名称</span>
          <span>{{ deviceForm.productName || "-" }}</span>
        </div>
        <div class="item">
          <span>Productkey</span>
          <span>{{ deviceForm.productKey || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>DeviceSecret</span>
          <span>{{ deviceForm.deviceSecret || "-" }}</span>
        </div>
        <div class="item">
          <span>DeviceName</span>
          <span>{{ deviceForm.deviceName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>IP地址</span>
          <span>{{ deviceForm.ipAddress || "-" }}</span>
        </div>
        <div class="item">
          <span>备注名称</span>
          <span>{{ deviceForm.aliasName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>节点类型</span>
          <span>{{ deviceForm.nodeType || "-" }}</span>
        </div>
        <div class="item">
          <span>通讯方式</span>
          <span>{{ deviceForm.networkWay || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>当前状态</span>
          <span>{{ deviceForm.status | statusText }}</span>
        </div>
        <div class="item">
          <span>激活时间</span>
          <span>{{ deviceForm.activeTime || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>创建时间</span>
          <span>{{ deviceForm.createTime || "-" }}</span>
        </div>
        <div class="item">
          <span>最后上线时间</span>
          <span>{{ deviceForm.lastReportTime || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>设备描述</span>
          <span>{{ deviceForm.description || "-" }}</span>
        </div>
      </div>
    </div>
    <div class="info-content">
      <div class="info-title">
        <div>标签信息</div>
        <div class="info-edit color2"
             @click="fn_open('popupLabel')">
          <img src="~@/assets/images/index/edit.svg" />
          <span>编辑</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="tag">
          <span>标签信息</span>
          <span class="spans"
                v-if="tagsForm.length">
            <span v-for="item in tagsForm"
                  :key="item.tagKey">{{`${item.tagName }:${item.tagKey }:${item.tagValue }` }}</span>
          </span>
          <span v-else
                class="spans">
            <span>暂无标签</span>
          </span>
        </div>
      </div>
    </div>
    <!-- <div class="info-title">
      <div>产品配置</div>
      <div class="color2"></div>
    </div>
    <div class="info-config">
      <p>是否禁止</p>
      <p>
        <el-switch
          v-throttle="500"
          v-model="deviceDisableStatus"
          @change="fn_isdisable"
          active-color="#13ce66"
          inactive-color="#efefef"
        ></el-switch>
        <span class="config-text">{{deviceForm.deviceDisableStatus ? '已开启': '已关闭'}}</span>
      </p>
      <p></p>
      <p>动态注册</p>
      <p>
        <el-switch
          v-throttle="500"
          v-model="dynamicRegisterAllowed"
          @change="fn_isregister"
          active-color="#13ce66"
          inactive-color="#efefef"
        ></el-switch>
        <span class="config-text">{{deviceForm.dynamicRegisterAllowed ? '已开启': '已关闭'}}</span>
      </p>
      <p></p>
      <p>自动注册</p>
      <p>
        <el-switch
          v-throttle="500"
          v-model="autoRegisterAllowed"
          :disabled="!dynamicRegisterAllowed"
          @change="fn_isauto"
          active-color="#13ce66"
          inactive-color="#efefef"
        ></el-switch>
        <span class="config-text">{{deviceForm.autoRegisterAllowed ? '已开启': '已关闭'}}</span>
      </p>
    </div> -->
    <edit-dialog ref="edit"
                 @reload="fn_get_device_detail" />

    <tag-editDialog ref="popupLabel"
                    @reload="fn_get_device_detail" />
  </div>
</template>

<script>
import { getDeviceInfo, getDeviceTags } from '@/api/device'
import editDialog from './components/edit'
import tagEditDialog from './components/tagEdit'
export default {
  name: 'deviceInfo',
  props: {
    id: {
      type: String,
      default: '',
    },
    form: {
      type: Object,
    },
    tags: {
      type: Array,
    },
  },
  data() {
    return {
      deviceForm: {
        activeTime: '', //激活时间
        aliasName: '', //设备备注名称
        createTime: '', //创建时间
        createType: 0, // 创建类型 1 预创建 2 自动创建
        description: '', //设备描述
        deviceName: '', //设备名称
        id: 0, //设备ID
        ipAddress: '', //ip地址
        lastReportTime: '', //最后上报时间
        networkWay: '', //	通讯方式
        nodeType: '', //节点类型
        productId: 0, //产品ID
        productKey: '', //产品key
        productName: '', //	产品名称
        // status: 0, //设备状态 4 未激活 5 在线 6 离线
      },
      tagsForm: [],
    }
  },
  watch: {
    form: {
      deep: true,
      handler: function () {
        this.deviceForm = this.form
      },
    },
    tags: {
      deep: true,
      handler: function () {
        this.tagsForm = this.tags
      },
    },
  },
  components: { editDialog, tagEditDialog },
  filters: {
    statusText(val) {
      return ['未激活', '在线', '离线'][+val - 4]
    },
  },
  created() {
    // this.fn_get_device_detail();
  },
  methods: {
    fn_open(key) {
      this.$refs[key].open(this.deviceForm)
    },
    // 设备详细信息
    fn_get_device_detail() {
      let params = {
        id: this.id,
      }
      getDeviceInfo(params).then((res) => {
        if (res.code == 200) {
          this.deviceForm = res.data
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })

      let paramsTags = {
        deviceId: this.id,
      }
      getDeviceTags(paramsTags).then((res) => {
        if (res.code == 200) {
          this.tagsForm = res.data
        }
      })
    },
  },
}
</script>

<style lang="scss" scoped>
.info {
  .info-title {
    display: flex;
    justify-content: space-between;
    padding-bottom: 10px;
    .info-edit {
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: H_Regular;
      img {
        width: 12px;
        height: 12px;
        margin-right: 6px;
      }
    }
  }
  .info-content {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 20px 32px 10px;
    margin-top: 18px;
    .info-row {
      .item {
        padding: 10px 0;
        flex: 1;
        display: inline-flex;
        span {
          height: 16px;
          line-height: 16px;
          font-size: 14px;
          font-weight: 400;
          &:first-child {
            margin-left: 48px;
            flex-shrink: 0;
            color: #999999;
            width: 90px;
            text-align: right;
            // margin-left: 26px;
          }
          &:last-child {
            width: 100%;
            color: #515151;
            margin-left: 48px;
          }
        }
      }

      .tag {
        height: 300px;
        padding: 10px 0;
        flex: 1;
        display: inline-flex;
        span {
          height: 32px;
          line-height: 32px;
          font-size: 14px;
          font-weight: 400;
          &:first-child {
            margin-left: 48px;
            flex-shrink: 0;
            color: #999999;
            width: 90px;
            text-align: right;
            // margin-left: 26px;
          }
        }
        .spans {
          height: 280px;
          span {
            height: 32px;
            line-height: 32px;
            margin-left: 8px;
            padding: 0 10px 0 10px;
            font-size: 14px;
            font-weight: 400;
            border-radius: 50px;
            color: #515151;
            background: #edf1f7;
            &:first-child {
              margin-left: 48px;
              flex-shrink: 0;
              color: #515151;
              width: 90px;
              text-align: right;
              // margin-left: 26px;
            }
          }
        }
      }
    }
  }
  .info-config {
    display: flex;
    align-items: center;
    border-top: 1px solid #e4e7ec;
    border-bottom: 1px solid #e4e7ec;
    min-height: 55px;
    p:nth-child(1) {
      margin-left: 30px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
      font-weight: 400;
      color: #999999;
    }
    p:nth-child(2) {
      margin-left: 102px;
      display: flex;
      align-items: center;
    }
    p:nth-child(3) {
      width: 1px;
      height: 24px;
      border: 1px solid #efefef;
      margin-left: 230px;
    }
    p:nth-child(4) {
      margin-left: 31px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
      font-weight: 400;
      color: #999999;
    }
    p:nth-child(5) {
      margin-left: 102px;
      display: flex;
      align-items: center;
    }

    p:nth-child(6) {
      width: 1px;
      height: 24px;
      border: 1px solid #efefef;
      margin-left: 230px;
    }

    p:nth-child(7) {
      margin-left: 31px;
      font-size: 14px;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
      font-weight: 400;
      color: #999999;
    }

    p:nth-child(8) {
      margin-left: 102px;
      display: flex;
      align-items: center;
    }
    .config-text {
      font-size: 14px;
      margin-left: 6px;
      color: #515151;
      font-weight: normal;
    }
  }
  .deviceForm {
    .el-form-tips {
      margin-top: -17px;
      margin-bottom: 0;
    }
  }
  /deep/.el-form-item {
    margin-bottom: 17px !important;
  }
}
</style>
