<template>
  <div class="access-key">
    <!-- 创建密钥对按钮 -->
    <div class="key-top">
      <iot-button text="创建密钥对"
                  @search="handleCreateKey"></iot-button>
    </div>
    <div class="key-content">
      <!-- 密钥对列表 -->
      <iot-table :columns="columns"
                 :data="tableData"
                 :loading="loading">
        <!-- 状态列 -->
        <template slot="status"
                  slot-scope="{ row }">
          <div class="table-status">
            <div class="status flex"
                 v-if="row.status == 0">
              <div class="red"></div>
              <div>已禁用</div>
            </div>
            <div class="status flex"
                 v-if="row.status != 0">
              <div class="green"></div>
              <div>已启动</div>
            </div>
          </div>
        </template>
        <!-- 密钥列 -->
        <template slot="accessKeySecret"
                  slot-scope="scope">
          <p v-if="accessKeyShow.includes(scope.row.id)">
            {{ scope.row.accessKeySecret }}
          </p>
          <p v-else>
            <span>******************************</span>
          </p>
        </template>
        <!-- 操作列 -->
        <template slot="operation"
                  slot-scope="scope">
          <div class="table-edit flex">
            <p class="color2"
               @click="handleViewSecret(scope.row)">
              查看Secret
            </p>
            <p class="table-line"></p>
            <p v-if="scope.row.status == 1"
               class="color2"
               v-throttle
               @click="handleDisable(scope.row)">
              禁用
            </p>
            <p v-if="scope.row.status != 1"
               class="color2"
               v-throttle
               @click="handleAble(scope.row)">
              启用
            </p>
            <p class="table-line"></p>
            <p class="color2"
               v-throttle
               @click="handleDelete(scope.row)">
              删除
            </p>
          </div>
        </template>
      </iot-table>
    </div>
    <iot-dialog :visible.sync="visible"
                :title="title"
                :width="dialogWidth"
                @callbackSure="fn_sure">
      <template #body>
        <div>
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除该密钥对后，将无法通过MQTT方式订阅获取设备实时数据，
                    请确认是否删除？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>
<script>
import {
  getAccessKeyList,
  getAccessKeyAdd,
  getAccessKeyDisable,
  getAccessKeyEnable,
  getAccessKeyRemove,
} from '@/api/accesskey'

import IotButton from '@/components/iot-button'
import IotTable from '@/components/iot-table'
import IotDialog from '@/components/iot-dialog'
import IotForm from '@/components/iot-form'
export default {
  name: 'AccessKey',
  components: {
    IotButton,
    IotTable,
    IotDialog,
    IotForm, 
  },
  data() {
    return {
      columns: [
        { label: 'AccessKeyID', prop: 'accessKeyId' },
        { label: '状态', prop: 'status', slotName: 'status' },
        {
          label: '密钥',
          prop: 'accessKeySecret',
          slotName: 'accessKeySecret',
        },
        {
          label: '备注',
          prop: 'remark',
        },
        { label: '创建时间', prop: 'createTime' },
        { label: '操作', prop: 'operation', slotName: 'operation' },
      ],
      tableData: [],// 表格数据
      loading: false,// 表格数据
      visible: false,// 删除对话框可见性
      title: '确定删除密钥对？',// 删除对话框标题
      dialogWidth: '550px',// 删除对话框宽度
      delIds: '',// 待删除的密钥对ID
      accessKeyShow: [],// 需要显示密钥的ID列表
    }
  },
  created() {// 页面创建时调用接口获取密钥对列表
    this.httpAccessKeyList()
  },
  methods: {
    // 创建密钥
    handleCreateKey() {
      this.httpAccessKeyAdd()
    },
    // 查看Secret
    handleViewSecret(row) {
      const index = this.accessKeyShow.findIndex((item) => item == row.id)
      if (index > -1) {
        this.accessKeyShow.splice(index, 1)
      } else {
        this.accessKeyShow.push(row.id)
      }
    },
    // 禁用密钥
    handleDisable(row) {
      const data = {
        id: row.id,
      }
      getAccessKeyDisable(data).then((res) => {
        if (res.code == 200) {
          // console.log(res)
          this.$newNotify.success({
            message: res.message,
          })
          this.httpAccessKeyList()
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })
    },
    // 启用密钥
    handleAble(row) {
      const data = {
        id: row.id,
      }
      getAccessKeyEnable(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          })
          this.httpAccessKeyList()
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })
    },
    // 删除密钥
    handleDelete(row) {
      this.delIds = row.id
      this.visible = true
    },
    // 列表
    httpAccessKeyList() {
      getAccessKeyList().then((res) => {
        if (res.code == 200) {
          this.tableData = res.data
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })
    },
    // 新增
    httpAccessKeyAdd() {
      getAccessKeyAdd().then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          })
          this.httpAccessKeyList()
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })
    },
    // 删除
    httpAccessKeyRemove() {
      const data = {
        id: this.delIds,
      }
      getAccessKeyRemove(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          })
          this.httpAccessKeyList()
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })
    },
    // 删除弹窗确定按钮
    fn_sure() {
      this.httpAccessKeyRemove()
      this.visible = false
    },
  },
}
</script>
<style lang="scss" scoped>
.access-key {
  padding: 0px 32px 0px 32px;
  .key-top {
    margin-top: 18px;
  }
  .key-content {
    margin-top: 18px;
    .table-status {
      .status {
        .green {
          background: #00c250;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 7px;
          margin-right: 6px;
        }
        .red {
          background: #ff4d4f;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 7px;
          margin-right: 6px;
        }
      }
    }
    .table-edit {
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
}
</style>
