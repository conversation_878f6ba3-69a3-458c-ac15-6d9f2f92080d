<template>
  <el-form-item
    v-if="attr.data.type == 'int'"
    :prop="`${prop}.value`"
    :rules="[{ validator: intValidate, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-input
          v-model="attr.value"
          placeholder="请输入参数(int)"
          @input="onInput()"
        ></el-input>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'float'"
    :prop="`${prop}.value`"
    :rules="[{ validator: floatValidate, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-input
          v-model="attr.value"
          placeholder="请输入参数(float)"
          @input="onInput()"
        ></el-input>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'double'"
    :prop="`${prop}.value`"
    :rules="[{ validator: doubleValidate, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-input
          v-model="attr.value"
          placeholder="请输入参数(double)"
          @input="onInput()"
        ></el-input>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'enum'"
    :prop="`${prop}.value`"
    :rules="[{ validator: compareValue, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-select
          v-model="attr.value"
          placeholder="请输入参数(enum)"
          @input="onInput()"
        >
          <el-option
            v-for="item in attr.data.enumList"
            :key="item.key"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'text'"
    :prop="`${prop}.value`"
    :rules="[{ validator: textValidate, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-input
          v-model="attr.value"
          placeholder="请输入参数(text)"
          @input="onInput()"
        ></el-input>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'bool'"
    :prop="`${prop}.value`"
    :rules="[{ validator: compareValue, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-select
          v-model="attr.value"
          placeholder="请输入参数(bool)"
          @input="onInput()"
        >
          <el-option
            v-for="item in attr.data.boolList"
            :key="item.key"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'time'"
    :prop="`${prop}.value`"
    :rules="[{ validator: compareValue, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-date-picker
          v-model="attr.value"
          type="datetime"
          value-format="yyyy-MM-dd HH:mm:ss"
          placeholder="请选择日期和时间"
          prefix-icon="el-icon-date"
          @input="onInput()"
        >
        </el-date-picker>
      </div>
    </div>
  </el-form-item>
  <el-form-item
    v-else-if="attr.data.type == 'array'"
    :prop="`${prop}.value`"
    :rules="[{ validator: arrayValidate, trigger: ['blur', 'change'] }]"
  >
    <div class="form-item flex">
      <div class="form-item-inp">
        <el-input
          v-model="attr.value"
          placeholder='输入数据须符合数组JSON格式,如[1,2], ["i","j"], [{"key":"value"}]'
          @input="onInput()"
        ></el-input>
      </div>
    </div>
  </el-form-item>
  <!-- <div class="object" v-else-if="attr.data.type == 'object'">
    <h4>{{ attr.name }}/{{ attr.id }}</h4>
    <div class="object-content">
      <online-custom-item
        v-for="(item, index) in attr.data.rules"
        :key="item.id"
        :index="index"
        :attr="item"
        :prop="`${prop}.data.rules.${index}`"
      />
    </div>
  </div> -->
</template>
<script>
import { checkDefault, arrayValidate } from "../ruleEdit/rules";
export default {
  data() {
    return {};
  },
  props: {
    attr: {
      type: Object,
    },
    prop: {
      type: String,
    },
    index: {
      type: [Number, String],
    },
  },
  methods: {
    onInput() {
      this.$forceUpdate();
    },
    intValidate(rule, value, callback) {
      let newValue = this.attr.value;
      if (newValue !== undefined && newValue !== "") {
        let rules = this.attr.data.rules;
        let result = checkDefault("int", newValue, rules);
        if (result.flag) {
          callback(result.text);
        } else {
          callback();
        }
      } else {
        callback("请输入值");
      }
    },
    floatValidate(rule, value, callback) {
      let newValue = this.attr.value;
      if (newValue !== undefined && newValue !== "") {
        let rules = this.attr.data.rules;
        let result = checkDefault("float", newValue, rules);
        if (result.flag) {
          callback(result.text);
        } else {
          callback();
        }
      } else {
        callback("请输入值");
      }
    },
    doubleValidate(rule, value, callback) {
      let newValue = this.attr.value;
      if (newValue !== undefined && newValue !== "") {
        let rules = this.attr.data.rules;
        let result = checkDefault("double", newValue, rules);
        if (result.flag) {
          callback(result.text);
        } else {
          callback();
        }
      } else {
        callback("请输入值");
      }
    },
    textValidate(rule, value, callback) {
      let newValue = this.attr.value;
      if (newValue !== undefined && newValue !== "") {
        let rules = this.attr.data.rules;
        let maxLength = rules.length || 1000;
        let result = checkDefault("text", newValue, rules);
        if (result.flag) {
          callback(`字符串的长度不能超过${maxLength}`);
        } else {
          callback();
        }
      } else {
        callback("请输入字符串");
      }
    },
    compareValue(rule, value, callback) {
      let newValue = this.attr.value;
      //   类型  值 规则   是否验证10位时间
      if (newValue !== undefined && newValue !== "") {
        let type = this.attr.type;
        let rules = this.attr.data.rules;
        let result = checkDefault(type, newValue, rules);
        if (result.flag) {
          callback(result.text);
        } else {
          callback();
        }
      } else {
        callback("请选择值");
      }
    },
    arrayValidate(rule, value, callback) {
      let newValue = this.attr.value;
      if (newValue !== undefined && newValue !== "") {
        let rules = this.attr.data.rules;
        arrayValidate(
          newValue,
          rules,
          callback,
          "输入格式有误，请输入正确的json格式"
        );
      } else {
        callback("请选择值");
      }
    },
  },
};
</script>

<style lang="scss" scoped></style>
