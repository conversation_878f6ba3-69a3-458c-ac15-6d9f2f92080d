/*
 * @Author: your name
 * @Date: 2021-12-29 11:31:19
 * @LastEditTime: 2022-04-20 11:13:06
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \tenant-web\src\api\firmwareUpdate.js
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;
const manage = baseServer;

/**
 *  分片上传接口
 * @param {*} params
 * @returns
 */
export const partUpload = `${manage}/ota/upload/file/uploadPart`;
/**
 *  分片合并接口
 * @param {*} params
 * @returns
 */
export const partMerge = `${manage}/ota/upload/multipart/merge`;

/**
 * @desc 固件列表
 * @returns
 */
export const getFirmwareList = (params) => {
  return request({
    url: `${manage}/ota/firmware/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 获取固件升级相关下拉框
 * @returns
 */
export const getPullDownList = (params) => {
  return request({
    url: `${manage}/ota/firmware/job/pullDownList`,
    method: "get",
    params,
  });
};

/**
 * @desc 添加固件
 * @returns
 */
export const postFirmwareAdd = (data) => {
  return request({
    url: `${manage}/ota/firmware/add`,
    method: "post",
    data,
  });
};

/**
 * @desc 上传固件升级包
 * @returns
 */
export const postFirmwareUpload = (data) => {
  return request({
    url: `${manage}/ota/firmware/upload`,
    method: "post",
    data,
  });
};

/**
 * @desc 添加固件升级任务
 * @returns
 */
export const postFirmwareJobAdd = (data) => {
  return request({
    url: `${manage}/ota/firmware/job/add`,
    method: "post",
    data,
  });
};

/**
 * @desc 删除固件
 * @returns
 */
export const deleteFirmware = (params) => {
  return request({
    url: `${manage}/ota/firmware/delete/${params}`,
    method: "delete",
  });
};

/**
 * @desc 产品下的设备的版本号下拉列表
 * @returns
 */
export const getVersionByProduct = (params) => {
  return request({
    url: `${manage}/ota/device/version/getVersionByProduct/${params}`,
    method: "get",
  });
};

/**
 * @desc 查看固任务详情
 * @returns
 */
export const getFirmwareDetail = (params) => {
  return request({
    url: `${manage}/ota/firmware/detail/${params}`,
    method: "get",
  });
};

/**
 * @desc 固件任务升级统计
 * @returns
 */
export const getFirmwareJobUpgradeStatistic = (params) => {
  return request({
    url: `${manage}/ota/firmware/job/upgradeStatistic/${params}`,
    method: "get",
  });
};

/**
 * @desc 固件任务分页查询
 * @returns
 */
export const getFirmwareJobList = (params) => {
  return request({
    url: `${manage}/ota/firmware/job/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 任务取消
 * @returns
 */
export const putFirmwareJobCancel = (params) => {
  return request({
    url: `${manage}/ota/firmware/job/cancel/${params}`,
    method: "put",
  });
};

/**
 * @desc 编辑固件
 * @returns
 */
export const putFirmwareUpdate = (data) => {
  return request({
    url: `${manage}/ota/firmware/update`,
    method: "put",
    data,
  });
};

/**
 * @desc 查看任务详情
 * @returns
 */
export const getFirmwareJobDetail = (params) => {
  return request({
    url: `${manage}/ota/firmware/job/detail/${params}`,
    method: "get",
  });
};

/**
 * @desc 任务设备升级统计
 * @returns
 */
export const getUpgradeStatistic = (params, param1) => {
  return request({
    url: `${manage}/ota/job/device/upgradeStatistic/${params}/${param1}`,
    method: "get",
  });
};

/**
 * @desc 设备升级任务分页查询
 * @returns
 */
export const getJobDeviceList = (params) => {
  return request({
    url: `${manage}/ota/job/device/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 设备升级取消
 * @returns
 */
export const putJobDeviceCancel = (params) => {
  return request({
    url: `${manage}/ota/job/device/cancel/${params}`,
    method: "put",
  });
};

/**
 * @desc 静态升级指定设备列表查询
 * @returns
 */
export const getDeviceByProduct = (params) => {
  return request({
    url: `${manage}/ota/device/version/deviceByProduct`,
    method: "get",
    params,
  });
};
