/*
 * @Author: your name
 * @Date: 2021-11-03 13:40:35
 * @LastEditTime: 2023-04-19 15:41:11
 * @LastEditors: lb <EMAIL>
 * @Description: In User Settings Edit
 * @FilePath: \tenant-web\src\conf\env.js
 */
let baseUrl = process.env.VUE_APP_BASE_URL;
const env = process.env;
const BASE_SERVER = "/core"; //  /xt-standalone
const other = "/core"
let HOST = process.env.HOST;
console.log("HOST",HOST)
// 默认 分布式服务
console.log(env);
let text = process.env.NODE_ENV
// if (env.NODE_ENV === "development") {
//     baseUrl = `/api`; // 开发环境地址
//     text = "开发环境"
// } else if (env.NODE_ENV === "production") {
//     //   baseUrl = "http://*************/api";
//     // 服务开发环境
//     // baseUrl = "http://*************/api";
//     // baseUrl = "http://**************:9000/api"//预发布环境
//     // 测试环境
//     baseUrl = `http://**************:9000/api`; //测试环境地址

//     //   baseUrl = "http://*************/api";
//     //   //生产环境地址
//     //   baseUrl = "https://iot.console.fj-yuchen.com/api";
//     //   text = "生产环境"
// } else if (env.NODE_ENV === "test") {
//     console.log("测试环境");
//     // baseUrl = `http://*************/api`; //测试环境地址
//     baseUrl = `http://**************:9000/api`; //测试环境地址
//     text="测试环境"
// }
console.log("text",text);
export { baseUrl, env, BASE_SERVER, other };
