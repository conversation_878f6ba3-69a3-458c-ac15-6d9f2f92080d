{"name": "tenant", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "test": "vue-cli-service build --mode test", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.24.0", "bin-code-editor": "^0.9.0", "browser-md5-file": "^1.1.1", "core-js": "^3.6.5", "cross-env": "^7.0.3", "crypto-js": "^4.1.1", "echarts": "^5.4.3", "element-ui": "^2.15.6", "js-md5": "^0.7.3", "jsencrypt": "^3.2.1", "lodash": "^4.17.21", "postcss": "~6.0.0", "sockjs-client": "^1.5.0", "stompjs": "^2.3.3", "vue": "^2.6.11", "vue-router": "^3.5.3", "vuex": "^3.6.2"}, "devDependencies": {"@types/echarts": "^4.9.18", "@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "node-sass": "^4.12.0", "postcss-px-to-viewport": "^1.1.1", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.6.11", "webpack-bundle-analyzer": "^4.10.1"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {"no-debugger": "off", "no-console": "off", "no-unused-vars": "off"}}, "browserslist": ["> 1%", "last 2 versions", "not dead"]}