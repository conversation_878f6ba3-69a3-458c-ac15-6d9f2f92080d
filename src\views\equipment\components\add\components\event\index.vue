<template>
  <div>
    <el-form-item>
      <div class="form-item">
        <p class="form-item-label">事件类型</p>
        <div class="event flex">
          <p
            class="flex"
            v-for="(item, index) in typeList"
            :key="index"
            :class="active == index ? 'active' : ''"
            @click="changeType(index)"
          >
            <img :src="item.img" alt="" />
            <span>{{ item.name }}</span>
          </p>
        </div>
      </div>
    </el-form-item>
    <el-form-item>
      <json-struct
        title="输出参数"
        :data="list"
        key="eventStruct"
        @addAttribute="(data) => addAttribute(data, 'out')"
        @editAttribute="(data) => editAttribute(data, 'out')"
        @deleteAttribute="(data) => deleteAttribute(data, 'out')"
      />
    </el-form-item>
    <div class="el-form-tips" v-if="outTips">{{ outTips }}</div>
  </div>
</template>

<script>
import jsonStruct from "../json-struct";
export default {
  name: "form-event",
  components: { jsonStruct },
  data() {
    return {
      active: 0,
      typeValue: "info", //事件类型
      typeList: [
        {
          img: require("@/assets/images/product/event-info-icon.png"),
          name: "信息",
          value: "info",
        },
        {
          img: require("@/assets/images/product/event-alarm-icon.png"),
          name: "告警",
          value: "alarm",
        },
        {
          img: require("@/assets/images/product/event-error-icon.png"),
          name: "故障",
          value: "error",
        },
      ],
      list: [],
    };
  },
  props: {
    form: {
      type: Object,
    },
    outTips: {
      type: String,
    },
  },
  watch: {
    form: {
      deep: true,
      handler: "defaultSet",
    },
  },
  mounted() {
    this.defaultSet();
  },
  methods: {
    defaultSet() {
      if (this.form) {
        let index = this.typeList.findIndex(
          (item) => item.value == this.form.type
        );
        this.active = index;
        this.list = this.form.out || [];
      }
    },
    changeType(index) {
      this.active = index;
      this.typeValue = this.typeList[index].value;
      this.emitChange({
        key: "type",
        value: this.typeValue,
      });
    },
    emitChange(data) {
      this.$emit("change", data);
    },
    addAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("addAttribute", params);
    },
    editAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("editAttribute", params);
    },
    deleteAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("deleteAttribute", params);
    },
  },
};
</script>

<style lang="scss" scoped>
.event {
  align-items: center;
  p {
    width: 110px;
    height: 64px;
    align-items: center;
    justify-content: center;
    background: #eeeff1;
    margin-right: 12px;
    cursor: pointer;
    transition: all 0.3s;
    border: 1px solid transparent;
  }
  .active {
    border: 1px solid #0088fe;
    background: #ffffff;
    position: relative;
    span {
      color: #0088fe;
    }
  }
  .active::before {
    content: "";
    width: 18px;
    height: 18px;
    position: absolute;
    right: 0;
    bottom: 0;
    background: url("~@/assets/images/product/event-active-icon.png") no-repeat;
  }
}
.el-form-tips {
  font-size: 12px;
  font-weight: 400;
  color: #f56c6c;
  margin-top: -28px;
  margin-bottom: 12px;
}
</style>
