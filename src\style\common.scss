// 全局样式  顶级    慎用 ------------
body {
	overflow: hidden;
}
// 全局elementui  message组件
.el-message--success {
	background: rgba(223, 255, 236, 0.8);
	border: 1px solid rgba(0, 194, 80, 0.24);
	box-sizing: border-box;
	backdrop-filter: blur(8px);
}

.el-message--warning {
	background: rgba(255, 245, 231, 0.8);
	border: 1px solid rgba(230, 162, 60, 0.24);
	box-sizing: border-box;
	backdrop-filter: blur(8px);
}

.el-message--error {
	background: rgba(255, 237, 237, 0.8);
	border: 1px solid rgba(255, 77, 79, 0.24);
	box-sizing: border-box;
	backdrop-filter: blur(8px);
}

.el-message--info {
	background: rgba(228, 231, 236, 0.8);
	border: 1px solid rgba(144, 147, 153, 0.24);
	box-sizing: border-box;
	backdrop-filter: blur(8px);
}

// 下拉空选项的插槽样式
.empty-project {
	padding: 8px;
	text-align: center;
	letter-spacing: 1px;

	span {
		margin: 0 auto;
		font-size: 12px;
		font-family: H_Thin Sans SC, HarmonyOS Sans SC-Regular;
		font-weight: 400;
		color: #999999;
	}
}

.el-select-dropdown__item.selected {
	color: #018aff;
}

.el-popper {
	margin-top: 5px !important;
	border-radius: 0 !important;
}

.popper__arrow {
	display: none !important;
}

.el-notification {
	margin-top: 43px;
}

// loading样式
.el-loading-spinner .el-icon-loading {
	background: url('~@/assets/images/index/loading.svg') no-repeat;
	width: 40px;
	height: 40px;
	background-size: 38px 38px;
}

.el-icon-loading:before {
	content: '';
}

// 表头的样式修改
.el-table thead {
	color: rgba(81, 81, 81, 1) !important;
	font-weight: 400 !important;
	font-family: HarmonyOS Sans SC;
	font-size: 14px;
	letter-spacing: 1px;
}

// 面包屑样式
.el-breadcrumb__inner {
	color: rgba(136, 136, 136, 1);
}

.el-breadcrumb__inner a,
.el-breadcrumb__inner.is-link {
	font-weight: 400;
	color: #262626;
	font-size: 12px;
	font-family: H_Regular;
}

.el-breadcrumb__item:last-child .el-breadcrumb__inner,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a,
.el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover,
.el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
	color: rgba(136, 136, 136, 1);
	font-size: 12px;
	font-family: H_Regular;
}

// 全局 disabled 样式
.el-input.is-disabled .el-input__inner {
	background-color: #f2f2f2 !important;
}

// info信息框的样式
.el-notification.right {
	min-width: 350px;
	width: auto;
}

.el-notification__closeBtn {
	font-weight: 600;
	color: #515151;
	margin-top: -2px;
}

.el-notification__content {
	p {
		margin-right: 10px;
	}
}

// 文本框字体样式全局修改
.el-textarea__inner {
	font-family: H_Regular !important;
	letter-spacing: 1px;
}

// 横向导航菜单样式
.el-radio-button__inner {
	width: 106px;
	background: #ffffff;
	border-radius: 0px;
	padding: 9px 18px;
}

.el-radio-button__orig-radio:checked+.el-radio-button__inner {
	background: linear-gradient(180deg, #0088fe, #006eff 100%);
}

.el-radio-button:first-child .el-radio-button__inner {
	border-radius: 0px;
}

.el-radio-button:last-child .el-radio-button__inner {
	border-radius: 0px;
}

// 全局高度修改
.el-input__inner {
	height: 34px !important;
	line-height: 34px !important;
	font-family: H_Regular;
}

.el-input__icon {
	line-height: 34px !important;
}

// 事件 服务 共用 event-tooltip
.event-tooltip {
	max-width: 40vh;
	border-radius: 0;
	border: 1px solid #e4e7ec !important;
	background: #ffffff !important;
	backdrop-filter: blur(4px);
	padding: 14px 18px;
	box-shadow: 0px 4px 8px 0px rgba(150, 150, 150, 0.19);

	.popper__arrow {
		display: block !important;
		// border-top-color: red !important;
		border-top-color: #ffffff !important;
		bottom: -14px !important;
		left: 18px !important;
		border-width: 6px !important;
	}

	.popper__arrow::after {
		// bottom: -10px !important;
		margin-left: -10px !important;
		border-width: 10px;
	}

	.tips-password {
		p {
			align-items: center;
			padding-bottom: 8px;
			font-family: H_Medium;

			img {
				width: 14px;
			}

			span {
				padding-left: 8px;
				color: #515151;
				font-size: 13px;
			}
		}

		p:last-child {
			padding-bottom: 0;
		}
	}
}

// 告警中心-告警列表-待处理-专用宽度
.alarmContent-tooltip {
	max-width: 50vh !important;
}

// 告警中心-告警列表-待处理-专用宽度
.handleSuggestion-tooltip {
	max-width: 25vh !important;
}
.description-tooltip {
	max-width: 35vh !important;
}

// 下拉框箭头颜色
.el-select .el-input__suffix .el-select__caret {
	color: #666666;
}

// .el-icon-arrow-up {
// 	font-family: 'tenant' !important;
// 	font-size: 8px;
// 	color: #666666;
// }

// .el-icon-arrow-up::before {
// 	content: '\e651';
// }
.el-input__icon {
	color: #666666;
}