# tenant

## Project setup
```
npm install
```

### Compiles and hot-reloads for development
```
npm run serve
```

### Compiles and minifies for production
```
npm run build
```

### Lints and fixes files
```
npm run lint
```

### Customize configuration
See [Configuration Reference](https://cli.vuejs.org/config/).



docker run \
-p 9000:80 \
--restart=always \
--privileged=true \
--name nginx \
-v /opt/docker-nginx/conf/nginx.conf:/etc/nginx/nginx.conf \
-v /opt/docker-nginx/conf/conf.d:/etc/nginx/conf.d \
-v /opt/docker-nginx/log:/var/log/nginx \
-v /opt/docker-nginx/www/html:/usr/share/nginx/html \
-d nginx:latest