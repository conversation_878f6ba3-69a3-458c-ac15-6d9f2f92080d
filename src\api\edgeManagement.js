import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;

/**
 * @desc 边缘节点列表
 * @returns
 */
export const getEdgeNodeList = (params) => {
    return request({
      url: `${baseServer}/edgeNode/list`,
      method: "get",
      params,
    });
  };

  /**
   * 
   * @desc 新建节点
   * @returns 
   */
export const createEdgeNode = (data)=>{
    return request({
        url:`${baseServer}/edgeNode/create?name=${data.name}`,
        method:'post',
    })
}

/**
 * 
 * @desc 获取边缘设备列表 
 * @returns 
 */
export const getEdgeDeviceList = (params)=>{
    return request({
        url:`${baseServer}/edgeNode/device/${params.id}`,
        method:'get',
    })
}

/**
 * 
 * @desc 获取边缘设备安装脚本
 * @returns 
 */
export const getEdgeDeviceInstall = (params)=>{
    return request({
        url:`${baseServer}/edgeNode/install/${params.id}`,
        method:'get',
    })
}

/**
 * 
 * @desc 删除节点
 * @returns 
 */
export const deleteEdgeNode = (params)=>{
    return request({
        url:`${baseServer}/edgeNode/delete`,
        method:'delete',
        params
    })
}

/**
 * 
 * @desc 删除设备
 * @returns 
 */
export const deleteEdgeNodedevice = (params)=>{
    return request({
        url:`${baseServer}/edgeNode/device/delete`,
        method:'delete',
        params
    })
}

/**
 * 
 * @desc 节点详情
 * @returns 
 */
export const getNodeDetail = (params)=>{
    return request({
        url:`${baseServer}/edgeNode/detail/${params.id}`,
        method:'get'
        
    })
}

/**
 * @desc 设备详细信息
 * @params
 * @returns
 */
export const getDeviceInfo = (params) => {
    console.log("params",params);
    return request({
        url: `${baseServer}/tenant/device/info`,
        method: "get",
        params,
    });
};

/**
 * @desc 设备详细信息
 * @params
 * @returns
 */
export const createEdgeDevice = (data) => {
    return request({
        url: `${baseServer}/edgeNode/device/create`,
        method: "post",
        data,
    });
};