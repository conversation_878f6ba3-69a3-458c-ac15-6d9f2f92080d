<!--
 * @Description: 公共的搜索形式，后面可扩展
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 11:15:29
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-02-21 14:45:46
-->
<template>
  <el-row>
    <!-- 搜索栏 -->
    <el-form
      :model="queryForm"
      ref="queryForm"
      :inline="true"
      @submit.native.prevent
    >
      <template v-if="frontInsert.slotName">
        <el-form-item :prop="frontInsert.prop">
          <slot :name="frontInsert.slotName"></slot>
        </el-form-item>
      </template>
      <el-form-item prop="type" v-if="isSelect">
        <el-select v-model="queryForm.id" :placeholder="selectHolder" @change="op_change">
          <el-option
            v-for="item in options"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          ></el-option>
        </el-select>
      </el-form-item>

      <el-form-item prop="value">
        <el-input
          :maxlength="maxlength"
          v-model="queryForm.value"
          :placeholder="placeholder"
          clearable
          @keyup.enter.native="fn_handle__query"
          @clear="handleClear"
        >
          
        </el-input>
      </el-form-item>

      <el-form-item>
        <iot-button text="查询" @search="fn_handle__query" style="margin-right: 12px;"/>
      </el-form-item>

      <el-form-item>
        <slot name="other"></slot>
      </el-form-item>
    </el-form>
  </el-row>
</template>
<script>
import IotButton from '@/components/iot-button'
export default {
  name: "FormSearch",
  components: {
    IotButton
  },
  props: {
    options: {
      type: Array,
      default: () => [
        {
          id: "1",
          name: "全部",
        },
      ],
    },
    selectHolder: {
      type: String,
      default: "按项目名称",
    },

    isShowSelect: {},
    inputHolders: {
      type: Array,
      default() {
        return [];
      },
    },
    inputHolder: {
      type: String,
      default: "请输入名称搜索",
    },
    // 前插槽定义
    frontInsert: {
      type: Object,
      default() {
        return {
          prop: "",
          slotName: "",
        };
      },
    },
    isSelect: {
      type: Boolean,
      default: true,
    },
    oneMaxLength: {
      //最大长度
      type: Number,
      default: 32,
    },
    twoMaxLength: {
      //最大长度
      type: Number,
      default: 16,
    },
    defaultId: {
      type: [Number, String],
      default: "",
    },
  },
  data() {
    return {
      queryForm: {
        value: "",
        id: "",
      },
      maxlength: 32,
      placeholder:
        this.inputHolders.length !== 0
          ? this.inputHolders[0]
          : this.inputHolder,
    };
  },
  watch: {
    queryForm: {
      deep: true,
      handler: function () {
        if (this.queryForm.id == "1") {
          this.maxlength = 32;
          this.placeholder = this.inputHolders[0] || this.inputHolder;
        } else if (this.queryForm.id == "2") {
          this.maxlength = 16;
          this.placeholder = this.inputHolders[1] || this.inputHolder;
        } else {
          this.maxlength = 32;
        }
      },
    },
    "queryForm.value"(val) {
      if (this.queryForm.id == "1") {
        let { flag, newStr } = this.getLength(val, this.oneMaxLength);
        if (flag) {
          this.queryForm.value = newStr;
        }
      } else if (this.queryForm.id == "2") {
        let { flag, newStr } = this.getLength(val, this.twoMaxLength);
        if (flag) {
          this.queryForm.value = newStr;
        }
      }
    },
  },
  created() {
    if (this.defaultId) this.queryForm.id = this.defaultId;
  },
  methods: {
    //限制文本框输入
    getLength(str, length) {
      let a = 0,
        flag,
        newStr = "",
        content = str;
      for (let i = 0; i < content.length; i++) {
        if (content[i].charCodeAt() > 0 && content[i].charCodeAt() < 255) {
          a += 1;
        } else {
          a += 2;
        }
        if (a <= length) {
          newStr += content[i].charAt();
        }
      }
      flag = a > length;
      return { flag, newStr }; //flag 是否超出 //newStr 新字符串
    },
    // 搜索数据
    fn_handle__query() {
      this.$emit("search", this.queryForm);
    },
    handleClear() {
      this.$emit("clear",this.queryForm);
    },
    op_change(){
      this.$emit("clear",this.queryForm);
    }
  },
};
</script>
<style lang="scss" scoped>
:deep(.el-form-item) {
  margin-bottom: 0px;
  // margin-right: 12px;
  margin: 0px;
  .el-form-item__content {
    line-height: normal;
    .el-input__inner {
      height: 34px;
      line-height: 34px;
    }
  }
}
:deep(.el-select) {
  width: 188px;
  height: 34px;
  margin-right: 12px;
}
:deep(.el-input) {
  width: 188px !important;
  height: 34px;
  margin-right: 12px;
  border-radius: 0;
}
:deep(.el-input__inner) {
  border-radius: 0;
}
</style>
