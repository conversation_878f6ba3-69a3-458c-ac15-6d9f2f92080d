<template>
	<div class="register">
		<top-bar :isLogin="false" />
		<div class="content">
			<h4>创建您的账号</h4>
			<div class="form">
				<el-form ref="form" :model="form" :rules="rules">
					<el-form-item label="" prop="userName">
						<div class="form-item">
							<!-- <p><span>*</span>用户名称</p> -->
							<el-tooltip
								class="item"
								effect="light"
								placement="right"
								content=" · 中文为 5 ~ 15 个字符，英文为 5 ~ 30 个字符"
								popper-class="register-tooltip"
							>
								<el-input
									v-model="form.userName"
									placeholder="用户名"
								></el-input>
							</el-tooltip>
						</div>
					</el-form-item>
					<el-form-item label="" prop="password">
						<div class="form-item">
							<!-- <p><span>*</span>设置密码</p> -->
							<el-tooltip
								class="item"
								effect="light"
								placement="right"
								popper-class="register-tooltip"
							>
								<div slot="content" class="tips-password">
									<p class="flex">
										<img
											v-if="passwordTips.length"
											src="~@/assets/images/index/right-icon.png"
											alt=""
										/>
										<img
											v-else
											src="~@/assets/images/index/wrong-icon.png"
											alt=""
										/>
										<span>密码长度至少6位,最多14位；</span>
									</p>
									<p class="flex">
										<img
											v-if="passwordTips.repeat"
											src="~@/assets/images/index/right-icon.png"
											alt=""
										/>
										<img
											v-else
											src="~@/assets/images/index/wrong-icon.png"
											alt=""
										/>
										<span> 密码不能与用户名相同；</span>
									</p>
									<p class="flex">
										<img
											v-if="passwordTips.verify"
											src="~@/assets/images/index/right-icon.png"
											alt=""
										/>
										<img
											v-else
											src="~@/assets/images/index/wrong-icon.png"
											alt=""
										/>
										<span
											>密码只能包含数字、字母和符号（除空格）；</span
										>
									</p>
									<p class="flex">
										<img
											v-if="passwordTips.double"
											src="~@/assets/images/index/right-icon.png"
											alt=""
										/>
										<img
											v-else
											src="~@/assets/images/index/wrong-icon.png"
											alt=""
										/>
										<span
											>字母、数字和符号至少包含两种；</span
										>
									</p>
								</div>
								<el-input
									v-model="form.password"
									placeholder="密码"
									show-password
								></el-input>
							</el-tooltip>
						</div>
					</el-form-item>
					<el-form-item label="" prop="confirmPassword">
						<div class="form-item">
							<!-- <p><span>*</span>确认密码</p> -->
							<el-tooltip
								class="item"
								effect="light"
								content=" · 需与密码一致"
								placement="right"
								popper-class="register-tooltip"
							>
								<el-input
									v-model="form.confirmPassword"
									placeholder="确认密码"
									show-password
								></el-input>
							</el-tooltip>
						</div>
					</el-form-item>
					<el-form-item label="" prop="phone">
						<div class="form-item">
							<!-- <p><span>*</span>手机号码</p> -->
							<el-tooltip
								class="item"
								effect="light"
								content=" · 目前只支持中国大陆的手机号码"
								placement="right"
								popper-class="register-tooltip"
							>
								<el-input
									v-model="form.phone"
									placeholder="手机号"
									maxlength="11"
								></el-input>
							</el-tooltip>
						</div>
					</el-form-item>
					<el-form-item label="" prop="captcha">
						<div class="form-item form-item-verify-code">
							<!-- <p><span>*</span>验证码</p> -->
							<el-input
								v-model="form.captcha"
								placeholder="验证码"
								maxlength="6"
							></el-input>
							<div class="verify-code">
								<!-- @click="getCode" -->
								<span
									v-if="!countDownOpen"
									v-throttle="500"
									@click="getCode"
									>获取验证码</span
								>
								<span v-else style="color: #bfbfbf">{{
									countDown + 's后重新获取'
								}}</span>
							</div>
						</div>
					</el-form-item>
					<el-form-item label="" prop="isCheck">
						<div class="form-item form-item-copyright flex">
							<el-checkbox v-model="form.isCheck"></el-checkbox>
							<h5>
								我已阅读以下条款<span @click="handleAgreement"
									>《钰辰物联网平台使用相关协议条款》</span
								>
							</h5>
						</div>
					</el-form-item>
				</el-form>
				<div
					class="handle-register"
					v-throttle="500"
					@click="handleSubmit"
				>
					<span>注册</span>
				</div>
				<div class="handle-login">
					<span @click="handleLogin">已有账号，立即登录</span>
				</div>
			</div>
		</div>
		<copyright />
		<!--  -->
		<confirm title="注册成功" ref="confirm" />
	</div>
</template>

<script>
import conf from './conf'
export default conf
</script>

<style lang="scss" scoped>
.register {
	min-height: 100vh;
	padding-top: 52px;
	display: flex;
	flex-direction: column;
	justify-content: space-between;
	.content {
		width: 680px;
		padding-bottom: 52px;
		margin: 58px auto 0;
		background: #ffffff;
		box-shadow: 0px 48px 48px rgba(0, 0, 0, 0.05);
		border-radius: 4px;
		h4 {
			height: 98px;
			color: #333333;
			line-height: 98px;
			font-weight: 500;
			font-size: 28px;
			border-bottom: 1px solid #f5f5f5;
			text-align: center;
		}
		.form {
			width: 330px;
			margin: 0 auto;
			padding-top: 30px;
			.form-item {
				align-items: center;
				p {
					font-size: 14px;
					line-height: 16px;
					color: #262626;
					padding-bottom: 8px;
					span {
						color: #f53e3e;
					}
				}
			}
			.form-item-verify-code {
				position: relative;
				/deep/ .el-input__inner {
					padding-right: 130px;
				}
				.verify-code {
					position: absolute;
					right: 15px;
					// top: 24px;
					top: 0;
					height: 42px;
					text-align: right;
					width: 100px;
					color: #0088fe;
					cursor: pointer;
				}
			}
			.form-item-copyright {
				line-height: 100%;
				h5 {
					color: #333333;
					font-weight: normal;
					font-size: 12px;
					line-height: 14px;
					padding-left: 12px;
					span {
						color: #0088fe;
						cursor: pointer;
					}
				}
			}
			/deep/ .el-form {
				.el-form-item {
					margin-bottom: 34px;
				}
				.el-input__inner {
					border-radius: 0;
					font-family: 'Courier New', Courier, monospace;
				}
				.el-input {
					input {
						border-radius: 0;
						height: 42px;
						font-size: 14px;
						border: 1px solid #e4e7ec;
					}
					input:focus,
					input:hover {
						border: 1px solid #018aff;
					}
					input::placeholder {
						color: #bfbfbf;
					}
				}
			}
		}
		.handle-register {
			margin-top: 48px;
			height: 42px;
			background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
			text-align: center;
			line-height: 42px;
			color: #ffffff;
			cursor: pointer;
			font-size: 14px;
		}
		.handle-login {
			text-align: right;
			padding-top: 24px;
			span {
				color: #0088fe;
				cursor: pointer;
				font-size: 14px;
				line-height: 16px;
			}
		}
	}
}
</style>

<style lang="scss">
.register-tooltip {
	border-radius: 0;
	border: 1px solid #e4e7ec !important;
	background: #ffffff !important;
	backdrop-filter: blur(4px);
	padding: 14px 18px;
	.popper__arrow {
		border-right-color: #e4e7ec !important;
		left: -10px !important;
		border-width: 10px;
	}
	.popper__arrow::after {
		bottom: -10px !important;
		border-width: 10px;
	}

	.tips-password {
		p {
			align-items: center;
			padding-bottom: 8px;
			font-family: H_Medium;
			img {
				width: 14px;
			}
			span {
				padding-left: 8px;
				color: #515151;
				font-size: 13px;
			}
		}
		p:last-child {
			padding-bottom: 0;
		}
	}
}
</style>
