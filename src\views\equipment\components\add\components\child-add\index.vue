<template>
  <el-dialog
    class="add"
    ref="childDialog"
    :title="title"
    :visible.sync="dialogVisible"
    :append-to-body="true"
    width="728px"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="content">
      <div class="form">
        <el-form
          :model="form"
          ref="ruleForm"
          :rules="rules"
          @validate="fn_validate"
        >
          <el-form-item prop="name">
            <div class="form-item">
              <p class="form-item-label">参数名称 <span>*</span></p>
              <el-input
                v-model="form.name"
                type="text"
                placeholder="请输入功能名称"
              ></el-input>
            </div>
          </el-form-item>
          <div class="el-form-tips" v-if="nameTrue">
            支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点；必须以中文、英文或数字开头，不超过
            30 个字符
          </div>
          <el-form-item prop="id">
            <div class="form-item">
              <p class="form-item-label">标识符</p>
              <el-input
                v-model="form.id"
                type="text"
                placeholder="请输入标识符"
              ></el-input>
            </div>
          </el-form-item>
          <div class="el-form-tips" v-if="idTrue">
            支持英文、数字、下划线的组合，不超过 50 个字符
          </div>
          <data-type
            ref="dataType"
            key="childDataType"
            :isChild="isChild"
            :form="form"
            :enumTips="enumTips"
            :boolTips="boolTips"
            :objectTips="objectTips"
            :arrayTips="arrayTips"
            :unitList="unitList"
            @addAttribute="addAttribute"
            @editAttribute="editAttribute"
            @deleteAttribute="deleteAttribute"
            @change="valChange"
          />
        </el-form>
      </div>
    </div>
    <div class="footer flex">
      <p class="cancel" @click="handleClose">取消</p>
      <p class="submit" v-throttle="500" @click="handleSubmit">提交</p>
    </div>
    <!--  -->
    <child-add
      ref="childAddComplex"
      :unitList="unitList"
      :mode="0"
      :reservedList="reservedList"
      @append="append"
      @edit="childEdit"
    />
  </el-dialog>
</template>

<script>
import dataType from "../data-type";
import { set as lodashSet, merge as lodashMerge } from "lodash";
import {
  reg_one,
  reg_three,
  reg_nine,
  reg_twelve,
  reg_six,
  reg_five,
  reg_ten,
  reg_eleven,
  eighteen,
  nineteen,
  twenty,
} from "@/util/util.js";
export default {
  name: "child-add",
  data() {
    return {
      isEdit: false, //0新增  1 修改
      title: "新增参数",
      dialogVisible: false,
      form: {
        name: "",
        id: "",
        data: {
          type: "int", //数据类型
          rules: {
            arrayType: "int",
            min: "",
            max: "",
            unit: "",
            unitName: "",
            size: "",
            step: "",
            length: "",
          },
          enumList: [
            {
              isIntFocus: false,
              key: "",
              value: "",
            },
          ],
          jsonData: [],
          arrayData: [],
        }, //
      },
      isChild: false, //// 是否开放  数组和对象 数据类型   单独控制
      childDataType: "", //申请添加数据的 类型   可能是array 也可能是object
      arraySource: "", //in / out  字段名

      // 校验
      nameTrue: true,
      idTrue: true,
      enumTips: "",
      boolTips: "",
      objectTips: "",
      arrayTips: "",
      rules: {
        name: [
          {
            required: true,
            // message: "请输入功能名称",
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        id: [
          {
            required: true,
            // message: '支持英文、数字、下划线的组合，不超过 50 个字符',
            trigger: "blur",
            validator: this.checkId,
          },
        ],
        "data.rules.min": [
          {
            required: false,
            // message: "请输入最小值",
            trigger: "blur",
            validator: this.checkMin,
          },
        ],
        "data.rules.step": [
          {
            required: false,
            // message: "请输入步长",
            trigger: "blur",
            validator: this.checkStep,
          },
        ],
        "data.rules.length": [
          {
            required: true,
            // message: "请输入最小值",
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
        "data.rules.size": [
          {
            required: false,
            // message: "请输入个数",
            trigger: "blur",
            validator: this.checkSize,
          },
        ],
      },

      nowIndex: -1, //用户编辑时过滤当前id
      oldId: "",
    };
  },
  props: {
    // mode    0 属性    1  事件   2 服务    单独控制   父组件key值 为classification
    mode: {
      type: [String, Number],
    },
    unitList: {
      type: Array,
    },
    reservedList: {
      type: Array,
    },
  },
  components: { dataType },
  methods: {
    // 变化事件-------------------------------------------------------------------⬇
    resetRules() {
      this.enumTips = "";
      this.boolTips = "";
      this.objectTips = "";
      this.arrayTips = "";
      // console.log(this.form.data.rules);
      this.form.data.rules = {
        arrayType: "int",
        min: "",
        max: "",
        unit: "",
        unitName: "",
        size: "",
        step: "",
        length: "",
        item: {
          size: "",
          rules: [],
        },
      };
    },
    // 数据结构组件  基本类型值变换传递
    valChange(data) {
      if (data.key == "data.type" || data.key == "data.rules.arrayType") {
        // 重置 rules    data。type 修改后rulesjiegou可能会变  重置成最初的对象才能触发form的验证
        this.resetRules();
      }
      lodashSet(this.form, data.key, data.value);
      this.$refs.ruleForm.clearValidate();
    },
    //   事件 / 服务  下的新增参数  （包括复合类型）  接收 {data,type}
    addAttribute(data) {
      let config = {
        source: this.arraySource,
      };
      config = lodashMerge(config, data);
      this.$refs.childAddComplex.add(true, config, this.form);
    },
    editAttribute(data) {
      let config = {
        source: this.arraySource,
      };
      config = lodashMerge(config, data);
      this.$refs.childAddComplex.edit(true, data.data, config, this.form);
    },
    deleteAttribute(data) {
      let name = "";
      if (data.type == "object") {
        name = "jsonData";
      } else if (data.type == "array") {
        name = "arrayData";
      }
      this.form.data[name] = this.form.data[name].filter(
        (item) => item.id != data.data.id
      );
    },
    // 表单基本事件--------------------------------------------------------------  ⬇

    // 表单校验事件------------------
    checkName(rule, value, callback) {
      if (value !== "") {
        let flag;
        // 属性
        if (this.form.mode != 0) {
          // 事件
          if (this.isEdit) {
            // 修改
            flag = this.verifyData.findIndex(
              (item, index) => item.name == value && index !== this.nowIndex
            );
          } else {
            flag = this.verifyData.findIndex((item) => item.name == value);
          }
          if (flag !== -1) {
            //
            return callback(new Error("同一类型下的参数名称不能重复"));
          }
        }
      }
      if (!reg_three(value)) {
        return callback(
          new Error(
            "支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点；必须以中文、英文或数字开头，不超过 30 个字符"
          )
        );
      } else {
        callback();
      }
    },
    checkId(rule, value, callback) {
      if (!reg_nine(value)) {
        return callback(
          new Error("支持英文、数字、下划线的组合，不超过 50 个字符")
        );
      }
      if (this.reservedList.includes(value)) {
        return callback(new Error("是系统保留字段，不能用于标识符定义"));
      }
      let flag;
      if (this.isEdit && this.oldId == value) {
        // 编辑  且新旧id相同
        flag = -1;
      } else {
        // 新增  /  编辑时id 不同
        flag = this.verifyData.findIndex((item) => item.id == value);
      }

      if (flag !== -1) {
        //
        return callback(new Error("已存在该标识符"));
      }
      callback();
    },
    checkMin(rule, value, callback) {
      if (value !== "" || this.form.data.rules.max !== "") {
        if (value !== "") {
          this.fn_checkMin(value, callback);
        }
        if (this.form.data.rules.max !== "") {
          this.fn_checkMin(this.form.data.rules.max, callback);
        }
        if (
          this.form.data.rules.max !== "" &&
          reg_five(value, this.form.data.rules.max)
        ) {
          return callback(new Error("最小值不得大于等于最大值"));
        }
        callback();
      } else {
        return callback();
      }
    },
    fn_checkMin(value, callback) {
      if (this.form.data.type == "int") {
        if (!/^-?[0-9]\d*$/.test(value)) {
          return callback(new Error("当前仅支持整数"));
        }
      } else {
        if (this.form.data.type == "float") {
          if (!eighteen(value)) {
            return callback(new Error("仅支持数字且小数点后不得超过7位"));
          }
        } else if (!nineteen(value)) {
          return callback(new Error("仅支持数字且小数点后不得超过16位"));
        }
      }
    },
    checkStep(rule, value, callback) {
      if (value !== "") {
        if (this.form.data.type == "int") {
          if (!/(^[1-9]\d*$)/.test(value)) {
            return callback(new Error("当前仅支持大于0的正整数"));
          }
        } else {
          if (!reg_eleven(value)) {
            return callback(new Error("当前仅支持正数"));
          }
          if (this.form.data.type == "float") {
            if (!eighteen(value)) {
              return callback(new Error("仅支持正数且小数点后不得超过7位"));
            }
          } else if (!nineteen(value)) {
            return callback(new Error("仅支持正数且小数点后不得超过16位"));
          }
        }
        if (this.form.data.rules.min && this.form.data.rules.max) {
          if (
            !reg_ten(
              0,
              Math.abs(this.form.data.rules.max) +
                Math.abs(this.form.data.rules.min),
              value
            )
          ) {
            return callback(new Error("超出取值范围"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    checkEnum() {
      let flag = true;
      let newArr = [];
      this.enumTips = "";
      if (this.form.data.enumList && this.form.data.enumList.length == 0) {
        this.enumTips = "至少输入一项";
        flag = false;
      } else {
        // 判断项是否满足
        for (let i = 0; i < this.form.data.enumList.length; i++) {
          if (
            this.form.data.enumList[i].key == "" ||
            this.form.data.enumList[i].value == ""
          ) {
            this.enumTips = "数值与说明不能为空";
            flag = false;
            break;
          }
          if (!twenty(this.form.data.enumList[i].key)) {
            this.enumTips = "数值只能为数字";
            flag = false;
            break;
          }
          if (
            !this.form.data.enumList[i].value ||
            !reg_one(this.form.data.enumList[i].value)
          ) {
            this.enumTips = "说明不能为空且不能超过32个字符";
            flag = false;
            break;
          }
          // 判断是否重复
          if (newArr.indexOf(this.form.data.enumList[i].key) == -1) {
            newArr.push(this.form.data.enumList[i].key);
          }
        }
        if (!flag) return flag;
        if (newArr.length != this.form.data.enumList.length) {
          this.enumTips = "数值不能重复";
          flag = false;
        }
      }
      return flag;
    },
    // bool 验证
    checkBool() {
      this.boolTips = "";
      if (
        this.form.data.rules["0"] == undefined ||
        this.form.data.rules["0"] == ""
      ) {
        this.boolTips = `缺少'否'含义的文字说明`;
        return false;
      } else if (
        this.form.data.rules["1"] == undefined ||
        this.form.data.rules["1"] == ""
      ) {
        this.boolTips = `缺少'是'含义的文字说明`;
        return false;
      }
      return true;
    },
    checkLength(rule, value, callback) {
      if (value !== "") {
        if (!reg_twelve(value)) {
          //
          return callback(new Error("取值范围为1~1000的正整数"));
        }
      }
      callback();
    },
    checkSize(rule, value, callback) {
      if (value != "") {
        if (!reg_six(value)) {
          //
          return callback(new Error("只能输入数字"));
        }
      }
      callback();
    },
    // 验证-------------------------------------end
    fn_validate(name, value) {
      if (name === "id") {
        this.idTrue = value;
      } else if (name === "name") {
        this.nameTrue = value;
      }
    },
    // 表单事件     如果是复合类型   需要传递  数组用于区分标识符
    add(isChild, config, form) {
      this.isEdit = false;
      this.title = "新增参数";
      this.isChild = isChild;
      if (this.mode == 0) {
        // 属性传递  数据类型
        this.childDataType = config.type;
        if (config.type == "object") {
          this.verifyData = form.data.jsonData;
        } else if (config.type == "array") {
          this.verifyData = form.data.arrayData;
        }
      } else {
        // 事件 服务 传递 来源  in  / out
        this.arraySource = config.source;
        // 递归子组件传递的数据类型
        this.childDataType = config.type || "";
        // 单一 入参/出参 参数列表    参数标识符   --------- 功能下参数标识符唯一
        // this.verifyData = form[config.source]; //
        // ---------------组合验证数据进行校验
        //组合（服务/事件）的参数  用于区分唯一标识
        let inList = form["in"] || [];
        let outList = form["out"] || [];
        this.verifyData = inList.concat(outList);
      }
      this.open();
    },
    // form 是父级数据  record
    edit(isChild, record, config, form) {
      this.isEdit = true;
      this.title = "修改参数";
      this.isChild = isChild;
      this.oldId = record.id;
      if (this.mode == 0) {
        // 属性传递  数据类型
        this.childDataType = config.type;
        if (config.type == "object") {
          this.verifyData = form.data.jsonData;
        } else if (config.type == "array") {
          this.verifyData = form.data.arrayData;
        }
      } else {
        // 事件 服务 传递 来源  in  / out
        this.arraySource = config.source;
        // 递归子组件传递的数据类型
        this.childDataType = config.type || "";
        this.verifyData = form[config.source];
      }
      this.nowIndex = this.verifyData.findIndex((item) => item.id == record.id); ///保存当前下标
      this.$nextTick(() => {
        let data = JSON.parse(JSON.stringify(record));
        this.form = lodashMerge(this.form, data);
      });

      this.open();
    },
    open() {
      this.dialogVisible = true;
    },
    close() {
      this.nameTrue = true;
      this.idTrue = true;
      this.dialogVisible = false;
    },
    handleClose() {
      // 子参数 只需要重置基本类型  max  无法清空 ?
      if (this.form.data.rules.max) {
        this.form.data.rules.max = "";
      }

      this.handleReset(); //清空复合类型
      this.$refs.dataType.resetDataComplex(); ////全清空  包括复合类型

      try {
        this.$refs.ruleForm.resetFields(); //resetFields 不能使用  表单存在未使用的值会报错 只能手动清除值  clearValidate
      } catch {
        //
      }

      this.close();
    },
    verifySubmit() {
      if (this.form.data.type == "enum") {
        // enum 校验
        if (!this.checkEnum()) return false; // 不通过 退出
      } else if (this.form.data.type == "bool") {
        // bool 校验
        if (!this.checkBool()) return false; // 不通过 退出
      } else if (this.form.data.type == "object") {
        if (this.form.data.jsonData.length == 0) {
          //
          this.objectTips = "至少输入一项";
          return false;
        }
      } else if (this.form.data.type == "array") {
        if (this.form.data.rules.arrayType == "object") {
          if (this.form.data.arrayData.length == 0) {
            //
            this.arrayTips = "至少输入一项";
            return false;
          }
        }
      }
      return true;
    },
    handleSubmit() {
      if (!this.verifySubmit()) return;
      this.$refs.ruleForm.validate((valid) => {
        if (valid) {
          // 发送子项添加完成事件  携带子集数据
          let data = {
            classification: this.mode, ////父组件key 为   classification   子组件 分类标识为mode字段
            data: JSON.parse(JSON.stringify(this.form)),
          };
          data["type"] = this.childDataType;
          data["source"] = this.arraySource;
          data["nowIndex"] = this.nowIndex;
          if (this.isEdit) {
            // 编辑
            this.$emit("edit", data);
          } else {
            this.$emit("append", data);
          }
          this.handleClose();

          return;
        } else {
          return false;
        }
      });
    },
    // 递归组件 抛出数据
    append(data) {
      // 注  递归子组件传递的mode   默认为0  不作处理
      let name = "";
      if (data.type == "object") {
        name = "jsonData";
      } else if (data.type == "array") {
        name = "arrayData";
      }
      this.form.data[name].push(data.data);
      // this.resetRules();
      this.enumTips = "";
      this.boolTips = "";
      this.objectTips = "";
      this.arrayTips = "";
    },
    childEdit(data) {
      let name = "";
      if (data.type == "object") {
        name = "jsonData";
      } else if (data.type == "array") {
        name = "arrayData";
      }
      this.$set(this.form.data[name], data.nowIndex, data.data);
    },
    handleReset() {
      this.resetRules(); //重置 rules 结构  及 提示语
      this.form.data.jsonData = [];
      this.form.data.arrayData = [];
      this.form.data.enumList = [
        {
          isIntFocus: false,
          key: "",
          value: "",
        },
      ];
    },
  },
};
</script>

<style lang="scss" scoped>
.add {
  .content {
    height: 550px;
    overflow: auto;
    padding: 20px 32px;
    border-radius: 0px 0px 2px 2px;
    .form {
      .form-item {
        .form-item-label {
          color: #515151;
          font-size: 14px;
          line-height: 14px;
          padding: 6px 0 8px 0;
          span {
            color: #ff2836;
          }
        }
        /deep/ {
          .el-radio-button__inner {
            width: 110px;
          }
          .el-input__inner {
            border-radius: 0;
          }
        }
      }
    }
  }
  .footer {
    height: 72px;
    align-items: center;
    justify-content: flex-end;
    background: #fbfbfb;
    border-radius: 0px 0px 2px 2px;
    padding: 0 32px;
    p {
      width: 96px;
      height: 36px;
      border: 1px solid #eeeff1;
      text-align: center;
      line-height: 36px;
      font-size: 14px;
      margin-left: 18px;
      color: #333333;
      cursor: pointer;
      background: #ffffff;
    }
    .submit {
      border: none;
      color: #ffffff;
      background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
    }
  }
  /deep/ {
    .el-dialog__header {
      height: 62px;
      border-bottom: 1px solid #eeeff1;
      .el-dialog__close {
        font-size: 18px;
        color: #515151;
        font-weight: bold;
      }
    }
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      display: none;
    }
  }
}
//
.el-form-tips {
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  margin-top: -16px;
  // margin-bottom: 9px;
}
</style>
