<template>
	<el-form-item
		v-if="attr.data.type == 'int'"
		:prop="`${prop}.value`"
		:rules="[{ validator: intValidate, trigger: 'blur' }]"
	>
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<iot-input
					placeholder="请输入参数(int)"
					v-model="attr.value"
					:unit="attr.data.rules.unit"
				/>
			</div>
		</div>
	</el-form-item>
	<el-form-item
		v-else-if="attr.data.type == 'float'"
		:prop="`${prop}.value`"
		:rules="[{ validator: floatValidate, trigger: 'blur' }]"
	>
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<iot-input
					v-model="attr.value"
					placeholder="请输入参数(float)"
					:unit="attr.data.rules.unit"
				/>
			</div>
		</div>
	</el-form-item>
	<el-form-item
		v-else-if="attr.data.type == 'double'"
		:prop="`${prop}.value`"
		:rules="[{ validator: doubleValidate, trigger: 'blur' }]"
	>
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<iot-input
					v-model="attr.value"
					placeholder="请输入参数(double)"
					:unit="attr.data.rules.unit"
				/>
			</div>
		</div>
	</el-form-item>
	<el-form-item v-else-if="attr.data.type == 'enum'" :prop="`${prop}.value`">
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }} </span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<el-select v-model="attr.value" placeholder="请输入参数(enum)">
					<el-option
						v-for="item in attr.data.enumList"
						:key="item.key"
						:label="item.label"
						:value="item.value"
					>
					</el-option>
				</el-select>
			</div>
		</div>
	</el-form-item>
	<el-form-item
		v-else-if="attr.data.type == 'text'"
		:prop="`${prop}.value`"
		:rules="[{ validator: textValidate, trigger: 'blur' }]"
	>
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<iot-input
					v-model="attr.value"
					placeholder="请输入参数(text)"
				/>
			</div>
		</div>
	</el-form-item>
	<el-form-item v-else-if="attr.data.type == 'bool'" :prop="`${prop}.value`">
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<el-select v-model="attr.value" placeholder="请输入参数(bool)">
					<el-option
						v-for="item in attr.data.boolList"
						:key="item.key"
						:label="item.label"
						:value="item.value"
					>
					</el-option>
				</el-select>
			</div>
		</div>
	</el-form-item>
	<el-form-item v-else-if="attr.data.type == 'time'" :prop="`${prop}.value`">
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<el-date-picker
					v-model="attr.value"
					type="datetime"
					placeholder="请选择日期和时间"
					prefix-icon="el-icon-date"
				>
				</el-date-picker>
			</div>
		</div>
	</el-form-item>
	<el-form-item
		v-else-if="attr.data.type == 'array'"
		:prop="`${prop}.value`"
		:rules="[{ validator: arrayValidate, trigger: 'blur' }]"
	>
		<div class="form-item flex">
			<div class="form-item-title">
				<span>{{ attr.name }}/{{ attr.id }}</span>
			</div>
			<!-- unit -->
			<div class="form-item-inp">
				<iot-input
					v-model="attr.value"
					placeholder='输入数据须符合数组JSON格式,如[1,2], ["i","j"], [{"key":"value"}]'
				/>
			</div>
		</div>
	</el-form-item>
	<div class="object" v-else-if="attr.data.type == 'object'">
		<h4>{{ attr.name }}/{{ attr.id }}</h4>
		<div class="object-content">
			<online-custom-item
				v-for="(item, index) in attr.data.rules"
				:key="item.id"
				:index="index"
				:attr="item"
				:prop="`${prop}.data.rules.${index}`"
			/>
		</div>
	</div>
</template>

<script>
import IotInput from '@/components/iot-input'
export default {
	name: 'onlineCustomItem',
	components: { IotInput },
	data() {
		return {
			// 暂未使用 -----------------⬇
			intValue: '',
			floatValue: '',
			doubleValue: '',
			enumValue: '',
			textValue: '',
			boolValue: '',
			timeValue: '',
			arrayValue: '',
			// 暂未使用------------------⬆
		}
	},
	props: {
		attr: {
			type: Object,
		},
		index: {
			type: [Number, String],
		},
		prop: {
			type: String,
		},
	},
	methods: {
		intValidate(rule, value, callback) {
			if (value !== '') {
				let rules = this.attr.data.rules
				let result = this.checkDefault('int', value, rules)
				if (result.flag) {
					callback(result.text)
				} else {
					callback()
				}
			} else {
				callback()
			}
		},
		floatValidate(rule, value, callback) {
			if (value !== '') {
				let rules = this.attr.data.rules
				let result = this.checkDefault('float', value, rules)
				if (result.flag) {
					callback(result.text)
				} else {
					callback()
				}
			}
			callback()
		},
		doubleValidate(rule, value, callback) {
			if (value !== '') {
				let rules = this.attr.data.rules
				let result = this.checkDefault('double', value, rules)
				if (result.flag) {
					callback(result.text)
				} else {
					callback()
				}
			}
			callback()
		},
		textValidate(rule, value, callback) {
			if (value !== '') {
				let rules = this.attr.data.rules
				let maxLength = rules.length || 1000
				let result = this.checkDefault('text', value, rules)
				if (result.flag) {
					callback(`字符串的长度不能超过${maxLength}`)
				} else {
					callback()
				}
			}
			callback()
		},
		arrayValidate(rule, value, callback) {
			if (value !== '') {
				let list // 储存过滤用户输入的数组
				let size = this.attr.data.rules.size || 100 //数组长度
				let { type, rules: egRules } = this.attr.data.rules.item //数组项的类型  项的内容
				let typeFlag = false,
					valueFlag = false
				let errText = `格式错误，请输入 array（${type}） 格式参数`
				// 判断是否满足json 格式
				try {
					list = JSON.parse(`${value}`)
				} catch {
					console.warn('该字符串不是JSON字符串')
					callback(errText)
				}
				if (!Array.isArray(list)) {
					console.warn('该字符串满足JOSN格式但不是数组')
					callback(errText)
					return
				}
				if (list.length > size || list.length <= 0) {
					console.warn('该字符串满足JOSN格式但数组长度不符')
					callback(`请输入正确的长度（${size}）`)
				}

				// 用for循环  方便中断循环
				for (let i = 0; i < list.length; i++) {
					let item = list[i]
					// 检查类型
					typeFlag = this.judgeType(type, item)
					if (typeFlag) {
						console.warn(
							'该字符串满足JSON数组字符串，但不是物模型定义的子项类型'
						)
						callback(errText)
						break
					}
					// 类型值判断
					valueFlag = this.checkValue(type, item, egRules)
					if (valueFlag) {
						console.warn(
							'该字符串满足JSON数组字符串，但子项类型值未满足物模型定义的数据结构'
						)
						callback(errText)
						break
					}
					callback()
				}
			} else {
				callback()
			}
		},
		judgeType(type, item) {
			if (type === 'int' || type === 'float' || type === 'double') {
				if (typeof item !== 'number') {
					// int float double
					return true
				}
			} else if (type === 'text') {
				if (typeof item !== 'string') {
					// text类型
					return true
				}
			} else if (type === 'object') {
				if (typeof item !== 'object') {
					return true
				}
			}
			return false
		},
		// 检测基本类型
		checkDefault(type, item, rules = {}) {
			if (type == 'int') {
				if (!/^-?[0-9]\d*$/.test(item))
					return { flag: true, text: '格式错误，请输入 int 格式参数' }
			} else if (type == 'float') {
				if (!/^-?\d{1,12}([.]\d{0,7})?$/.test(item))
					// 小数点位数超过7
					return {
						flag: true,
						text: '格式错误，请输入 float 格式参数',
					}
			} else if (type == 'double') {
				if (!/^-?\d{1,12}([.]\d{0,16})?$/.test(item))
					// 小数点位数超过了16
					return {
						flag: true,
						text: '格式错误，请输入 double 格式参数',
					}
			} else if (type == 'text') {
				if (rules.length !== '' && rules.length < item.length)
					return { flag: true }
				if (item.length > 1000) return { flag: true }
			} else if (type == 'enum' || type == 'bool') {
				// 数组下的枚举 布尔  取值  存在即合理
				if (!rules[item]) return { flag: true }
			}

			if (type == 'int' || type == 'float' || type == 'double') {
				if (rules.max !== '' && rules.min !== '') {
					if (Number(rules.min) > item || Number(rules.max) < item)
						return {
							flag: true,
							text: `请输入正确的取值范围(${rules.min}~${rules.max})`,
						}
				}
				if (rules.min !== '' && Number(rules.min) > item)
					return { flag: true, text: `取值不得小于${rules.min}` }
				if (rules.max !== '' && Number(rules.max) < item)
					return { flag: true, text: `取值不得超过${rules.max}` }
			}
			// 时间格式验证
			/* if (type == 'time') {
        if (`${item}`.length > 10) {
          return { flag: true, text: '请输入十位数以下数字' }
        }
      } */
			return { flag: false, text: '' }
		},
		// 检测数组项  (基本类型的特殊情况)
		checkValue(type, item, egRules) {
			console.log(type, item, egRules)
			/* if (type == "int") {
        return !/^-?[0-9]\d*$/.test(item);
      } else if (type == "float") {
        return !/^-?\d{1,12}([.]\d{0,7})?$/.test(item);
      } else if (type == "double") {
        return !/^-?\d{1,12}([.]\d{0,16})?$/.test(item);
      } else if (type == "text") {
        return item.length > 1000 ? true : false;
      }else if (type == "object") {
        let keys = Object.keys(item);
        if (keys.length == 0) {
          // 允许对象为空
          return false;
        } else if (keys.length != egRules.length) {
          // 对象有属性  但与 模型 对象属性长度不一致  ：未填写完整
          return true;
        } else {
          // 具体判断属性值
          for (let i = 0; i < keys.length; i++) {
            let index = egRules.findIndex((item) => item.id == keys[i]);
            if (index !== -1) {
              // 找到对应项
              let result = this.checkDefault(
                egRules[index].data.type,
                item[keys[i]],
                egRules[index].data.rules
              );

              if (result.flag) return true;
            } else {
              return true;
            }
          }
        }
      } */
			return false
		},
	},
}
</script>

<style lang="scss" scoped>
.form-item {
	align-items: center;
	justify-content: space-between;
	.form-item-title {
		color: #515151;
		font-size: 14px;
		max-width: 180px;
		word-break: break-all;
	}
	.form-item-inp {
		width: 458px;
	}
}

.object {
	// border-bottom: 1px solid #eeeff1;
	margin-bottom: 26px;
	h4 {
		color: #333333;
		font-weight: normal;
		font-size: 14px;
		padding-bottom: 26px;
	}
	.object-content {
		padding-left: 24px;
	}
}
/deep/ {
	.el-select {
		width: 100%;
		.el-input__inner {
			border-radius: 0;
		}
	}
	.el-form-item__content {
		line-height: 34px;
	}
	.el-date-editor {
		width: 100%;
		.el-input__inner {
			border-radius: 0;
		}
	}
	.el-form-item__error {
		left: calc(100% - 458px);
	}
	.el-select__caret {
		color: #666666 !important;
	}
}
</style>
