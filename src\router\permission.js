/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-11 17:27:53
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2022-05-13 15:42:37
 */
import Router from "./index";
import Encrypt from "@/util/aes";

Router.beforeEach((to, from, next) => {
    let access_token = localStorage.getItem("access_token");
    let tenant_id = Encrypt.decryptoByAES(localStorage.getItem("tenant_id"));
    if (to.path == "/login") {
        if (access_token && tenant_id) {
            next("/product");
        } else {
            next();
        }
    } else {
        if (to.meta.isLogin) {
            if (access_token && tenant_id) {
                next();
            } else {
                return next("/login");
            }
        } else {
            next();
        }
    }
});

Router.afterEach(() => {
    // console.log('meta',to.meta)
    document.title = "钰辰物联网平台"; // 动态设置浏览器标题
    //   let head = document.getElementsByTagName("head");
    //   let meta = document.createElement("meta");
    //   //og:description
    //   // meta.name = 'description'
    //   // meta.content = 'AI+IoT平台为客户提给AIoT智慧物联网场景应用解决方案'
    //   meta.property = "description";
    //   meta.content = "AI+IoT平台为客户提给AIoT智慧物联网场景应用解决方案";
    //   head[0].appendChild(meta);
    // if (to.meta.crumb) {
    //   document.title = to.meta.crumb[to.meta.crumb.length -1].name || "租户平台"; // 动态设置浏览器标题
    // } else {
    //   document.title = to.meta.title || "租户平台"; // 动态设置浏览器标题
    // }
});
