/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-11 10:00:31
 * @LastEditors: hs
 * @LastEditTime: 2021-12-22 17:23:49
 */
export default {
  userInfo: (state) => state.user.userInfo,
  xtSocket: (state) => state.device.xtSocket,
  statusData: (state) => state.device.statusData,
  layoutInfo: (state) => state.layout.layoutInfo,
  logData: (state) => state.online.logData,
  xtLogsSocket: (state) => state.online.xtLogsSocket,
  logDataCopy: (state) => state.online.logDataCopy,
  onlineFlag: (state) => state.online.onlineFlag,
};
