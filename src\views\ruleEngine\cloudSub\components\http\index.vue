<template>
  <div class="cloudSub">
    <iot-button
      :text="btnText"
      :style="addStyle"
      v-throttle="500"
      @search="fn_add"
      class="btn_create"
    />
    <div class="cloudSub-table">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="secretKey" slot-scope="scope">
          <p v-if="showSecretKey">
            {{ scope.row.secretKey }}
          </p>
          <p v-else>
            <span>******************************</span>
          </p>
        </template>
        <template slot="action" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_edit" class="color2">查看Serect</p>
            <p></p>
            <p @click="fn_del(scope.row.id)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="gap"></div>
    <!-- <h4 class="title">事件上报配置</h4> -->
    <div>
      <iot-form>
        <template #default>
          <el-form ref="form" :model="form" :rules="rules">
            <el-form-item prop="eventReportedUrl">
              <div class="form-item-title">
                <p>事件上报地址</p>
              </div>
              <el-input
                v-model="form.eventReportedUrl"
                placeholder="http://"
              ></el-input>
            </el-form-item>
          </el-form>
        </template>
      </iot-form>
      <!-- <div class="form-tips">
        配置连接后，该接口用来向您的服务器推送设备数据，查看
        <span><a href="test.txt" download="test.txt">技术文档</a></span>
      </div> -->
    </div>
    <!-- <div class="gap"></div>
    <h4 class="title">指令下发</h4>
    <div>
      <iot-form>
        <template #default>
          <el-form>
            <el-form-item>
              <div class="form-item-title">
                <p>指令下发地址</p>
              </div>
              <el-input
                v-model="instructionIssueUrl"
                :disabled="true"
              ></el-input>
            </el-form-item>
          </el-form>
        </template>
      </iot-form>
      <div class="form-tips">
        该接口用于转发设备控制指令，查看<span
          ><a href="test.txt" download="test.txt">技术文档</a></span
        >
      </div>
    </div> -->
    <div class="submit">
      <iot-button text="保存配置" @search="handleSubmit" />
    </div>

    <iot-dialog
      :visible.sync="visible"
      :title="title"
      width="550px"
      @callbackSure="fn_sure"
    >
      <template #body>
        <iot-form>
          <template #default>
            <el-form>
              <el-form-item>
                <div class="del-tips">
                  请注意：删除Access Key Serect后，所有使用就Access Key
                  Serect的接口将立即失效，为确保能够正常使用，删除后，请尽快更新Access
                  Key Serect信息。
                </div>
              </el-form-item>
            </el-form>
          </template>
        </iot-form>
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button";
import IotTable from "@/components/iot-table";
import IotForm from "@/components/iot-form";
import IotDialog from "@/components/iot-dialog";
import { reg_fourteen } from "@/util/util";
import {
  getSecretKey,
  addSecretKey,
  deleteSecretKey,
  updateSecretKey,
} from "@/api/cloud";
import { cloneDeep } from "lodash";
export default {
  name: "cloudSub",
  data() {
    return {
      loading: false,
      showSecretKey: false,
      model: {}, //返回数据
      title: "确定删除？",
      visible: false,
      addSecret: false, //是否允许新增key
      columns: [
        // { label: "Public Key", prop: "publicKey", width: "350px" },
        {
          label: "Access Key Secret",
          prop: "secretKey",
          slotName: "secretKey",
          // width: "700px",
        },
        { label: "创建时间", prop: "time", width: "200px" },
        { label: "操作", slotName: "action", width: "200px" },
      ],
      tableData: [],
      instructionIssueUrl: "", //下发地址
      form: {
        eventReportedUrl: "",
      },
      rules: {
        eventReportedUrl: [
          {
            required: true,
            trigger: "blur",
            validator: this.checkReport,
          },
        ],
      },
    };
  },
  computed: {
    addStyle() {
      return {
        background: this.addSecret
          ? "linear-gradient(180deg, #0088fe, #006eff 100%)"
          : "linear-gradient(180deg, #0088fe, #006eff 100%)",
      };
    },
    btnText() {
      return this.addSecret ? "生成加密密钥" : "重新生成加密密钥";
    },
  },
  components: { IotButton, IotTable, IotForm, IotDialog },
  mounted() {
    this.getData();
  },
  methods: {
    checkReport(rule, value, callback) {
      if (!reg_fourteen(value)) {
        return callback(new Error("上报地址必须以http开头"));
      } else {
        callback();
      }
    },
    getData() {
      this.showSecretKey = false;
      getSecretKey().then((res) => {
        if (res.code == 200) {
          //
          this.tableData = [];
          if (res.data.privateKey) {
            this.tableData.push({
              publicKey: res.data.publicKey,
              secretKey: res.data.privateKey,
              time: res.data.createTime,
            });
            this.addSecret = false;
          } else {
            this.addSecret = true;
          }
          this.model = cloneDeep(res.data);
          this.form.eventReportedUrl = res.data.eventReportedUrl;
          this.instructionIssueUrl = res.instructionIssueUrl;
        } else {
          //
          this.addSecret = true;
        }
      });
    },
    fn_add() {
      addSecretKey().then((res) => {
        if (res.code == 200) {
          this.getData();
          this.$newNotify.success({
            message: res.message,
          });
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    fn_edit() {
      this.showSecretKey = !this.showSecretKey;
    },
    fn_del() {
      this.visible = true;
    },
    fn_sure() {
      if (!this.model.id) return;
      deleteSecretKey({
        ids: this.model.id,
      }).then((res) => {
        if (res.code == 200) {
          this.$nextTick(() => {
            this.getData();
            this.visible = false;
          });
          this.$newNotify.success({
            message: res.message,
          });
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    handleSubmit() {
      this.$refs.form.validate((vaild) => {
        if (vaild) {
          //
          updateSecretKey({
            id: this.model.id,
            eventReportedUrl: this.form.eventReportedUrl,
          }).then((res) => {
            if (res.code == 200) {
              this.$nextTick(() => {
                this.getData();
                this.visible = false;
              });
              this.$newNotify.success({
                message: res.message,
              });
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
        }
      });
    },
    upload() {},
  },
};
</script>

<style lang="scss" scoped>
.cloudSub {
  padding-top: 15px;
  padding-bottom: 32px;
  .btn_create {
    background: linear-gradient(180deg, #0088fe, #006eff 100%);
    border: #0088fe;
  }
  .cloudSub-table {
    padding-top: 18px;
  }
  .empty {
    text-align: center;
    line-height: 50px;
  }
  .table-edit {
    display: flex;
    align-items: center;
    p {
      cursor: pointer;
    }
    p:nth-child(2) {
      margin: 0px 12px;
      width: 1px;
      height: 13px;
      border: 1px solid #ededed;
    }
  }
  .title {
    padding: 18px 0 24px;
    color: #333333;
    font-weight: 500;
    font-size: 16px;
  }
  .form-item-title {
    color: #515151;
    font-size: 14px;
    padding-bottom: 8px;
  }
  .form-tips {
    color: #999999;
    margin-top: 18px;
    font-size: 14px;
    span,
    a {
      color: #018aff;
      cursor: pointer;
      text-decoration: none;
    }
  }
  .submit {
    padding-top: 8px;
  }
}
/deep/ .el-form-item {
  margin-bottom: 18px;
}

.gap {
  height: 28px;
}
</style>
