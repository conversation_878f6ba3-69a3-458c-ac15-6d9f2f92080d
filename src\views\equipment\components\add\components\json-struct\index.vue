<template>
  <div class="item">
    <el-form-item>
      <div class="form-item">
        <p class="form-item-label">{{ title }}</p>
        <div class="json">
          <el-table :data="data" stripe style="width: 100%">
            <el-table-column prop="name" label="参数名"> </el-table-column>
            <el-table-column prop="id" label="标识符"> </el-table-column>
            <el-table-column prop="data.type" label="参数类型">
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <div class="action flex">
                  <p class="edit" @click="jsonEdit(scope.row)">编辑</p>
                  <p class="delete" @click="jsonDelete(scope.row)">删除</p>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <div class="struct-add">
          <p @click="addAttribute">+ 新增参数</p>
        </div>
      </div>
    </el-form-item>
  </div>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: "JSON对象",
    },
    data: {
      type: Array,
    },
  },
  methods: {
    addAttribute() {
      this.$emit("addAttribute");
    },
    jsonEdit(data) {
      this.$emit("editAttribute", data);
    },
    jsonDelete(data) {
      this.$emit("deleteAttribute", data);
    },
  },
};
</script>

<style lang="scss" scoped>
.item {
  .form-item {
    // json
    .json {
      .edit {
        padding-right: 12px;
        color: #018aff;
        font-size: 14px;
        position: relative;
        cursor: pointer;
      }
      .edit::before {
        content: "";
        width: 1px;
        height: 14px;
        background: #ededed;
        position: absolute;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
      }
      .delete {
        padding-left: 12px;
        font-size: 14px;
        color: #ff4d4f;
        cursor: pointer;
      }
      /deep/ {
        .el-table::before {
          display: none;
        }
        .el-table {
          .el-table__header {
            .el-table__cell {
              background: #f7f7f7;
              padding: 0;
              font-weight: normal;
              border-bottom: none;
            }
          }
          .el-table__cell {
            border-bottom: none;
            padding: 0;
            height: 34px;
            line-height: 34px;
          }
        }
      }
    }
    .struct-add {
      p {
        background: #ebf6ff;
        width: 160px;
        height: 34px;
        color: #0088fe;
        font-size: 14px;
        line-height: 34px;
        text-align: center;
        cursor: pointer;
        margin-top: 8px;
      }
    }
  }
}
</style>
