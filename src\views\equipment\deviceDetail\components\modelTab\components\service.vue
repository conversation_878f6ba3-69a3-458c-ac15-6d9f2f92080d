<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-14 14:47:00
-->
<template>
	<div class="model">
		<div class="model-table">
			<!-- 表格 -->
			<iot-table :columns="columns" :data="tableData" :loading="loading">
				<template slot="inputPara" slot-scope="scope">
					<el-tooltip
						class="item"
						effect="light"
						placement="top"
						popper-class="event-tooltip"
					>
						<!--  -->
						<template #content>
							<p :style="fn_formatter_style(scope.row)">
								{{ scope.row.inputPara }}
							</p>
						</template>
						<div class="flex">
							<p class="outputPara">{{ scope.row.inputPara }}</p>
							<p
								v-copy="scope.row.inputPara"
								class="output-copy color2"
							>
								复制
							</p>
						</div>
					</el-tooltip>
				</template>
				<template slot="outputPara" slot-scope="scope">
					<el-tooltip
						class="item"
						effect="light"
						placement="top"
						popper-class="event-tooltip"
					>
						<!--  -->
						<template #content>
							<p>{{ scope.row.outputPara }}</p>
						</template>
						<div class="flex">
							<p class="outputPara">{{ scope.row.outputPara }}</p>
							<p
								v-copy="scope.row.outputPara"
								class="output-copy color2"
							>
								复制
							</p>
						</div>
					</el-tooltip>
				</template>
				<!-- <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_open(scope.row)" class="color2">查看数据</p>
          </div>
        </template>-->
			</iot-table>
		</div>

		<div class="model-bottom" v-if="tableData && tableData.length > 0">
			<iot-pagination
				:pagination="pagination"
				@size-change="sizeChange"
				@current-change="currentChange"
			/>
		</div>
	</div>
</template>

<script>
import IotTable from '@/components/iot-table'
import IotPagination from '@/components/iot-pagination'
import { getServicesData } from '@/api/device'
import { fn_util__date_format } from '@/util/util'
export default {
	name: 'Service',
	components: {
		IotTable,
		IotPagination,
	},
	data() {
		return {
			loading: false,
			pageSource: [],
			tableData: [],
			columns: [
				{
					label: '时间',
					prop: 'time',
					// slotName: "classification",
				},
				{ label: '标识符', prop: 'identifier' },
				{ label: '服务名称', prop: 'serviceName' },
				{ label: '输入参数', prop: 'inputPara', slotName: 'inputPara' },
				{
					label: '输出参数',
					prop: 'outputPara',
					slotName: 'outputPara',
				},
				// { label: '操作', prop: 'operation', slotName: 'operation' }
			],
			pagination: {
				current: 1, // 当前页
				total: 0, // 记录条数
				pages: 0, // 总页数
				sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
				size: 10,
			},
			startTime: '',
			endTime: '',
			rangeDate: null,
		}
	},
	props: {
		productKey: {
			type: String,
		},
		tenant_id: {
			type: String,
		},
		deviceName: {
			type: String,
		},
		time: {
			type: [Number, String],
		},
	},
	watch: {
		time() {
			this.formatTime(this.time)
		},
		rangeDate: {
			deep: true,
			handler: function () {
				this.formatTime(this.time)
			},
		},
	},
	mounted() {
		if (this.time == 1) {
			this.formatTime(this.time)
		}
	},
	methods: {
		formatTime(type) {
			let format
			let { yy, MM, dd, hh, mm, ss, timestamp } = fn_util__date_format()
			this.endTime = `${yy}-${MM}-${dd} ${hh}:${mm}:${ss}`
			// timestamp 13位时间戳
			if (type == 1) {
				// 1小时
				let time = timestamp - 3600 * 1000
				format = fn_util__date_format(time)
			} else if (type == 2) {
				// 24小时
				let time = timestamp - 24 * 3600 * 1000
				format = fn_util__date_format(time)
			} else if (type == 3) {
				// 7天
				let time = timestamp - 7 * 24 * 3600 * 1000
				format = fn_util__date_format(time)
			} else if (type == 4) {
				// 自定义
				if (this.rangeDate && this.rangeDate.length > 0) {
					format = fn_util__date_format(this.rangeDate[0])
					let endFormat = fn_util__date_format(this.rangeDate[1])
					this.endTime = `${endFormat.yy}-${endFormat.MM}-${endFormat.dd} ${endFormat.hh}:${endFormat.mm}:${endFormat.ss}`
				} else return
			}
			this.startTime = `${format.yy}-${format.MM}-${format.dd} ${format.hh}:${format.mm}:${format.ss}`
			this.getData()
		},
		getData() {
			let params = {
				productKey: this.productKey,
				deviceName: this.deviceName,
				size: this.pagination.size,
				current: this.pagination.current,
				startTime: this.startTime,
				endTime: this.endTime,
				// type: this.eventType,
			}
			this.loading = true
			getServicesData(params)
				.then((res) => {
					if (res.code == 200) {
						this.tableData = res.data.records
						this.pagination.total = res.data.total
					} else {
						this.$newNotify.error({
							message: res.message,
						})
					}
				})
				.finally(() => {
					this.loading = false
				})
		},
		handleReset() {
			this.pagination.current = 1
			this.getData()
		},
		currentChange(data) {
			this.pagination.current = data
			this.getData()
		},
		sizeChange(data) {
			this.pagination.current = 1
			this.pagination.size = data
			this.getData()
		},
		fn_open() {
			this.$refs.add.add(this.thingModel)
		},
		fn_formatter_style(row) {
			if (row.inputPara.length > 1006) {
				return 'overflow: auto;height: 100%;max-height: 300px;'
			} else {
				return ''
			}
		},
	},
}
</script>

<style lang="scss" scoped>
.model {
	.model-top {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin: 18px 0px 18px 0px;
		// .top-left {
		// }
		.top-right {
			align-items: center;
		}
	}
	.model-table {
		.table-edit {
			display: flex;
			align-items: center;
			p {
				cursor: pointer;
			}
			p:nth-child(2) {
				margin: 0px 12px;
				width: 1px;
				height: 13px;
				border: 1px solid #ededed;
			}
		}
		.outputPara {
			width: 250px;
			overflow: hidden;
			text-overflow: ellipsis;
			white-space: nowrap;
			color: #515151;
			font-size: 14px;
		}
		.output-copy {
			cursor: pointer;
			margin-left: 8px;
		}
	}

	.model-bottom {
		text-align: right;
		margin-top: 18px;
	}
	.del-tips {
		width: 420px;
	}
}

/deep/ {
	.cm-s-idea {
		height: 60vh;
	}
}

/deep/ {
	.el-radio-button__inner {
		width: 110px;
	}
	.el-input__inner {
		border-radius: 0;
		height: 34px;
	}
	.el-textarea__inner {
		border-radius: 0;
		height: 100px;
		color: #515151;

		font-family: H_Medium;
	}
	.el-textarea__inner::placeholder {
		font-family: H_Regular;
		font-weight: normal;
	}
}
</style>
