<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-21 14:15:23
 * @LastEditors: hs
 * @LastEditTime: 2021-12-24 11:33:42
-->
<template>
	<div class="content-item flex">
		<div class="left">
			<span>{{ logItem.title }}</span>
			<span>{{ logItem.time }}</span>
		</div>
		<div class="right">
			<p>{{ logItem.character }}</p>
		</div>
	</div>
</template>
<script>
export default {
	name: 'contentItem',
	props: {
		logItem: {
			type: Object,
			default: () => {},
		},
	},
	data() {
		return {}
	},
	methods: {},
}
</script>
<style lang="scss" scoped>
.content-item {
	margin: 0 18px;
	padding: 18px 0;
	border-bottom: 1px solid #eeeff1;
	.left {
		width: 150px;
		span {
			width: 100%;
			display: list-item;
			list-style: none;
			&:first-child {
				color: #515151;
				font-size: 14px;
				font-weight: 400;
				margin-bottom: 8px;
			}
			&:last-child {
				width: 150px;
				color: #888888;
				font-size: 12px;
				font-weight: 400;
			}
		}
	}
	.right {
		width: 100%;
		padding-left: 18px;
		p {
			color: #515151;
			font-size: 12px;
			word-break: break-all;
			word-wrap: break-word;
		}
	}
}
</style>
