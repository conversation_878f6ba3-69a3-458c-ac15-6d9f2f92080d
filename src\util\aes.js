import CryptoJs from 'crypto-js'

const KEYS = 'TheKeyOfYcAIoTdx' // 16位, 密钥字符串

const KEY = CryptoJs.enc.Utf8.parse(KEYS) //  将解密字符串转为WordArray类型

export default {
  // 加密时调用该方法，data传入必须是string类型
  //  故，如果要加密object，需要先用JSON.stringify()将其转为string再传入
  encryptoByAES(data) {
    let myData = CryptoJs.enc.Utf8.parse(data)
    let uData = CryptoJs.AES.encrypt(myData, KEY, {
      mode: CryptoJs.mode.ECB, // 加密模式ECB
      padding: CryptoJs.pad.Pkcs7 // 填充方式
    })
    let encrypted = uData.toString() // 返回的是base64密文，是字符串类型
    return encrypted
  },
  
  // 解密，调用该方法时，传入的data是base64密文
  decryptoByAES(data) {
    if(data) {
      let uData = CryptoJs.AES.decrypt(data, KEY, {
        mode: CryptoJs.mode.ECB, // 加密模式ECB
        padding: CryptoJs.pad.Pkcs7 // 填充方式
      })
      let decrypted = uData.toString(CryptoJs.enc.Utf8)
      return decrypted
    } else {
      return ''
    }
  },
};