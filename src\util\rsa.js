import JSEncrypt from "jsencrypt";
export default function (str) {
  let pubKey =
    "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQC8+0Co5SSzPH4BZ3EAF6ziwzmCqC7iVgt7+UF22aq8kdXJHZTxW1auH74JP2h0CDeY+OUviQ9AjLONilSdTGf7N4GoKlIIA1rXoy64NtQt27KxGjZIGg48c9MPvNuxSx/n5YplgT0teoeVpt7SeyF5sydH1QPHtG5hDaRVEa+IHQIDAQAB"; //公钥
  let encryptStr = new JSEncrypt();
  encryptStr.setPublicKey(pubKey); // 设置 加密公钥
  let data = encryptStr.encrypt(str.toString()); // 进行加密
  return data;
}
