<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 15:31:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2021-11-25 14:03:46
-->
<template>
  <div class="iot-pagination">
    <el-pagination
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      :current-page="pagination.current"
      :page-sizes="pagination.sizes"
      :page-size="pagination.size"
      :pager-count ="pagination.pagerCount"
      :layout="layout"
      :total="pagination.total"
    ></el-pagination>
  </div>
</template>

<script>
export default {
  name: "IotPagination",
  props: {
    pagination: {
      type: Object,
      default: () => ({
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
        pagerCount: 7
      }),
    },
    layout: {
      type: String,
      default: "total, prev, pager, next, sizes, jumper",
    },
  },
  data() {
    return {};
  },
  methods: {
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.$emit("size-change", val);
    },
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.$emit("current-change", val);
    },
  },
};
</script>

<style lang="scss" scoped>
.iot-pagination {
  
  .el-pagination {
    margin-right: 16px;
    :deep(.el-input__inner) {
      border-radius: 0;
      height: 30px !important;
    }
    :deep(.el-pager li) {
      color: rgba(51, 51, 51, 0.85);
      min-width: 28px;
      height: 34px;
      line-height: 34px;
      font-size: 14px;
    }
    :deep(.el-pager li.active) {
    //   border: 1px solid;
      color: #1890ff;
    }
    :deep(.el-pagination__jump) {
      margin-left: 0;
    }
    :deep(.el-pagination__total) {
      height: 34px;
      line-height: 34px;
    }
    :deep(.btn-prev) {
      height: 34px;
      i {
        font-size: 14px;
      }
    }
    :deep(.btn-next) {
      height: 34px;
      i {
        font-size: 14px;
      }
    }
  }
}
</style>
