<template>
  <div id="main">
    <div class="info-content">
      <div class="info-title">
        <div>基础信息</div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>事件ID</span>
          <span>{{ obj.id || '--' }}</span>
        </div>
        <div class="item">
          <span>设备名称</span>
          <span>{{ obj.aliasName || '--' }}</span>
        </div>
        <div class="item">
          <span>DeviceName</span>
          <span>{{ obj.deviceName || '--' }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>ProductKey</span>
          <span>{{ obj.productKey || '--' }}</span>
        </div>
        <div class="item">
          <span>产品名称</span>
          <span>{{ obj.productName|| '--'  }}</span>
        </div>
        <div class="item">
          <span>所属项目</span>
          <span>{{ obj.projectName|| '--'  }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>事件类型</span>
          <span>{{ obj.alarmType == 1 ? '设备告警' : obj.alarmType == 2 ? '业务告警' : obj.alarmType == 3 ? '系统告警' : obj.alarmType == 4 ? '其他告警' : '--' }}</span>
        </div>
        <div class="item">
          <span>事件等级</span>
          <span>{{ obj.alarmLevel == 1 ? '紧急' : obj.alarmLevel == 2 ? '重要' : obj.alarmLevel == 3 ? '次重要' : obj.alarmLevel == 4 ? '提示' : '--'  }}</span>
        </div>
        <div class="item">
          <span>事件发生时间</span>
          <span>{{ obj.createTime || '--' }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>事件关闭时间</span>
          <span>{{ obj.closeTime || '--' }}</span>
        </div>
        <div class="item">
          <span>耗时</span>
          <span>{{ duration || '---' }}</span>
        </div>
        <div class="item">
          <span></span>
          <span></span>
        </div>
      </div>
    </div>

    <div class="bootom">
      <div>
        <div class="title">事件描述</div>
        <el-input
          placeholder="请输入内容"
          v-model="eventdesc"
          :disabled="true"
          :class="isShow?'custom-input':'custom-offinput'"
        >
        </el-input>
      </div>

      <div class="feedback">
        <div class="feedback_title">事件处理反馈</div>
        <div class="classification">解决方案分类</div>
        <el-select v-model="value" :disabled="isShow" placeholder="请选择" :class="isShow?'custom-input':'custom-offinput'">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div class="classification">处理方案来源</div>
        <el-select v-model="value2" :disabled="isShow" placeholder="请选择" :class="isShow?'custom-input':'custom-offinput'">
          <el-option
            v-for="item in options2"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div class="classification">事件处理反馈</div>
        <el-input
          type="textarea"
          :rows="4"
          placeholder="请填写事件原因，处理方案等反馈信息"
          v-model="textarea"
          style="margin-top: 8px"
          maxlength="500"
          show-word-limit
          :disabled="isShow"
          :class="isShow?'custom-input':'custom-offinput'"
        >
        </el-input>
      </div>

      <div class="btn">
        <iot-button :text="isShow?'已关闭':'确认已处理'" @search="fn_handle" :type="isShow?'lightblack':'default'" :disabled="isShow" :class="isShow?'dis':''"></iot-button>
      </div>
    </div>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button";
import { handleEvents } from "@/api/monitorMaintain";
export default {
  components: {
    IotButton,
  },
  data() {
    return {
      input: "",
      value: "1",
      value2: "2",
      options: [
        {
          value: "1",
          label: "设备自行恢复正常",
        },
        {
          value: "2",
          label: "人为干预反馈解决",
        },
      ],
      options2: [
        {
          value: "1",
          label: "钉钉",
        },
        {
          value: "2",
          label: "web",
        },
      ],
      textarea: "",
      obj: {},
      isShow:false,
      eventdesc:""
    };
  },
  created() {
    this.obj = this.$route.query;
    console.log(this.$route.query);
    
      this.eventdesc = this.$route.query.message + '-' + this.$route.query.content 
    
    if(this.$route.query.eventStatus==1){//事件为关闭状态时
      this.isShow = true
      this.textarea = this.$route.query.processingDetails
      this.value = this.$route.query.processingPlan
      // this.value2 = this.$route.query.processingSource
    }
  },
  computed: {
    duration() {
      if (this.obj.createTime && this.obj.closeTime) {
        const start = new Date(this.obj.createTime);
        const end = new Date(this.obj.closeTime);
        const diff = end - start;
        const minutes = Math.floor(diff / 1000 / 60);
        const seconds = Math.floor((diff / 1000) % 60);
        return `${minutes} 分钟 ${seconds} 秒`;
      }
      
      return '--'; // 默认值
    },
},
  methods: {
    fn_handle() {
      let data = {
        ...this.obj,
        processingPlan: this.value,
        processingSource: this.value2,
        processingDetails: this.textarea,
      };
      handleEvents(data).then((res) => {
        if (200 == res.code) {
          this.$newNotify.success({
            message: res.message,
          });
          this.$router.go(-1);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
  },
};
</script>

    


<style lang="scss" scoped>
#main {
  width: 100%;
  padding: 0px 32px 0px 32px;
  .info-content {
    box-shadow: 0px 8px 18px 0px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 20px 32px;
    margin-top: 16px;
    outline: 1px solid #eeeff1;
    .info-title {
      display: flex;
      justify-content: space-between;

      .info-edit {
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: H_Regular;

        img {
          width: 12px;
          height: 12px;
          margin-right: 6px;
        }

        .el-radio-group {
          margin-right: 24px;

          .el-radio-button {
            // width: 80px;
            text-align: center;
          }
        }
      }
    }
    .info-row {
      .item {
        flex: 1;
        padding-top: 20px;
        width: 50%;
        display: flex;
        height: 100%;
        font-size: 14px;
        font-weight: 400;

        span {
          height: 16px;
          line-height: 16px;
          display: block;

          &:first-child {
            color: #999999;
            width: 100px;
            text-align: right;
            flex-shrink: 0;
          }

          &:last-child {
            // width: 100%;
            flex: 1;
            color: #515151;
            margin-left: 48px;
          }
        }

        .item-span {
          padding-right: 22px;
          width: calc(100% - 100px - 22px);
          color: #515151;
          margin-left: 48px;
          word-wrap: break-word;
        }
      }
    }
  }
  .bootom {
    margin-top: 32px;
    .title {
      font-weight: 400;
      font-size: 14px;
      line-height: 16.41px;
      font-family: HarmonyOS Sans SC;
    }
    :deep(.el-input) {
      margin-top: 8px;
      height: 34px;
    }
    .feedback {
      margin-top: 34px;
      .feedback_title {
        font-weight: 600;
        font-size: 16px;
        line-height: 16px;
        color: #333333;
      }
      .classification {
        font-size: 14px;
        font-weight: 400;
        line-height: 16.41px;
        color: #515151;
        margin-top: 24px;
      }
    }
    .btn {
      margin-top: 32px;
      .dis{
        cursor: not-allowed;
      }
    }
    :deep(.custom-offinput .el-input__inner) {
      /* 移除边框的圆角 */
      border-radius: 0;
      font-family: H_Medium;
    }
    :deep(.custom-input .el-input__inner) {
      /* 移除边框的圆角 */
      border-radius: 0;
      background-color: #f2f2f2;
      font-family: H_Medium;
    }
    :deep(.custom-offinput .el-textarea__inner) {
      border-radius: 0;
      font-family: H_Medium;
    }
    :deep(.custom-input .el-textarea__inner) {
      background-color: #f2f2f2;
      border-radius: 0;
      font-family: H_Medium;
    }
  }
}
</style>