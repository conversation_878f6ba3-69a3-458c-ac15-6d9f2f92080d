import Vue from "vue";

// 自定义指令   节流       请求时 如果响应慢  效果不大
const pointDoms = []; // 使用这个指令的所有DOM对象
Vue.directive("throttle", {
  inserted(el, binding) {
    pointDoms.push(el); // 存储使用这个指令的DOM
    el.addEventListener("click", () => {
      // 禁用所有使用这个指令的DOM结构点击事件
      pointDoms.forEach((pointItem) => {
        pointItem.style.pointerEvents = "none";
      });
      setTimeout(() => {
        // 启动所有使用这个指令的DOM结构点击事件
        pointDoms.forEach((pointItem) => {
          pointItem.style.pointerEvents = "auto";
        });
      }, binding.value || 1000);
    });
  },
});
