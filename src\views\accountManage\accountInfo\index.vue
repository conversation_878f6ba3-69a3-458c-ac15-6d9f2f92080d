<!--
 * @Author: your name
 * @Date: 2021-12-28 19:14:28
 * @LastEditTime: 2022-02-28 11:20:26
 * @LastEditors: Please set LastEditors
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \tenant-web\src\views\accountManage\accountInfo\index.vue
-->
<template>
  <div class="account-info">
    <div class="info-top">
      <div class="info-tab">
        <el-tabs v-model="tabIndex"
                 type="border-card">
          <el-tab-pane label="基本信息"
                       name="0">
          </el-tab-pane>
          <el-tab-pane label="修改密码"
                       name="1">
          </el-tab-pane>
        </el-tabs>
      </div>
      <div class="info-content">
        <basic-info v-if="tabIndex == '0'"></basic-info>
        <update-secret v-if="tabIndex == '1'"></update-secret>
      </div>
    </div>
  </div>
</template>
<script>
import basicInfo from './components/basicInfo'
import updateSecret from './components/updateSecret'
export default {
  name: 'accountInfo',
  components: {
    basicInfo,
    updateSecret,
  },
  data() {
    return {
      tabIndex: '0',
    }
  },
  created() {
    if (this.$route.query.type) {
      this.tabIndex = this.$route.query.type
    } else {
      this.tabIndex = '0'
    }
  },
}
</script>
<style lang="scss" scoped>
.account-info {
  padding: 0px 32px 0px 32px;
  .info-top {
    margin-top: 18px;
    .info-tab {
      :deep(.el-tabs__item) {
        height: 48px;
        line-height: 48px;
        color: rgba(51, 51, 51, 1);
      }
      :deep(.el-tabs--border-card > .el-tabs__header) {
        background-color: #edf1f7;
        border-bottom: none;
      }
      :deep(.el-tabs--border-card > .el-tabs__content) {
        padding: 0px;
      }
      :deep(.el-tabs__item) {
        padding: 0px 35px;
        height: 42px;
        line-height: 42px;
      }
      :deep(.el-tabs--border-card) {
        box-shadow: none;
        border: none;
      }
      :deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item) {
        color: rgba(51, 51, 51, 1);
        font-family: HarmonyOS Sans SC;
        font-style: normal;
        font-weight: normal;
        font-size: 16px;
        letter-spacing: 1px;
      }
      :deep(.el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:nth-child(2)) {
        padding-left: 35px;
      }
      :deep(.el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child) {
        padding-right: 35px;
      }
      :deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active::after) {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
        height: 3px;
        background-color: #515151;
        z-index: 1;
      }
      :deep(.is-active) {
        color: rgba(51, 51, 51, 1);
      }
    }
  }
}
</style>
