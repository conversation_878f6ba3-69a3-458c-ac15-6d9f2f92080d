<template>
  <div class="rule-edit">
    <div class="title flex">
      <div class="flex">
        <p>规则描述</p>
        <span>{{ info.description }}</span>
      </div>
      <div class="edit flex" @click="editInfo">
        <img src="~@/assets/images/index/edit-icon.png" alt="" />
        <span>编辑</span>
      </div>
    </div>
    <h5>联动规则</h5>

    <el-form ref="form" :model="form" :validate-on-rule-change="false">
      <!-- ----------------------------------------------------------------------------------触发器 -->
      <div class="form-title flex">
        <p>触发器</p>
        <span>*</span>
        <el-tooltip
          placement="top-start"
          effect="light"
          popper-class="ruleEdit-tooltip"
        >
          <div slot="content">触发器必选，满足触发条件中任意一个即可触发。</div>
          <img src="~@/assets/images/index/doubt-icon.png" alt="" />
        </el-tooltip>
      </div>
      <div
        class="flex"
        v-for="(item, index) in form.trigger"
        :key="item.domKey"
      >
        <div class="form-content">
          <trigger
            :attr="item"
            :index="index"
            :prop="`trigger.${index}`"
            :tenant_id="tenant_id"
            :productList="productList"
            @formSet="formSet"
          >
          </trigger>
        </div>
        <div class="form-content-delete">
          <span
            :style="{
              color: form.trigger.length <= 1 ? '#BFBFBF' : '',
            }"
            @click="handleDelete('trigger', index)"
            >删除</span
          >
        </div>
      </div>

      <!--  -->
      <div class="form-btn" @click="handleAdd('trigger')">
        <span>+ 新增触发器</span>
      </div>
      <!-- --------------------------------------------------------------------------------条件 -->
      <div class="form-title flex">
        <p>执行条件</p>
        <el-tooltip
          placement="top-start"
          effect="light"
          popper-class="ruleEdit-tooltip"
        >
          <div slot="content">
            执行条件非必选，需要满足所有执行条件才会被继续处理。
          </div>
          <img src="~@/assets/images/index/doubt-icon.png" alt="" />
        </el-tooltip>
      </div>
      <div
        class="flex"
        v-for="(item, index) in form.condition"
        :key="item.domKey"
      >
        <div class="form-content">
          <condition
            :attr="item"
            :index="index"
            :prop="`condition.${index}`"
            :tenant_id="tenant_id"
            :productList="productList"
            @formSet="formSet"
          />
        </div>
        <div class="form-content-delete">
          <span @click="handleDelete('condition', index)">删除</span>
        </div>
      </div>

      <div class="form-btn" @click="handleAdd('condition')">
        <span>+ 新增执行条件</span>
      </div>
      <!-- ------------------------------------------------------------------------------------动作 -->
      <div class="form-title flex">
        <p>执行动作</p>
        <span>*</span>
        <el-tooltip
          placement="top-start"
          effect="light"
          popper-class="ruleEdit-tooltip"
        >
          <div slot="content">执行动作必选，同时执行所有动作。</div>
          <img src="~@/assets/images/index/doubt-icon.png" alt="" />
        </el-tooltip>
      </div>
      <div class="flex" v-for="(item, index) in form.action" :key="item.domKey">
        <div class="form-content">
          <action
            :attr="item"
            :index="index"
            :prop="`action.${index}`"
            :tenant_id="tenant_id"
            :productList="productList"
            :isShowAlarm="index == occupy.index ? false : occupy.flag"
            :ruleID="ruleID"
            @formSet="formSet"
            @actionModeChange="actionModeChange"
          />
        </div>
        <div class="form-content-delete">
          <span
            :style="{
              color: form.action.length <= 1 ? '#BFBFBF' : '',
            }"
            @click="handleDelete('action', index)"
            >删除</span
          >
        </div>
      </div>

      <div class="form-btn" @click="handleAdd('action')">
        <span>+ 新增执行动作</span>
      </div>
    </el-form>

    <div class="action flex">
      <el-button type="primary" @click="handleSubmit">保存</el-button>
      <el-button @click="handleBack">取消</el-button>
    </div>
    <edit ref="edit" :ruleID="ruleID" @updateInfo="updateInfo" />
  </div>
</template>
<script>
// import { getProductList } from "@/api/product";
import { getProductSelect } from "@/api/onlineDubug";
import { compareData, fn_util__date_format } from "@/util/util";
import trigger from "../trigger";
import condition from "../condition";
import action from "../action";
import { set as lodashSet } from "lodash";
import { getRuleSceneUpdateRule } from "@/api/sceneLink";
import { getRuleInfo } from "@/api/sceneLink";
import edit from "../edit";
export default {
  name: "rule-edit",
  data() {
    return {
      info: {
        name: "", //规则名
        description: "", //规则描述
      },
      tenant_id: "",
      ruleID: "",
      ruleContent: "", //返回的规则字符串
      form: {
        trigger: [{ domKey: "trigger1" }], //触发器
        condition: [{ domKey: "condition1" }], //执行条件
        action: [{ domKey: "action1" }], // 执行动作
      },
      productList: [],
      occupy: {
        flag: false, //默认不被占用
        index: -1,
      },
    };
  },
  components: { trigger, condition, action, edit },
  mounted() {
    this.tenant_id = this.Encrypt.decryptoByAES(
      localStorage.getItem("tenant_id")
    );
    this.ruleID = this.$route.query.id;
    this.getDetail();
  },
  methods: {
    editInfo() {
      this.$refs.edit.open(this.info);
    },
    updateInfo(data) {
      this.info = data;
    },
    getDetail() {
      getRuleInfo({ id: this.ruleID })
        .then((res) => {
          if (res.code == 200) {
            //路由信息
            let data = {
              id: res.data.id,
              title: res.data.name,
            };
            this.$store.dispatch("setLayoutInfo", data);
            //
            this.info = res.data;
            if (!res.data.ruleContent) return;
            this.ruleContent = JSON.parse(res.data.ruleContent);
            this.$nextTick(() => {
              this.form.action = this.ruleActionFormat(
                this.ruleContent.actions
              );
              this.form.condition = this.ruleConditionFormat(
                this.ruleContent.conditions
              );
              this.form.trigger = this.ruleTriggerFormat(
                this.ruleContent.triggers
              );
            });
            // console.log(this.form);
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          this.getProductData();
        });
    },
    // 返回trigger 数据重组
    ruleTriggerFormat(list) {
      let result = list.map((item, index) => {
        let object = {};
        let uri = item.uri.split("/");
        let triggerMode = "trigger";
        let triggerShape = uri[2];
        let deviceName = "";

        let { productKey } = item.params;
        if (triggerShape == "deviceStatusChange") {
          // 上下线
          let propertyName = item.params.status;
          object = {
            propertyName,
          };
        } else if (triggerShape == "property" || triggerShape == "event") {
          // 属性  / 事件
          let { propertyName } = item.params;
          if (propertyName !== "_all") {
            // 不是全部属性 或全部事件
            let { propMode, compareType, compareValue } = item.params;
            let objectList = [];
            if (propMode != "object") {
              compareValue = item.params.compareValue;
              //   下方的三目运算符   回显 数组值   提交时 后台处理不了字符串数组  暂时由前端过滤    执行条件/执行动作 也有此处理
              object = {
                compareType,
                compareValue:
                  propMode != "array"
                    ? compareValue
                    : JSON.stringify(compareValue),
              };
            } else {
              //
              let keys = Object.keys(compareValue);
              objectList = keys.map((item) => {
                return {
                  id: item,
                  value: compareValue[item],
                };
              });
              object = {
                objectList,
                compareType,
              };
            }
          }
          if (item.params.eventCode) {
            // 事件
            let eventCode = item.params.eventCode;
            // 设计 问题   propertyName 与eventCode值交换
            object["eventCode"] = propertyName;
            propertyName = eventCode;
            // object = {
            //   ...object,
            //   eventCode,
            // };
          }
          object = {
            ...object,
            propertyName,
          };
        }
        // 是否为全部设备
        if (uri[1] == "device") {
          // 具体设备
          deviceName = item.params.deviceName;
        } else {
          // 全部设备
          deviceName = "_all";
        }

        object = {
          ...object,
          triggerMode,
          productKey,
          deviceName,
          triggerShape,
          domKey: `trigger${index}`,
        };
        return object;
      });
      return result;
    },
    // 返回condition 数据重组
    ruleConditionFormat(list) {
      let result = list.map((item, index) => {
        let uri = item.uri.split("/");
        let condition = uri[1];
        let object = {};
        if (condition == "device") {
          condition = "status";
          let propetyMode = uri[2];
          let objectList = [];
          let {
            productKey,
            deviceName,
            propertyName,
            compareType,
            compareValue,
            propMode,
          } = item.params;
          if (propMode == "object") {
            let keys = Object.keys(compareValue);
            objectList = keys.map((item) => {
              return {
                id: item,
                value: compareValue[item],
              };
            });
            object = {
              objectList,
            };
          } else if (propMode == "array") {
            object = {
              compareValue: JSON.stringify(compareValue),
            };
          } else {
            object = {
              compareValue,
            };
          }
          object = {
            ...object,
            productKey,
            deviceName,
            propertyName,
            compareType,
            propMode,
          };
          if (propetyMode == "event") {
            let eventCode = item.params.eventCode;
            // 设计 问题   propertyName 与eventCode值交换
            object["eventCode"] = object["propertyName"];
            object["propertyName"] = eventCode;
            // object = {
            //   ...object,
            //   eventCode,
            // };
          }
        } else if (condition == "timeRange") {
          let { beginDate, endDate } = item.params;
          object = {
            beginDate,
            endDate,
          };
        }
        object = {
          ...object,
          condition,
          domKey: `condition${index}`,
        };
        return object;
      });
      return result;
    },
    // 返回action数据重组
    ruleActionFormat(list) {
      // action
      let result = list.map((item, index) => {
        let uri = item.uri.split("/");
        let actionMode = uri[1];
        let object = {};
        if (actionMode == "event") {
          // 告警
          // 回显告警占用
          this.occupy.flag = true;
          this.occupy.index = index;
        } else if (actionMode == "scene") {
          // 规则
          let { ruleId, switchStatus } = item.params;
          object = {
            ruleId,
            switchStatus,
          };
        } else if (actionMode == "device") {
          let { productKey, deviceName, propMode } = item.params;
          let propertyValue;
          let propertyName;
          let serviceInList;
          // 判断是否为服务  identity
          let keys = Object.keys(item.params.propertyItems);

          if (item.params.identity) {
            let { propertyItemsType } = item.params;
            // 服务   服务需要循环
            propertyName = item.params.identity;
            serviceInList = keys.map((item2) => {
              let flag =
                propertyItemsType[item2] == "array" ||
                propertyItemsType[item2] == "object";
              return {
                id: item2,
                propertyValue: !flag
                  ? item.params.propertyItems[item2]
                  : JSON.stringify(item.params.propertyItems[item2]),
              };
            });
            object = {
              serviceInList,
              propertyName,
            };
          } else {
            // 属性  属性只有一个
            propertyName = keys[0];
            if (propMode == "object" || propMode == "array") {
              propertyValue = JSON.stringify(
                item.params.propertyItems[keys[0]]
              );
            } else {
              propertyValue = item.params.propertyItems[keys[0]];
            }
            object = {
              propertyValue,
              propertyName,
            };
          }
          object = {
            ...object,
            productKey,
            deviceName,
          };
        }
        object = {
          ...object,
          actionMode,
          domKey: `action${index}`,
        };
        return object;
      });
      return result;
    },

    // 执行动作 下的告警输出  是否被占用   { flag:是否被占用,index:占用项的下标}
    actionModeChange(data) {
      this.occupy = data;
    },
    formSet(data) {
      lodashSet(this.form, data.key, data.value);
    },
    // 获取产品列表
    getProductData() {
      // getProductList().then((res) => {
      //   if (res.code == 200) {
      //     this.productList = res.data.records;
      //   }
      // });
      getProductSelect().then((res) => {
        if (res.code == 200) {
          this.productList = res.data;
        }
      });
    },
    compare(type) {
      let index = compareData.findIndex((item) => item.type == type);
      let result = compareData[index];
      return result;
    },
    handleDelete(type, index) {
      if (type == "trigger" || type == "action") {
        if (this.form[type].length <= 1) return;
      }
      this.form[type] = this.form[type].filter((item, idx) => idx != index);
    },
    handleAdd(type) {
      this.form[type].push({
        domKey: `${type}${this.form[type].length + 1}`,
      });
    },
    formatData(object) {
      let actions = [];
      let conditions = [];
      let triggers = [];
      actions = this.actionFormat(object.action);
      triggers = this.triggerFormat(object.trigger);
      conditions = this.conditionFormat(object.condition);
      return {
        triggers,
        conditions,
        actions,
      };
    },
    conditionFormat(list) {
      let result = list.map((item) => {
        let uri = "";
        let params = {};
        let { condition } = item;
        if (condition == "status") {
          // 设备状态
          uri = `condition/device/property`;
          let { deviceName, productKey, propertyName, compareType, propMode } =
            item;
          let compareValue = "";
          if (propMode == "object") {
            compareValue = {};
            let { objectList } = item;
            objectList.map((item2) => {
              compareValue[item2.id] = item2.value;
              return item2;
            });
          } else if (propMode == "array") {
            compareValue = JSON.parse(`${item.compareValue}`);
          } else {
            compareValue = item.compareValue;
          }
          params = {
            productKey,
            deviceName,
            propertyName,
            compareType,
            compareValue,
            propMode,
          };
          if (item.eventCode) {
            // 事件
            uri = "condition/device/event";
            // 设计 问题   propertyName 与eventCode值交换
            params["eventCode"] = params["propertyName"];
            params["propertyName"] = item.eventCode;
          }
        } else if (condition == "timeRange") {
          // 时间范围
          uri = `condition/timeRange`;
          let { beginDate, endDate } = item;
          let start = fn_util__date_format(beginDate);
          let end = fn_util__date_format(endDate);
          let startTime = `${start.yy}-${start.MM}-${start.dd} ${start.hh}:${start.mm}:${start.ss}`;
          let endTime = `${end.yy}-${end.MM}-${end.dd} ${end.hh}:${end.mm}:${end.ss}`;
          params = {
            beginDate: startTime,
            endDate: endTime,
          };
        }
        return {
          uri,
          params,
        };
      });
      result = result.filter((item) => item.uri != "");
      return result;
    },
    triggerFormat(list) {
      let result = list.map((item) => {
        let uri = "";
        let params = {};
        let { deviceName, productKey, propertyName, triggerShape } = item;
        if (!triggerShape) return { uri, params };
        if (triggerShape !== "deviceStatusChange") {
          // 属性/事件触发
          if (propertyName !== "_all") {
            // 具体属性/事件
            let { compareType, propMode } = item;
            let compareValue = "";
            if (propMode == "object") {
              let { objectList } = item;
              compareValue = {};
              objectList.map((item2) => {
                compareValue[item2.id] = item2.value;
                return item2;
              });
            } else if (propMode == "array") {
              compareValue = JSON.parse(`${item.compareValue}`);
            } else {
              compareValue = item.compareValue;
            }
            params = {
              productKey,
              propertyName,
              compareType,
              compareValue,
              propMode,
            };
            if (triggerShape == "event") {
              let { eventCode } = item;
              // 设计 问题   propertyName 与eventCode值交换
              params["eventCode"] = params["propertyName"];
              params["propertyName"] = eventCode;
            }
          } else {
            // 全部属性
            params = {
              productKey,
            };
            if (triggerShape == "event") {
              params["eventCode"] = "_all";
            } else {
              params["propertyName"] = "_all";
            }
          }
        } else {
          // 上下线触发 propertyName
          params = {
            productKey,
            status: propertyName,
          };
        }
        if (item.deviceName !== "_all") {
          // 不是全部设备
          uri = `trigger/device/${triggerShape}`;
          params["deviceName"] = deviceName;
        } else {
          uri = `trigger/product/${triggerShape}`;
        }
        return {
          uri,
          params,
        };
      });
      result = result.filter((item) => item.uri != "");
      return result;
    },
    actionFormat(list) {
      let result = list.map((item) => {
        let uri = "";
        let params = {};
        if (item.actionMode == "event") {
          // 告警
          uri = `action/${item.actionMode}/triggerAlarm`;
        } else if (item.actionMode == "scene") {
          // 规则
          uri = `action/${item.actionMode}/trigger`;
          let { switchStatus, ruleId } = item;
          params = {
            switchStatus,
            ruleId,
          };
        } else {
          // 设备输出
          uri = `action/${item.actionMode}/setProperty`;
          let {
            productKey,
            deviceName,
            propertyName: identity,
            propMode,
          } = item;
          let propertyItems = {};
          let propertyItemsType = {};
          if (item.serviceInList && item.serviceInList.length > 0) {
            // 服务
            // propertyItems['identity'] = item.propertyName
            for (let i = 0; i < item.serviceInList.length; i++) {
              if (item.serviceInList[i].propertyValue) {
                let serviceInListValue = item.serviceInList[i].propertyValue;
                // // 时间特殊处理   输入为10 为时间戳
                propertyItemsType[item.serviceInList[i].id] =
                  item.serviceInList[i].data.type;
                if (
                  item.serviceInList[i].data.type == "object" ||
                  item.serviceInList[i].data.type == "array"
                ) {
                  propertyItems[item.serviceInList[i].id] = JSON.parse(
                    `${serviceInListValue}`
                  );
                } else {
                  propertyItems[item.serviceInList[i].id] = serviceInListValue;
                }
              }
            }
            params["identity"] = identity;
            params = {
              ...params,
              propertyItemsType,
            };
          } else {
            // 属性
            if (propMode == "array" || propMode == "object") {
              propertyItems[item.propertyName] = JSON.parse(
                `${item.propertyValue}`
              );
            } else {
              propertyItems[item.propertyName] = item.propertyValue;
            }
            params = {
              ...params,
              propMode,
            };
          }
          params = {
            ...params,
            productKey,
            deviceName,
            propertyItems,
          };
        }
        return {
          uri,
          params,
        };
      });
      return result;
    },
    handleSubmit() {
      console.log(this.form);
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (this.form.action.length <= 0) {
            this.$newNotify.warning({
              message: "请设置执行动作",
            });
            return;
          }
          let result = this.formatData(this.form);
          let targetId = [];
          this.form.action.filter((item) => {
            if (item.actionMode == "scene") {
              targetId.push(item.ruleId);
            }
            return false;
          });
          getRuleSceneUpdateRule({
            id: this.ruleID,
            childList: targetId.join(","),
            ruleContent: JSON.stringify(result),
          }).then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.$router.go(-1);
            } else {
              this.$newNotify.warning({
                message: res.message,
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    handleBack() {
      this.$router.go(-1);
    },
  },
};
</script>
<style lang="scss" scoped>
.rule-edit {
  width: 100%;
  .title {
    height: 58px;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #e4e7ec;
    p {
      width: 160px;
      color: #999999;
      font-size: 14px;
    }
    span {
      color: #515151;
      font-size: 14px;
    }
    .edit {
      flex-shrink: 0;
      font-size: 14px;
      align-items: center;
      cursor: pointer;
      span {
        color: #018aff;
      }
    }
    img {
      width: 12px;
      height: 12px;
      margin-right: 6px;
    }
  }
  h5 {
    color: #333333;
    font-weight: 500;
    font-size: 16px;
    padding: 20px 0 10px;
  }
  .form-title {
    align-items: center;
    color: #515151;
    font-size: 14px;
    font-weight: normal;
    padding: 8px 0;
    span {
      color: #ff4d4f;
    }
    img {
      width: 14px;
      height: 14px;
      margin-left: 8px;
    }
  }
  .form-content {
    width: calc(100% - 46px);
    background: #fbfbfb;
    border: 1px dashed #e4e7ec;
    padding: 14px 14px 6px;
    margin-bottom: 10px;
  }
  .form-content-delete {
    width: 46px;
    flex-shrink: 0;
    text-align: center;
    padding-top: 14px;
    color: #018aff;
    font-size: 14px;
    text-align: right;
    span {
      cursor: pointer;
    }
  }
  .form-btn {
    width: 160px;
    height: 36px;
    line-height: 36px;
    text-align: center;
    background: #ebf6ff;
    margin: 12px 14px 24px;
    color: #0088fe;
    font-size: 14px;
    font-weight: normal;
    font-family: H_Regular;
    cursor: pointer;
  }
  .action {
    align-items: center;
    padding: 18px 0 24px;
  }
  :deep(.el-button) {
    border-radius: 0;
    width: 76px;
    height: 34px;
    line-height: 34px;
    padding: 0;
  }
  :deep(.el-button--primary) {
    background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
  }
  :deep(.el-button--default) {
    margin-left: 14px;
  }
}
</style>
<style lang="scss">
.ruleEdit-tooltip {
  border-radius: 0;
  border: 1px solid #e4e7ec !important;
  background: #ffffff !important;
  backdrop-filter: blur(4px);
  padding: 14px 18px;
  box-shadow: 0px 3px 8px #e6e6e6;
  .popper__arrow {
    border-right-color: #e4e7ec !important;
    left: -10px !important;
    border-width: 10px;
  }
  .popper__arrow::after {
    bottom: -10px !important;
    border-width: 10px;
  }

  .tips-password {
    p {
      align-items: center;
      padding-bottom: 8px;
      font-family: H_Medium;
      img {
        width: 14px;
      }
      span {
        padding-left: 8px;
        color: #515151;
        font-size: 13px;
      }
    }
    p:last-child {
      padding-bottom: 0;
    }
  }
}
</style>
