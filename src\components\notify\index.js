/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-11 10:00:31
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-12 20:35:10
 */
import Vue from 'vue'

export const notifyError = (param) => {
  Vue.prototype.$notify({
    dangerouslyUseHTMLString: true,
    duration: 2000,
    iconClass: 'notify-error',
    ...param
  })
}

export const notifySuccess = (param) => {
  Vue.prototype.$notify({
    dangerouslyUseHTMLString: true,
    duration: 2000,
    iconClass: 'notify-success',
    ...param
  })
}

export const notifyWarning = (param) => {
  Vue.prototype.$notify({
    dangerouslyUseHTMLString: true,
    duration: 2000,
    iconClass: 'notify-warning',
    ...param
  })
}