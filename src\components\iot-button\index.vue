<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 14:38:30
 * @LastEditors: hs
 * @LastEditTime: 2021-11-08 21:29:43
-->
<template>
  <span
    class="iot-btn"
    :class="[type ? 'iot-button-' + type : '', { 'iot-button-disabled': disabled }]"
    v-throttle="500"
    @click="fn_search"
    :style="{ backgroundColor: bgcolor }"
    :disabled="disabled"
    >{{ text }}
  </span>
</template>

<script>
export default {
  name: "Iot-btn",
  props: {
    text: {
      type: String,
      default: "搜索",
    },
    bgcolor: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "default",
    },
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  methods: {
    fn_search() {
      if (!this.disabled) {
        this.$emit("search");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.iot-btn {
  text-align: center;
  // color: #fff;
  // background: linear-gradient(180deg, #0088fe, #006eff 100%);
  // border-radius: 2px;
  cursor: pointer;
  padding: 7px 23px;
  display: inline-block;
  // font-family: H_Black;
  font-size: 14px;
  font-weight: normal;
  // letter-spacing: 2px;
  // margin-left: 14px;
}
.iot-button-default {
  background: linear-gradient(180deg, #0088fe, #006eff 100%);
  color: #fff;
  border: 1px solid #0088fe;
}
.iot-button-grey {
  background: #bfbfbf;
  color: #fff;
}
.iot-button-white {
  background: #fff;
  color: #333333;
  border: 1px solid #eeeff1;
}

.iot-button-lightblack {
  background: #EBEBEB;
  color: #BFBFBF;
  border: 1px solid #EBEBEB;
}

.iot-button-warning{
  background: #eb5151;
  color: #f3f0f0;
  border: 1px solid #eb5151;
}
</style>
