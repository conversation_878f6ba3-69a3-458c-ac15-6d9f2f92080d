/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-23 14:25:31
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:12:38
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;
const manage = `${baseServer}/project`;
const deviceGroup = `${baseServer}/mqtt/device/group`

/**
 * @desc 创建项目
 * @params data
 * @returns
 */
export const addProject = (data) => {
  return request({
    url: `${manage}/save`,
    method: "post",
    data,
  });
};

/**
 * @desc 列表展示
 * @params params
 * @returns
 */
export const pageProject = (params) => {
  return request({
    url: `${manage}/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 项目基本信息查询
 * @params params
 * @returns
 */
export const infoProject = (params) => {
  return request({
    url: `${manage}/info`,
    method: "get",
    params,
  });
};

/**
 * @desc 修改项目基本信息
 * @params data
 * @returns
 */
export const updateProject = (data) => {
  return request({
    url: `${manage}/update`,
    method: "put",
    data,
  });
};

/**
 * @desc 删除项目基本信息
 * @params params
 * @returns
 */
export const deleteProject = (params) => {
  return request({
    url: `${manage}/remove`,
    method: "delete",
    params,
  });
};

/**
 * @desc 所有项目名称查询
 * @params params
 * @returns
 */
export const listProject = (params) => {
  return request({
    url: `${manage}/all`,
    method: "get",
    params,
  });
};

/**
 * @desc 获取设备分组表格信息
 * @params data
 * @returns
 */
export const getDeviceGroundList = (params) => {
  return request({
    url: `${deviceGroup}/page`,
    method: "get",
    params,
  });
};
/**
 * @desc 新增设备分组
 * @data 传入字段，name、introduce
 * @returns
 */
export const addDeviceGroup = (data) => {
  return request({
    url: `${deviceGroup}/add`,
    method: "post",
    data,
  });
};


/**
 * @desc 编辑设备分组
 * @data 传入字段，id、name、introduce
 * @returns
 */
export const editDeviceGroup = (data) => {
  return request({
    url: `${deviceGroup}/update`,
    method: "post",
    data,
  });
};

/**
 * @desc 删除设备分组
 * @data 传入字段，id
 * @returns
 */
export const deleteDeviceGroup = (params) => {
  return request({
    url: `${deviceGroup}/delete`,
    method: "get",
    params,
  });
};

/**
 * @desc 设备分组详情
 * @data 传入字段，id：所属分组id
 * @returns
 */
export const deviceGroupDetail = (params) => {
  return request({
    url: `${deviceGroup}/detail`,
    method: "get",
    params,
  });
};

/**
 * @desc 移除设备分组
 * @data 传入字段，id：所属分组id，deviceId：移除的设备ids, 多个以逗号分割
 * @returns
 */
export const Delete_DeviceGroup = (params) => {
  return request({
    url: `${deviceGroup}/removeDevice`,
    method: "get",
    params,
  });
};

/**
 * @desc 获取添加设备列表
 * @data 
 * @returns
 */
export const get_DeviceGroup = (params) => {
  return request({
    url: `${deviceGroup}/unassignedPage`,
    method: "get",
    params,
  });
};

/**
 * @desc 获取添加设备列表
 * @data 
 * @returns
 */
export const get_DeviceGroup2 = (params) => {
  return request({
    url: `${deviceGroup}/assignedPage`,
    method: "get",
    params,
  });
};

/**
 * @desc 添加设备进入分组
 * @data 传入字段，id：所属分组id，deviceId：设备id, 多个以逗号分割
 * @returns
 */
export const add_DeviceGroup = (data) => {
  return request({
    url: `${deviceGroup}/addDevice`,
    method: "post",
    data,
  });
};