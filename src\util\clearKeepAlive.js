export const removeKeepAliveCacheForVueInstance = function (vueInstance, path) {
  let key =
    vueInstance.$vnode.key ??
    vueInstance.$vnode.componentOptions.Ctor.cid +
      (vueInstance.$vnode.componentOptions.tag
        ? `::${vueInstance.$vnode.componentOptions.tag}`
        : "");
  let cache = vueInstance.$vnode.parent.parent.componentInstance.cache; // router-view 嵌套  需要多一级parent
  let keys = vueInstance.$vnode.parent.parent.componentInstance.keys;
  // 原来获取的key 无法找到对应 试图   通过传入的path指定
  key = path;
  if (cache[key]) {
    vueInstance.$destroy();
    delete cache[key];
    let index = keys.indexOf(key);
    if (index > -1) {
      keys.splice(index, 1);
    }
  }
};
