<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 20:24:24
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-02-16 15:59:53
-->
<template>
  <div class="iot-table">
    <div class="selection-text flex" v-if="columns[0].selectionText">
      <p>当前已选择{{ selecionData.length }}项数据。</p>
      <p
        v-if="columns[0].isShowdelete"
        class="color2"
        @click="fn_del_selection_data"
        v-throttle
      >
        批量删除
      </p>
      <slot name="multSelectText"></slot>
    </div>
    <el-table
      ref="table"
      empty-text=" "
      :data="data"
      v-loading="loading"
      element-loading-spinner="el-icon-loading"
      style="width: 100%"
      v-bind="$attrs"
      v-on="$listeners"
      :header-cell-style="{ background: '#F7F7F7' }"
      :row-style="{ height: toVW(48) }"
      @selection-change="handleSelectionChange"
    >
      <template v-for="item in columns">
        <el-table-column
          v-if="item.type"
          :type="item.type"
          :key="item.type"
          :width="item.width"
          :selectable="selectable"
          align="center"
        ></el-table-column>
        <el-table-column
          v-else
          :key="item.prop"
          :label="item.label"
          :prop="item.prop"
          :type="item.type"
          :width="item.width"
          :fixed="item.fixed"
        >
          <template slot-scope="{ row }">
            <template v-if="item.slotName">
              <slot :name="item.slotName" :row="row"></slot>
            </template>
            <template v-else>
              <span>{{ row[item.prop] }}</span>
            </template>
          </template>
        </el-table-column>
      </template>
      <template slot="empty">
        <slot name="empty">
          <div class="table-empty" v-if="!loading">
            <img src="~@/assets/images/empty/empty.png" alt />
          </div>
        </slot>
      </template>
    </el-table>
    <iot-dialog
      :width="columns[0].dialogWidth ? columns[0].dialogWidth : toVW(550)"
      :visible.sync="columns[0].visible"
      :title="columns[0].title"
      @callbackSure="fn_sure"
    >
      <template #body>
        <el-form>
          <el-form-item>
            <div class="del-tips">
              <!-- 删除该设备，设备删除后不可恢复，请确认是否删除该设备？ -->
              {{ columns[0].text }}
            </div>
          </el-form-item>
        </el-form>
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import IotDialog from "@/components/iot-dialog";
import toVW from "@/util/toVW.js";
export default {
  name: "IotTable",
  components: {
    IotDialog,
  },
  props: {
    columns: {
      type: Array,
      default: () => [
        {
          // 第一个对象，使用多选的时候使用
          type: "",
          selectionText: false,
          isShowdelete: true,
          title: "",
          text: "",
          visible: false,
          dialogWidth: toVW(600), // 弹窗宽度必传
        },
      ],
    },
    data: {
      type: Array,
      default: () => [],
    },
    loading: {
      type: Boolean,
      default: false,
    },
   
  },
  data() {
    return {
      selecionData: [],
    };
  },
  methods: {
    toVW,
    fn_sure() {
      this.$emit("del-callbackSure");
    },
    handleSelectionChange(data) {
      this.selecionData = data.map((item) => {
        return item.id;
      });
      this.$emit("selection-change", data);
    },
    // 多选删除
    fn_del_selection_data() {
      if (!this.selecionData.length) {
        this.$newNotify.warning({
          message: "请至少选择一项",
        });
        return;
      }

      this.$emit("selection-del", this.selecionData);
    },
    toggleSelect(row, select) {
      this.$refs.table.toggleRowSelection(row, select);
    },
    doLayout() {
      this.$nextTick(() => {
        this.$refs["table"].doLayout();
      });
    },
    selectable(row){
      if(row.unSelect){
        return false
      }else{
        return true
      }
    }
  },
};
</script>

<style lang="scss" scoped>
.iot-table {
  .selection-text {
    margin-bottom: 14px;
    p {
      font-size: 14px;
      font-weight: normal;
      &:nth-child(1) {
        margin-right: 24px;
        letter-spacing: 1px;
        color: #515151;
      }
      &:nth-child(2) {
        cursor: pointer;
      }
    }
  }
  /deep/ {
    .el-table thead {
      font-weight: 500 !important;
    }
    .el-table__row:hover {
      .el-table__cell {
        background-color: rgba(1, 138, 255, 0.08) !important;
      }
    }
    .el-table__empty-text {
      line-height: normal;
    }
    .table-empty {
      img {
        margin-top: 84px;
        margin-bottom: 28px;
      }
    }
    .el-table__header-wrapper {
      .el-table__cell {
        padding: 7px 0;
      }
    }

    // .el-loading-spinner {
    //   background: url('~@/assets/images/index/loading.svg') no-repeat;
    //   background-size: 48px 48px;
    //   width: 100%;
    //   height: 100%;
    //   position: relative;
    //   top: 50%;
    //   left: 45%;
    // }
    .el-table__fixed-right {
      .el-table__header {
        .el-table__cell {
          padding: 7px 0;
        }
      }
    }
  }
  .el-table::before {
    height: 0px;
  }
  .del-tips {
    width: 420px;
  }
}
</style>
