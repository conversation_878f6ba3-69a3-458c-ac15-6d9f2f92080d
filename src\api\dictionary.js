/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-10 17:39:13
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-19 15:09:43
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
// const dictionary_core = "/core/dict/child-list";
const dictionary_core = `${BASE_SERVER}/dict/child-list`;

/**
 * @desc 产品品类
 * @params
 * @returns
 */
export const getDicList = (params) => {
  return request({
    url: `${dictionary_core}?parentId=1457622213346750465`,
    method: "get",
    params,
  });
};

/**
 * @desc 节点类型
 * @params
 * @returns
 */
export const getDictNetList = (params) => {
  return request({
    url: `${dictionary_core}?parentId=1457606482320191489`,
    method: "get",
    params,
  });
};
/**
 * @desc 单位类型
 * @params
 * @returns
 */
export const getDictUnitList = (params) => {
  return request({
    url: `${dictionary_core}?parentId=1458415719388610562`,
    method: "get",
    params,
  });
};
