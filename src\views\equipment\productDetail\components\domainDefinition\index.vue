<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-15 17:20:13
-->
<template>
  <div class="project">
    <div class="project-top">
      <div class="top-left">
        <iot-button text="添加功能" @search="fn_open" />
        <iot-button text="导入物模型" type="white" @search="fn_import" />
        <iot-button text="查看物模型" type="white" @search="fn_see" />
      </div>
      <div class="top-right"></div>
    </div>

    <div class="project-table">
      <!-- 表格 -->
      <iot-table :columns="columns" :data="tableData">
        <template slot="classification" slot-scope="scope">
          <div class="flex">
            <span v-if="scope.row.classification == 0">属性</span>
            <span v-if="scope.row.classification == 1">事件</span>
            <span v-if="scope.row.classification == 2">服务</span>
          </div>
        </template>
        <template slot="type" slot-scope="scope">
          <div class="flex">
            <span v-if="scope.row.classification == 0">{{
              scope.row.data.type
            }}</span>
            <span v-else>--</span>
          </div>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_edit(scope.row)" class="color2">
              编辑
            </p>
            <p></p>
            <!-- <p @click="fn_del(scope.row)" class="color2">删除</p> -->
            <p @click="afterDelete(scope.row)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>

    <!-- <div class="project-bottom">
      <iot-pagination
        :pagination="pagination"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>-->
    <add
      ref="add"
      :productKey="productKey"
      :tenant_id="tenant_id"
      :UniqueCode="UniqueCode"
      @ok="handleReset"
    />
    <iot-dialog
      :visible.sync="visibleDelete"
      :title="titleDelete"
      width="550px"
      @callbackSure="fn_sure"
    >
      <template #body>
        <iot-form>
          <template #default>
            <el-form>
              <el-form-item>
                <div class="del-tips">
                  删除该功能后，对应该功能的设备数据将同步被删除，请确认是否删除该功能？
                </div>
              </el-form-item>
            </el-form>
          </template>
        </iot-form>
      </template>
    </iot-dialog>
    <iot-dialog
      comfirmText="导出"
      :visible.sync="visible"
      :title="title"
      width="580px"
      @callbackSure="fn_sure_export"
    >
      <template #body>
        <b-code-editor
          v-model="thingModel2"
          :theme="theme"
          :auto-format="false"
          ref="editor"
          :show-number="showNumber"
          :readonly="readonly"
          :lint="lint"
          height="60vh"
        />
      </template>
    </iot-dialog>
    <iot-dialog
      comfirmText="确定"
      :visible.sync="importVisible"
      :title="titleImport"
      :btnClass="btnClass"
      width="580px"
      @close="fn_close_import"
      @callbackSure="fn_sure_import"
    >
      <template #body>
        <iot-tips content="注：导入的物模型会覆盖原来的功能。" />
        <div class="import-title">上传物模型文件 <span>*</span></div>
        <iot-upload
          ref="iotUpload"
          label="选择文件"
          btnWidth="120"
          uploadTips="仅支持json类型的文件，文件大小不能超过512KB"
          :errorMsg="importErrorMsg"
          :action="importAction"
          :formData="importFormData"
          :autoUpload="false"
          :beforeUpload="importBefore"
          :success="importSuccess"
          :error="importError"
          @fileChange="importFileChange"
        />
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button";
// import IotPagination from "@/components/iot-pagination";
import add from "@/views/equipment/components/add";
import IotTable from "@/components/iot-table";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";
import IotUpload from "@/components/iot-upload";
import IotTips from "@/components/iot-tips";
import {
  productAbilityDetail,
  getProductDetail,
  productAbilityEdit,
  getUniqueCode,
  exportObjectModel,
  importPartUrl,
} from "@/api/product.js";

// const jsonData = `{"profile":{"productKey":"","version": "1.0"},"properties":[],"events":[],"services":[]}`
import { mapGetters } from "vuex";
export default {
  name: "Project",
  components: {
    IotButton,
    // IotPagination,
    add,
    IotTable,
    IotDialog,
    IotForm,
    IotUpload,
    IotTips,
  },
  data() {
    return {
      titleDelete: "确定删除该功能？",
      visibleDelete: false,
      dataSource: [],
      pageSource: [],
      tableData: [],
      columns: [
        {
          label: "功能类型",
          prop: "classification",
          slotName: "classification",
        },
        { label: "功能名称", prop: "name" },
        { label: "标识符", prop: "id" },
        { label: "数据类型", prop: "data", slotName: "type" },
        // { label: "数据定义", prop: "" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      thingModel: {
        profile: {
          productKey: "",
          version: "1.0",
        },
        properties: [],
        events: [],
        services: [],
      },
      UniqueCode: "", //唯一标识
      visible: false,
      title: "查看物模型",
      record: {},
      //编辑器配置
      showNumber: true,
      lint: true,
      readonly: true,
      wrap: true,
      theme: "idea",
      thingModel2: JSON.stringify(
        JSON.parse(
          `{"profile":{"productKey":"${this.productKey}","version": "1.0"},"properties":[],"events":[],"services":[]}`
        ),
        null,
        2
      ),
      titleImport: "导入物模型",
      importVisible: false,
      importAction: importPartUrl,
      importFormData: {},
      importErrorMsg: "",
      btnClass: "grey",
    };
  },
  props: {
    productKey: {
      type: String,
    },
    tenant_id: {
      type: String,
    },
    productTitle: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters(["layoutInfo"]),
    jsonData() {
      return `{"profile":{"productKey":"${this.productKey}","version": "1.0"},"properties":[],"events":[],"services":[]}`;
    },
  },
  created() {
    this.fn_get_product_detail();
  },
  mounted() {
    if (this.productKey) {
      this.http_get_detail();
      this.getUniqueCode();
    }
  },
  watch: {
    productKey(val) {
      if (val) {
        this.http_get_detail();
        this.getUniqueCode();
      }
    },
  },
  methods: {
    getUniqueCode() {
      getUniqueCode({
        tenantId: this.tenant_id,
        productKey: this.productKey,
      }).then((res) => {
        this.UniqueCode = res.data;
      });
    },
    fn_import() {
      //   this.$newNotify.warning({
      //     message: "暂未开发，敬请期待",
      //   });
      this.importVisible = true;
    },
    importBefore(fileList) {
      console.log(fileList,'111');
      let file = fileList[0];
      let nameList = file.name.split(".");
      let format = nameList[nameList.length - 1];
      console.log(file);
      if (format !== "json") {
        this.importErrorMsg = "文件格式错误";
        this.$newNotify.error({
          message: "请上传正确格式的文件!",
        });
        return false;
      }
      const isLt1024M = file.size < 512 * 1024;
      if (!isLt1024M) {
        this.$newNotify.error({
          message: "上传文件不能大于512kb!",
        });
        return false;
      }
      this.fileData = file;
      return true;
    },
    importSuccess() {
      this.importVisible = false;
      this.$newNotify.success({
        message: "导入成功",
      });
      setTimeout(() => {
        this.$refs.iotUpload.handleReset();
        this.http_get_detail();
        this.getUniqueCode();
      }, 300);
    },
    importError(err) {
      this.importErrorMsg = "物模型上传失败";
      this.$newNotify.error({
        message: err.message || "导入失败",
      });
    },
    importFileChange(num) {
      this.btnClass = num > 0 ? "default" : "grey";
    },

    fn_sure_import() {
      this.$refs.iotUpload.planUpload();
    },
    fn_close_import() {
      this.$refs.iotUpload.handleReset();
    },
    fn_see() {
      console.log(this.thingModel.services);
      this.visible = true;
      this.$nextTick(() => {
        this.$refs.editor.refresh();
      });
    },
    group(array, subGroupLength) {
      let index = 0;
      let newArray = [];
      while (index < array.length) {
        newArray.push(array.slice(index, (index += subGroupLength)));
      }
      return newArray;
    },
    currentChange(data) {
      this.pagination.current = data;
      this.tableData = this.pageSource[data - 1];
    },
    sizeChange(data) {
      this.pagination.current = 1;
      this.pagination.size = data;
      this.pageSource = this.group(this.dataSource, this.pagination.size);
      this.tableData = this.pageSource[0]; //默认第一页的数据
    },
    handleReset() {
      this.getUniqueCode();
      this.http_get_detail();
    },
    fn_open() {
      // this.$refs.add.add(JSON.parse(this.thingModel2));
      this.$refs.add.add(this.thingModel);
    },
    fn_sure_export() {
      let params = {
        // id: this.$route.query.id,
        productKey: this.productKey,
      };
      exportObjectModel(params).then((res) => {
        console.log("res", res);
        if (!res) {
          this.$newNotify.error({
            message: "不存的物模型文件",
          });
          return;
        }
        const blob = new Blob([JSON.stringify(res, undefined, 4)], {
          type: "application/json",
        });
        // const blob = new Blob([res],{
        //   type: 'application/json'
        // })
        // const file = new File([blob],this.$route.query.title)
        const link = document.createElement("a");
        link.style.display = "none";
        link.href = URL.createObjectURL(blob);
        // link.href = URL.createObjectURL(file)
        link.download = unescape(this.productTitle); // 下载后文件名: 拦截器处理 content-disposition, 传入优先
        document.body.appendChild(link);
        link.click();
        this.$newNotify.success({
          message: "导出成功",
        });
        this.visible = false;
      });
    },
    fn_get_product_detail() {
      let params = {
        id: this.$route.query.id,
      };
      getProductDetail(params).then((res) => {
        if (res.code == 200) {
          this.produceForm = res.data;
          let data = {
            id: res.data.id,
            title: res.data.name,
            productKey: res.data.productKey,
          };
          this.$store.dispatch("setLayoutInfo", data);
          this.importFormData["productKey"] = res.data.productKey;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    fn_edit(record) {
      // 功能分类   标识符  ------- 传递物模型
      let { id, classification } = record;
      let data = {
        classification,
        id,
      };
      // this.$refs.add.edit(data, JSON.parse(this.thingModel2));
      this.$refs.add.edit(data, this.thingModel);
    },
    afterDelete(data) {
      this.visibleDelete = true;
      this.record = data;
    },
    fn_sure() {
      this.fn_del(this.record);
      this.visibleDelete = false;
    },
    fn_del(record) {
      let { id, classification } = record;
      let nowIndex = -1;
      let postIndex = -1;
      let getIndex = -1;
      let setIndex = -1;
      if (classification == 0) {
        // 属性删除
        nowIndex = this.thingModel.properties.findIndex(
          (item) => item.id === id
        );
        this.thingModel.properties.splice(nowIndex, 1);
        this.thingModel.events.forEach((item, index) => {
          if (item.id === "post") {
            let outIndex = item.out.findIndex((item3) => item3.id === id);
            item.out.splice(outIndex, 1);
            if (item.out.length == 0) {
              postIndex = index;
            }
          }
        });
        if (postIndex != -1) {
          this.thingModel.events.splice(postIndex, 1);
        }

        this.thingModel.services.forEach((item, index) => {
          if (item.id === "get" || item.id === "set") {
            let inIndex = item.in.findIndex((item2) => item2 === id);
            let outIndex = item.out.findIndex((item3) => item3.id === id);
            item.in.splice(inIndex, 1);
            item.out.splice(outIndex, 1);
            if (item.in.length == 0 || item.out.length == 0) {
              if (item.id === "get") {
                getIndex = index;
              }
              if (item.id === "set") {
                setIndex = index;
              }
            }
          }
        });
        if (getIndex != -1 && setIndex != -1) {
          this.thingModel.services = this.thingModel.services.filter(
            (item) => item.id !== "get" && item.id !== "set"
          );
        }
      } else if (classification == 1) {
        // 事件删除
        nowIndex = this.thingModel.events.findIndex((item) => item.id === id);
        this.thingModel.events.splice(nowIndex, 1);
      } else if (classification == 2) {
        // 服务删除
        nowIndex = this.thingModel.services.findIndex((item) => item.id === id);
        this.thingModel.services.splice(nowIndex, 1);
      }
      if (nowIndex != -1) {
        //  编码设计原因  在此清除  classification  属性   物模型结构 不存在此字段  -------------后续优化   优先将此字段 抽离
        // 临时处理  将此字段手动删除
        this.thingModel.events = this.thingModel.events.map((item) => {
          delete item.classification;
          return item;
        });
        this.thingModel.properties = this.thingModel.properties.map((item) => {
          delete item.classification;
          return item;
        });
        this.thingModel.services = this.thingModel.services.map((item) => {
          delete item.classification;
          return item;
        });
        //
        productAbilityEdit(
          {
            tenantId: this.tenant_id,
            productKey: this.productKey,
            thingModel: JSON.stringify(this.thingModel),
          },
          {
            uniqueCode: this.UniqueCode,
          }
        ).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: "删除成功",
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
          this.handleReset();
          this.thingModel2 = JSON.stringify(JSON.parse(this.jsonData), null, 2);
        });
      }
    },
    http_get_detail() {
      let data = {
        // id: 1458046283196428289,
        // tenantId: this.tenant_id,
        productKey: this.productKey,
      };
      productAbilityDetail(data).then((res) => {
        console.log(res,'%%%');
        try {
          // this.thingModel2 = JSON.stringify(JSON.parse(res.data), null, 2);
          if (res.code == 200 && typeof res.data == "string") {
            this.$set(
              this,
              "thingModel2",
              JSON.stringify(JSON.parse(res.data), null, 2)
            );
            this.thingModel = JSON.parse(res.data);
            let properties = this.thingModel.properties.map((item) => {
              item.classification = 0;
              return item;
            });
            let events = this.thingModel.events.filter((item) => {
              item.classification = 1;
              return item.id !== "post";
            });
            let services = this.thingModel.services.filter((item) => {
              item.classification = 2;
              return item.id !== "get" && item.id !== "set";
            });
            this.dataSource = []
              .concat(properties)
              .concat(events)
              .concat(services);
            this.pagination.total = this.dataSource.length;
            this.tableData = this.dataSource;
            // this.sizeChange(10);
          } else {
            this.$set(
              this,
              "thingModel2",
              JSON.stringify(JSON.parse(this.jsonData), null, 2)
            );
          }
          if (res.code != 200) {
            this.$newNotify.warning({
              message: res.message,
            });
          }
        } catch {
          this.thingModel = {};
        }
      });
    },
    formateModel() {},
  },
};
</script>

<style lang="scss" scoped>
.project {
  .project-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 18px 0px 18px 0px;
    // .top-left {
    // }
    .top-right {
      align-items: center;
    }
  }
  .project-table {
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
  .project-bottom {
    text-align: right;
    margin-top: 18px;
  }
  .del-tips {
    width: 420px;
  }
}
.import-title {
  color: #515151;

  font-weight: 400;
  font-size: 14px;
  line-height: 16px;
  padding: 0 0 8px;
  span {
    color: #ff4d4f;
  }
}

/deep/ {
  .cm-s-idea {
    height: 60vh;
  }
}
</style>
