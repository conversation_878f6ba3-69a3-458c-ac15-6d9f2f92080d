<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 15:38:52
 * @LastEditors: hs
 * @LastEditTime: 2021-11-17 10:52:27
-->
<template>
  <el-dialog
    :close-on-click-modal="false"
    custom-class="iot-dialog"
    :top="top"
    :title="title"
    :visible.sync="dialogVisible"
    :width="width"
    :before-close="fn_close"
    :append-to-body="appendBody"
    :modal="maskModel"
  >
    <div class="iot-dialog-content" :style="{ maxHeight: maxHeight }">
      <slot name="body"></slot>
    </div>

    <div class="footer" v-if="footer">
      <iot-button
        v-throttle="500"
        text="取消"
        type="white"
        @search="fn_close"
      />
      <iot-button
        v-throttle="500"
        :type="btnClass"
        :text="comfirmText"
        @search="fn_sure"
      />
    </div>
  </el-dialog>
</template>
<script>
import IotButton from "@/components/iot-button";
export default {
  name: "IotDialog",
  components: {
    IotButton,
  },
  props: {
    top: {
      type: String,
      default: "15vh",
    },
    maxHeight: {
      type: String,
      default: "65vh",
    },
    title: {
      type: String,
      default: "标题",
    },
    visible: {
      type: Boolean,
      default: false,
    },
    width: {
      type: String,
      default: "30%",
    },
    footer: {
      type: Boolean,
      default: true,
    },
    appendBody: {
      type: Boolean,
      default: false,
    },
    callbackSure: Function,
    comfirmText: {
      type: String,
      default: "确 定",
    },
    maskModel: {
      type: Boolean,
      default: true,
    },
    showLoading: {
      type: Boolean,
      default: false,
    },
    btnClass: {
      type: String,
      default: "default",
    },
  },
  computed: {
    dialogVisible() {
      return this.visible;
    },
  },
  data() {
    return {};
  },
  methods: {
    // 取消的回调
    fn_close() {
      this.$emit("update:visible", false);
      this.$emit("close");
    },
    // 确定的回调
    fn_sure() {
      // if (this.callbackSure) {
      //   this.callbackSure()
      // }
      this.$emit("callbackSure");
      // this.$emit('update:visible', false)
    },
  },
};
</script>
<style lang="scss" scoped>
// .iot-dialog{
//   position: relative;
// }
.iot-dialog-content {
  overflow: auto;
  padding: 20px 32px;
  overflow: auto;
  border-top: 1px solid #eeeff1;
}
.footer {
  width: 100%;
  height: 72px;
  background: #fbfbfb;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 14px;
  text-align: right;
}
/deep/ {
  .el-dialog__body {
    padding-top: 0px;
    position: relative;
    padding: 0px;
  }
  .el-dialog__header{
    height: 58px;
  }
  .iot-btn {
    margin-right: 18px;
  }
  .el-dialog__title {
    margin-left: 10px;
    font-family: H_Medium;
  }
  .el-dialog__headerbtn .el-dialog__close {
    color: rgba(81, 81, 81, 1);
  }
  [class*=" el-icon-"],
  [class^="el-icon-"] {
    font-weight: 600;
  }
  .footer {
    height: 58px !important;
  }
}
</style>
