<template>
  <div class="specialDesc flex">
    <img src="~@/assets/images/index/specialTips.png" alt />
    <span>
      {{ content }}
    </span>
  </div>
</template>

<script>
export default {
  props: {
    content: {
      type: String,
    },
  },
};
</script>

<style lang="scss" scoped>
.specialDesc {
  padding: 11px 0 11px 14px;
  background-color: rgba(1, 138, 255, 0.08);
  margin-bottom: 18px;
  span {
    font-size: 12px;
    line-height: 14px;
  }
  img {
    display: inline-block;
    width: 14px;
    height: 14px;
    line-height: 14px;
    margin-right: 10px;
  }
}
</style>
