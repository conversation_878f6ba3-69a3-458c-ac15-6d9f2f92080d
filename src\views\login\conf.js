import md5 from "js-md5";
import topBar from "@/components/topbar";
import copyright from "@/components/copyright";
import { getLogin, childList } from "@/api/user";
export default {
  data() {
    return {
      flag: true,// 控制登录按钮点击的标志
      nameFocus: false,//用户名输入框是否聚焦
      passwordFocus: false,//密码输入框是否聚焦
      isVisible: false, //是否为明文输入框
      form: {
        username: "",
        password: "",
      },
      rules: {
        username: [
          {
            required: true,
            message: "请输入手机号或用户名",
            trigger: "blur",
          },
        ],
        password: [{ required: true, message: "请输入密码", trigger: "blur" }],
      },
      loginTips: "", //用户名或密码不正确
    };
  },
  components: { topBar, copyright },
  mounted() {
    window.addEventListener("keydown", this.keyDown, true);//监听键盘按下事件，并执行this.keyDown函数,true表示在捕获阶段处理事件
    this.$nextTick(() => {//页面加载完成后将焦点设置在用户名输入框
      this.$refs.username.focus();
    });
  },
  methods: {
    // test 提交
    handleRegister() {//跳转至登录页面
      this.$router.push({
        path: "/register",
      });
    },
    handleReset() {// 跳转到重置密码页面
      this.$router.push({
        path: "/reset",
      });
    },
    keyDown(event) {// 监听键盘按下事件，如果按下的是回车键，则执行登录操作
      if (event.keyCode === 13) {
        this.handleLogin();
      }
    },
    handleLogin() {// 处理登录操作
      this.$refs.form.validate((valid) => {
        if (valid) {
          if (!this.flag) return; // 防重复点击登录按钮
          this.flag = false;
          let params = {
            grant_type: "password",
            scope: "all",
          };
          params = Object.assign(params, this.form);// 将表单数据合并到 params 对象中
          // 暂时只要md5        this.$getRsaCode(md5(params.password))
          params.password = md5(params.password);
          getLogin(params)
            .then((res) => {
              if (res.code == 200) {
                
                localStorage.setItem("access_token", res.data.access_token);

                // 对租户 ID 进行 AES 加密后存储到本地缓存中
                let encrypt_tenant_id = this.Encrypt.encryptoByAES(
                  res.data.tenant_id
                );
                localStorage.setItem("tenant_id", encrypt_tenant_id);

                // 构造用户信息对象，并将其存储到本地缓存中
                let userInfo = {
                  ...res.data,
                  tenant_id: encrypt_tenant_id,
                };
                localStorage.setItem("userInfo", JSON.stringify(userInfo));

                // 更新全局状态中的用户信息
                this.$store.dispatch("setUserInfo", res.data);
                this.$router.push('/')
                // this.$router.push({
                //   path: "/overview",
                // });
                // childList({
                //   tenant_id: res.data.tenant_id,
                //   parentId: "",
                // }).then((res) => {
                //   console.log("/cc", res);
                // });
                // console.log(window.location);
                // window.location.reload();
              } else {
                // this.$message.warning(res.message);
                this.loginTips = res.message || "用户名或密码不正确";
                setTimeout(() => {
                  this.loginTips = "";
                }, 3000);
              }
            })
            .finally(() => {
              this.flag = true;
            });
        } else {
          return false;
        }
      });
    },
  },
  beforeDestroy() {//移除键盘监听事件
    window.removeEventListener("keydown", this.keyDown, true);
  },
};
