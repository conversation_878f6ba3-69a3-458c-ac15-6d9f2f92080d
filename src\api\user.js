import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;
const auth = baseServer;
const user = baseServer;
const system = baseServer;

/**
 * @desc 登录
 * @params params
 * @returns
 */

export const getLogin = (params) => {
  return request({
    url: `${auth}/oauth/token`,
    method: "post",
    params,
  });
};

/**
 * @desc 登录
 * @params params
 * @returns
 */

export const getLoginOut = (params) => {
  return request({
    url: `${auth}/oauth/logout`,
    method: "get",
    params,
  });
};

/**
 * @desc 账号信息
 * @params params
 * @returns
 */

export const getUserInfo = (params) => {
  return request({
    url: `${user}/user/info`,
    method: "get",
    params,
  });
};

/**
 * @desc
 *
 * @params params
 * @returns
 */

export const getRegister = (data) => {
  return request({
    url: `${user}/user/account/register`,
    method: "post",
    data,
  });
};

/**
 * @desc 发送短信验证码
 * @params params
 * @returns
 */

export const sendMessage = (params) => {
  return request({
    url: `${user}/user/account/captcha`,
    method: "post",
    params,
  });
};

/**
 * @desc 注册 字段验证
 * @params params
 * @returns
 */

export const checkFieldPhone = (params) => {
  return request({
    url: `${user}/user/account/isPhoneExist`,
    method: "post",
    params,
  });
};

/**
 * @desc 找回密码 字段验证
 * @params params
 * @returns
 */

export const checkPhoneMustExists = (params) => {
  return request({
    url: `${user}/user/account/phoneMustExists`,
    method: "post",
    params,
  });
};

/**
 * @desc 注册 字段验证
 * @params params
 * @returns
 */

export const checkPhoneMustNotExists = (params) => {
  return request({
    url: `${user}/user/account/phoneMustNotExists`,
    method: "post",
    params,
  });
};

/**
 * @desc 注册 字段验证
 * @params params
 * @returns
 */

export const checkFieldName = (params) => {
  return request({
    url: `${user}/user/account/isAccountExist`,
    method: "post",
    params,
  });
};

/**
 * @desc 注册 验证短信
 * @params params
 * @returns
 */

export const checkCaptcha = (params) => {
  return request({
    url: `${user}/user/account/checkCaptcha`,
    method: "post",
    params,
  });
};

/**
 * @desc 重置密码
 * @params params
 * @returns
 */

export const updatePassword = (data) => {
  return request({
    url: `${user}/user/account/password/update`,
    method: "post",
    data,
  });
};

export const childList = (data) => {
  return request({
    url: `${system}/dict-biz/child-list`,
    method: "get",
    data,
  });
};

/**
 * @desc 修改密码
 * @params newPassword、newPassword1、oldPassword
 * @returns
 */
export const mofidyPassword = (params) => {
  return request({
    url: `${user}/user/update-password`,
    method: "post",
    params,
  });
};
