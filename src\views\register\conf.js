import topBar from '@/components/topbar'
import copyright from '@/components/copyright'
import {
    getRegister,
    sendMessage,
    checkFieldPhone,
    checkPhoneMustNotExists,
    checkFieldName,
    checkCaptcha,
} from '@/api/user'
import confirm from '../reset/components/confirm'
import { getLength, fnThrottle } from '@/util/util'
export default {
    data () {
        const verifyName = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入用户名称'))
            } else {
                let length = getLength(value)
                if (length < 5 || length > 30) {
                    callback(
                        new Error('用户名输入不正确，请按照正确的格式填写')
                    )
                } else {
                    checkFieldName({
                        account: value,
                    })
                        .then((res) => {
                            if (res.code == 200) {
                                // 可以使用
                                callback()
                            } else {
                                callback(new Error(res.message))
                            }
                        })
                        .finally(() => {
                            callback()
                        })
                }
            }
        }
        const password = (rule, value, callback) => {
            let flag = true
            let reg1 = /^.{6,14}$/ //至少6位
            // let reg2 = /s/; ///^\S/; //
            let reg3 =
                /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)/ //.{6,}$
            // let reg4 = /.*[\u4e00-\u9fa5]+.*$/;
            let reg4 = new RegExp('[\\u4E00-\\u9FFF]+', 'g')

            if (value === '') {
                this.passwordTips.length = false
                this.passwordTips.verify = false
                this.passwordTips.double = false
                this.passwordTips.repeat = false
                callback(new Error('请输入密码'))
            } else {
                if (value == this.form.userName) {
                    this.passwordTips.repeat = false
                } else {
                    this.passwordTips.repeat = true
                }
                if (!reg1.test(value)) {
                    // 长度正则
                    this.passwordTips.length = false
                } else {
                    this.passwordTips.length = true
                }
                if (value.indexOf(' ') >= 0) {
                    // !reg2.test(value)
                    this.passwordTips.verify = false
                } else {
                    this.passwordTips.verify = true
                }
                if (!reg3.test(value) || reg4.test(value)) {
                    // 不能是纯数组  / 纯字母  / 纯字符
                    this.passwordTips.double = false
                } else {
                    this.passwordTips.double = true
                }
                for (let i in this.passwordTips) {
                    if (!this.passwordTips[i]) {
                        flag = false
                    }
                }
                if (flag) {
                    callback()
                } else {
                    callback(new Error('密码输入不正确，请输入符合要求的密码'))
                }
            }
        }
        const verifyPassword = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请再次输入密码'))
            } else if (value != this.form.password) {
                callback(new Error('两次密码不一致'))
            } else {
                callback()
            }
        }
        const phoneBlur = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('请输入手机号'))
            } else {
                let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
                if (reg.test(value)) {
                    checkPhoneMustNotExists({
                        phone: value,
                    })
                        .then((res) => {
                            if (res.code == 200) {
                                // 可以使用
                            } else {
                                callback(res.message)
                            }
                        })
                        .finally(() => {
                            callback()
                        })
                } else {
                    callback(new Error('请填写有效的手机号'))
                }
            }
        }
        const verifyCaptcha = (rule, value, callback) => {
            if (this.sendError) {
                // 出现异常  退出验证
                callback(new Error(this.sendErrorMessage))
                return
            }
            if (value === '') {
                callback(new Error('请输入6位验证码'))
            } else {
                let reg = /^\d+$|^\d+[.]?\d+$/
                if (reg.test(value)) {
                    if (!this.form.phone) {
                        callback()
                        return
                    }
                    checkCaptcha({
                        captcha: this.form.captcha,
                        phone: this.form.phone,
                    })
                        .then((res) => {
                            if (res.code != 200) {
                                callback(new Error(res.message))
                            } else {
                                callback()
                            }
                        })
                        .finally(() => {
                            callback()
                        })
                } else {
                    callback(new Error('只能输入数字'))
                }
            }
        }
        const verifyCheck = (rule, value, callback) => {
            if (value) {
                callback()
            } else {
                callback('请先阅读平台协议条款，并选择同意')
            }
        }
        return {
            isOpen: true,
            countDown: 59,
            countDownOpen: false,
            sendError: false,
            sendErrorMessage: '',
            passwordTips: {
                length: false,
                repeat: false,
                verify: false,
                double: false,
            },
            form: {
                userName: '',
                password: '',
                confirmPassword: '',
                phone: '',
                captcha: '',
                isCheck: false,
            },
            rules: {
                userName: [
                    {
                        required: true,
                        trigger: 'blur',
                        validator: verifyName,
                    },
                ],
                password: [
                    { required: true, trigger: 'change', validator: password },
                    {
                        min: 6,
                        max: 14,
                        trigger: 'change',
                        message: '密码最少6位,最多14位',
                    },
                ],
                confirmPassword: [
                    {
                        required: true,
                        trigger: 'blur',
                        validator: verifyPassword,
                    },
                ],
                phone: [
                    {
                        required: true,
                        trigger: 'blur',
                        validator: phoneBlur,
                    },
                ],
                captcha: [
                    {
                        required: true,
                        trigger: 'blur',
                        validator: verifyCaptcha,
                    },
                    {
                        min: 6,
                        max: 6,
                        trigger: 'blur',
                        message: '请输入6位验证码',
                    },
                    // { pattern: /^\d+$|^\d+[.]?\d+$/, message: "只能输入数字" },
                ],
                isCheck: [
                    {
                        required: true,
                        trigger: 'change',
                        validator: verifyCheck,
                    },
                ],
            },
        }
    },
    components: { topBar, confirm, copyright },
    mounted () { },
    methods: {
        verifyCode () {
            if (!this.form.phone) return
            if (!this.countDownOpen) return
            this.$refs.form.validateField('captcha', (valid) => {
                if (!valid) {
                    checkCaptcha({
                        captcha: this.form.captcha,
                        phone: this.form.phone,
                    }).then((res) => {
                        if (res.code != 200) {
                            this.$message.warning(res.message || '验证码错误')
                        }
                    })
                } else {
                    return
                }
            })
        },

        // 发送验证码
        getCode () {
            this.$refs.form.validateField('phone', (valid) => {
                if (!valid) {
                    checkPhoneMustNotExists({
                        phone: this.form.phone,
                    }).then((res) => {
                        if (res.code == 200) {
                            // 可以使用
                            if (this.countDownOpen) return

                            sendMessage({
                                phone: this.form.phone,
                                messageType: 'register',
                            }).then((res) => {
                                if (res.code == 200) {
                                    this.sendError = false //未出现异常
                                    this.countDownOpen = true
                                    this.$message.success(
                                        res.message || '发送成功'
                                    )
                                    let timer = setInterval(() => {
                                        this.countDown--
                                        if (this.countDown <= 0) {
                                            this.countDownOpen = false
                                            this.countDown = 59
                                            clearInterval(timer)
                                        }
                                    }, 1000)
                                } else {
                                    this.countDownOpen = false
                                    this.sendError = true //出现异常
                                    this.sendErrorMessage = res.message
                                    this.$refs.form.validateField('captcha')
                                }
                            })
                        } else if (res.code == 400) {
                            // this.$refs.form.validateField("phone");
                        }
                    })
                }
            })
        },
        handleLogin () {
            this.$router.push({ path: '/login' })
        },
        handleSubmit: fnThrottle(
            function () {
                this.handleRegister()
            },
            3000,
            true
        ),
        handleRegister () {
            this.$refs.form.validate((valid) => {
                if (valid) {
                    if (!this.form.isCheck) {
                        this.$message.warning('请阅读并勾选协议条款')
                        return false
                    }
                    let params = {}
                    params = Object.assign(params, this.form)
                    params.password = this.$getRsaCode(this.form.password)
                    params.confirmPassword = this.$getRsaCode(
                        this.form.confirmPassword
                    )
                    getRegister(params).then((res) => {
                        if (res.code == 200) {
                            // this.$message.success("注册成功");
                            this.$refs.confirm.open()
                            // setTimeout(() => {
                            //   this.$router.replace({ path: "/login" });
                            // }, 1000);
                        } else {
                            this.$message.warning(res.message)
                        }
                    })
                    // this.form.password = $getRsaCode(this.form.password)
                } else {
                    return false
                }
            })
        },
        handleAgreement () {
            let routerData = this.$router.resolve({ path: '/agreement' })
            window.open(routerData.href, 'agreement')
        },
    },
}
