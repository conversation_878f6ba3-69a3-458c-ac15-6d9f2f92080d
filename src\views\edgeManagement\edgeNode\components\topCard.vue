<template>
  <div class="top-card">
    <div class="card-top">
      <span>边缘管理/节点管理</span>
    </div>
    <h4 class="nodeName">节点管理</h4>
    <div class="searchBox">
      <div class="search">
        <span>节点名称</span>
        <IotInput
          v-model="searchKey"
          placeholder="请输入节点名称"
          @input="handleInput"
          width="240px"
        ></IotInput>
      </div>
      <div class="btnBox">
        <IotButton text="查询" @search="fn_search"></IotButton>
        <IotButton text="重置" @search="fn_reset" type="white"></IotButton>
      </div>
    </div>
  </div>
</template>

<script>
import IotInput from "@/components/iot-input/index.vue";
import IotButton from "@/components/iot-button/index.vue";
export default {
    props:{
        searchKey:{
            type:String
        }
    },
    components:{
        IotInput,
        IotButton
    },
    data(){
        return{

        }
    },
    methods:{
        handleInput(val){
            this.$emit("update:searchKey",val)
        },
        fn_search(){
            this.$emit('searchNodeName')
        },
        fn_reset(){
            this.$emit('resetNodeName')
        }
    }
};
</script>

<style scoped lang="scss">
.top-card {
  width: 100%;
  padding: 15px;
  margin-bottom: 20px;
  background-color: #fff;
  .card-top {
    font-size: 14px;
    color: gray;
  }
  .nodeName {
    padding: 15px 0;
  }
  .searchBox {
    width: 100%;
    height: 50px;
    border-top: 1px solid #e6e6e6;
    padding-top: 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    .search {
      width: 400px;
      height: 30px;
      display: flex;
      align-items: center;
      .span {
        margin-right: 10px;
        font-size: 14px;
      }
    }
    .btnBox {
      :first-child {
        margin-right: 10px;
      }
    }
  }
}
</style>
