/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-11 10:00:31
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2022-05-13 15:53:59
 */
import Vue from "vue";
import ElementUI from "element-ui";
import "element-ui/lib/theme-chalk/index.css";
import router from "./router";
import "@/router/permission.js";
import store from "./store";
import App from "./App.vue";
import JSEncrypt from "@/util/rsa";
import "@/style/common.scss";
import "@/util/debounce.js";
import "@/util/throttle.js";
import "@/util/copy.js";
import "@/util/el-loadmore.js";
import { scrollToRef } from "@/util/scrollTo";
import * as echarts from "echarts";

import CodeEditor from "bin-code-editor";
import Encrypt from "@/util/aes";

import { notifyError, notifySuccess, notifyWarning } from "@/components/notify";
import { removeKeepAliveCacheForVueInstance } from "@/util/clearKeepAlive";
Vue.config.productionTip = false;
Vue.prototype.$getRsaCode = JSEncrypt; //rsa加密函数

Vue.prototype.$echarts = echarts;

Vue.prototype.Encrypt = Encrypt; // AES加解密函数

// 例子
// this.Encrypt.encryptoByAES(data) 加密操作 传入字符串(对象也必须通过JSON.stringIfy()操作)
// this.Encrypt.decryptoByAES(data) 解密操作

Vue.prototype.$newNotify = {
  error: notifyError,
  success: notifySuccess,
  warning: notifyWarning,
};

Vue.prototype.$scrollRef = scrollToRef;

Vue.prototype.$clearKeepAlive = removeKeepAliveCacheForVueInstance;

Vue.use(CodeEditor);
Vue.use(ElementUI);

new Vue({
  router,
  store,
  render: (h) => h(App),
  beforeCreate(){
    Vue.prototype.$bus=this
  }
}).$mount("#app");
