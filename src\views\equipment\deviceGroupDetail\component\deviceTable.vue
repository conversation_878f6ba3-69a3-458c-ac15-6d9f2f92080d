<template>
    <div>
        <iot-dialog
        :visible.sync="dialogVisible"
        :title="title"
        :width="width"
        :showLoading="true"
        @callbackSure="fn_sure"
        @close="fn_close"
        >
            <template #body>
                <div id="detail_GroupList">
                    <div class="left">
                        <div class="tableNav">
                            <form-search
                            :selectHolder="selectHolder"
                            :inputHolders="inputHolders"
                            ></form-search>
                        </div>
                        
                        <iot-table
                        :columns="columns"
                        :data="tableData" 
                        :loading="loading"
                        @selection-change="fn_select_more_data"
                        >
                        <template slot="status" slot-scope="scope">
                            <div class="table-status">
                                <div class="status flex" v-if="scope.row.status == 6">
                                <div class="red"></div>
                                <div>离线</div>
                                </div>
                                <div class="status flex" v-if="scope.row.status == 5">
                                <div class="green"></div>
                                <div>在线</div>
                                </div>
                                <div class="status flex" v-if="scope.row.status == 4">
                                <div class="yellow"></div>
                                <div>未激活</div>
                                </div>
                            </div>
                        </template>
                        </iot-table>
                        <!-- 分页 -->
                        <div class="produce-bottom" v-if="tableData.length">
                            <iot-pagination
                            :pagination="pagination"
                        />
                        </div>
                    </div>
                    <div class="right"></div>
                </div>
            </template>
        </iot-dialog>
    </div>
</template>

<script>
import IotTable from "@/components/iot-table";
import IotDialog from "@/components/iot-dialog";
import FormSearch from "@/components/form-search";
import IotPagination from "@/components/iot-pagination";

import {get_DeviceGroup,get_DeviceGroup2,add_DeviceGroup} from "@/api/equipment"
    export default {
        name:'deviceTable',
        components: {
            IotTable,
            IotDialog,
            FormSearch,
            IotPagination
        },
        props:{
            title: {
                type: String,
                default: "标题",
            },
            visible: {
                type: Boolean,
                default: false,
            },
            width: {
                type: String,
                default: "1118px",
            },
            id:{
                type: String,
                default: "",
            }
        },
        data(){
            return{
                
                columns: [
                    {
                        type: "selection",
                        selectionText: false,
                        title: "确定删除该设备？",
                        text: "批量删除设备后，设备数据不可恢复，请确认是否删除？",
                        visible: false,
                        isShowdelete: false,
                    },
                    { label: "设备名称", prop: "deviceName"},
                    {label: "所属产品",prop: "productName"},
                    { label: "设备状态", prop: "status",slotName:"status"},
                ],
                tableData:[],
                loading:true,
                selectHolder: "请选择所属产品/ProductKey",
                inputHolders: ["请输入设备名称/DeviceName", "请输入产品key"],

                pagination: {
                    current: 1, // 当前页
                    total: 0, // 记录条数
                    pages: 0, // 总页数
                    sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
                    size: 10,
                },

                Ids: [],// 多选出来的id
            }
        },
        methods: {
            //获取设备列表
            get_DeviceGroup(){
                get_DeviceGroup().then((res)=>{
                    // console.log(res,'@@@');
                    const {data} = res
                    this.tableData = data.records
                }).finally(() => {
                    setTimeout(() => {
                        this.loading = false;
                    }, 300);
                    });
            },
            //获取设备列表
            get_DeviceGroup2(){
                get_DeviceGroup2().then((res)=>{
                    console.log(res,'###');
                })
            },
            //弹出框确定按钮
            fn_sure(){
                let data = {
                    id:this.id,
                    deviceId:this.Ids
                }
                add_DeviceGroup(data).then((res)=>{//添加设备接口
                    if(200==res.code){
                        this.$newNotify.success({
                        message: res.message,
                    })
                        this.get_DeviceGroup()
                        this.get_DeviceGroup2()
                        this.$emit("update:visible", false);
                        this.$emit('customEvent')//触发父组件方法重新获取数据
                    }else{
                        this.$newNotify.error({
                            message: res.message,
                        });
                    }
                })
            },
            //弹出框取消按钮
            fn_close(){
                this.$emit("update:visible", false);
            },
            // 多选数据
            fn_select_more_data(data) {
                this.Ids = data.map((item) => {
                    return item.id;
                });
            },
        },
        
        computed: {
            dialogVisible: {
                get() {
                return this.visible;
                },
                set(value) {
                this.$emit("update:visible", value);
                },
            },
        },
        mounted(){
            this.get_DeviceGroup()
            this.get_DeviceGroup2()
        }
    }
</script>

<style lang="scss" scoped>
#detail_GroupList{
        width: 100%;
        // height: 400px;
        display: flex;
        .left{
            width: 50%;
            padding: 10px;
            border: 1px solid #ccc;
            .tableNav{
            width: 100%;
            height: 50px;
            display: flex;
            justify-content: end;
        }
        .produce-bottom {
            text-align: right;
            margin-top: 14px;
            padding-bottom: 10px;
        }
            .table-status {
                .status {
                    .red {
                    background: #ff4d4f;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                    .green {
                    background: #00c250;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                    .yellow {
                    background: #e6a23c;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                }
            }
        }
        .right{
            width: 50%;
            // background-color: #00c250;
            padding: 10px;
            border: 1px solid #ccc;
        }
    }
</style>