import Vue from "vue";
Vue.directive("el-loadmore", {
  bind(el, binding) {
    const SELECTWRAP_DOM = el.querySelector(
      ".el-select-dropdown .el-select-dropdown__wrap"
    );
    let startTime = new Date();
    SELECTWRAP_DOM.addEventListener("scroll", function () {
      const CONDITION =
        this.scrollHeight - this.scrollTop - 1 <= this.clientHeight;
      let curTime = new Date();
      // 简易节流   两次调用至少间隔500
      if (CONDITION && curTime - startTime >= 500) {
        binding.value();
        startTime = curTime;
      }
    });
  },
});
