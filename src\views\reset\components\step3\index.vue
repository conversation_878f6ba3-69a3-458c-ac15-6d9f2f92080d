<template>
  <div class="model">
    <h4>忘记密码</h4>
    <div class="form">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="" prop="password">
          <div class="form-item" v-if="step == 2">
            <!-- <p><span>*</span>设置密码</p> -->
            <el-tooltip
              class="item"
              effect="light"
              placement="right"
              popper-class="register-tooltip"
            >
              <div slot="content" class="tips-password">
                <p class="flex">
                  <img
                    v-if="passwordTips.length"
                    src="~@/assets/images/index/right-icon.png"
                    alt=""
                  />
                  <img
                    v-else
                    src="~@/assets/images/index/wrong-icon.png"
                    alt=""
                  />
                  <span>密码长度至少6位,最多14位；</span>
                </p>
                <p class="flex">
                  <img
                    v-if="passwordTips.repeat"
                    src="~@/assets/images/index/right-icon.png"
                    alt=""
                  />
                  <img
                    v-else
                    src="~@/assets/images/index/wrong-icon.png"
                    alt=""
                  />
                  <span> 密码不能与用户名相同；</span>
                </p>
                <p class="flex">
                  <img
                    v-if="passwordTips.verify"
                    src="~@/assets/images/index/right-icon.png"
                    alt=""
                  />
                  <img
                    v-else
                    src="~@/assets/images/index/wrong-icon.png"
                    alt=""
                  />
                  <span>密码只能包含数字、字母和符号（除空格）；</span>
                </p>
                <p class="flex">
                  <img
                    v-if="passwordTips.double"
                    src="~@/assets/images/index/right-icon.png"
                    alt=""
                  />
                  <img
                    v-else
                    src="~@/assets/images/index/wrong-icon.png"
                    alt=""
                  />
                  <span>字母、数字和符号至少包含两种；</span>
                </p>
              </div>
              <el-input
                v-model="form.password"
                placeholder="输入新密码"
                show-password
              ></el-input>
            </el-tooltip>
          </div>
        </el-form-item>
        <el-form-item label="" prop="confirmPassword">
          <div class="form-item" v-if="step == 2">
            <!-- <p><span>*</span>确认密码</p>  -->
            <el-tooltip
              class="item"
              effect="light"
              content=" · 需与密码一致"
              placement="right"
              popper-class="register-tooltip"
            >
              <el-input
                v-model="form.confirmPassword"
                placeholder="确认新密码"
                show-password
              ></el-input>
            </el-tooltip>
          </div>
        </el-form-item>
      </el-form>
      <div class="handle-next" v-throttle="1500" @click="handleConfirm">
        <span>确认</span>
      </div>
      <div class="handle-login">
        <span @click="handleLogin">已有账号，立即登录</span>
      </div>
    </div>
  </div>
</template>

<script>
import { updatePassword } from "@/api/user";
export default {
  data() {
    const password = (rule, value, callback) => {
      let flag = true;
      let reg1 = /^.{6,14}$/; //至少6位
      // let reg2 = /s/; ///^\S/; //
      let reg3 = /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)/; //.{6,}$
      // let reg4 = /.*[\u4e00-\u9fa5]+.*$/;
      let reg4 = new RegExp("[\\u4E00-\\u9FFF]+", "g");

      if (value === "") {
        this.passwordTips.length = false;
        this.passwordTips.verify = false;
        this.passwordTips.double = false;
        this.passwordTips.repeat = false;
        callback(new Error("请输入新密码"));
      } else {
        if (value == this.form.userName) {
          this.passwordTips.repeat = false;
        } else {
          this.passwordTips.repeat = true;
        }
        if (!reg1.test(value)) {
          // 长度正则
          this.passwordTips.length = false;
        } else {
          this.passwordTips.length = true;
        }
        if (value.indexOf(" ") >= 0) {
          // !reg2.test(value)
          this.passwordTips.verify = false;
        } else {
          this.passwordTips.verify = true;
        }
        if (!reg3.test(value) || reg4.test(value)) {
          // 不能是纯数组  / 纯字母  / 纯字符
          this.passwordTips.double = false;
        } else {
          this.passwordTips.double = true;
        }
        for (let i in this.passwordTips) {
          if (!this.passwordTips[i]) {
            flag = false;
          }
        }
        if (flag) {
          callback();
        } else {
          callback(new Error("密码输入不正确，请输入符合要求的密码"));
        }
      }
    };
    const verifyPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请确认新密码"));
      } else if (value != this.form.password) {
        callback(new Error("两次密码不一致"));
      } else {
        callback();
      }
    };
    return {
      passwordTips: {
        length: false,
        repeat: false,
        verify: false,
        double: false,
      },
      form: {
        password: "",
        confirmPassword: "",
      },
      rules: {
        password: [
          { required: true, trigger: "change", validator: password },
          {
            min: 6,
            max: 14,
            trigger: "change",
            message: "密码最少6位,最多14位",
          },
        ],
        confirmPassword: [
          {
            required: true,
            trigger: "blur",
            validator: verifyPassword,
          },
        ],
      },
    };
  },
  props: {
    phone: {
      type: String,
    },
    captcha: {
      type: [String, Number],
    },
    step: {
      type: [Number, String],
    },
  },
  methods: {
    handleConfirm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          let params = {};
          params = Object.assign(params, this.form);
          params.password = this.$getRsaCode(params.password);
          params.confirmPassword = this.$getRsaCode(params.confirmPassword);
          params["phone"] = this.phone;
          params["captcha"] = this.captcha;
          updatePassword(params).then((res) => {
            if (res.code == 200) {
              setTimeout(() => {
                this.$emit("next", { step: 3 });
              }, 1200);
            } else {
              this.$message.warning(res.message || "修改失败，请联系管理员");
            }
          });
        } else {
          return false;
        }
      });
    },
    handleLogin() {
      this.$emit("route");
    },
  },
};
</script>

<style lang="scss" scoped>
.model {
  width: 1020px;
  height: 460px;
  margin: 0 auto;
  background: #ffffff;
  box-shadow: 0px 48px 48px rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  h4 {
    height: 98px;
    line-height: 98px;
    color: #333333;
    font-weight: 500;
    font-size: 28px;
    text-align: center;
    border-bottom: 1px solid #f5f5f5;
  }
  .form {
    width: 330px;
    margin: 0 auto;
    padding-top: 48px;
    .form-item {
      align-items: center;
      p {
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        padding-bottom: 8px;
        span {
          color: #f53e3e;
        }
      }
    }
    /deep/ {
      .el-form-item {
        margin-bottom: 34px;
      }
      .el-input {
        input {
          border-radius: 0;
          height: 42px;
          font-size: 14px;
          border: 1px solid #e4e7ec;
        }
        input:focus,
        input:hover {
          border: 1px solid #018aff;
        }
        input::placeholder {
          color: #bfbfbf;
        }
      }
    }
    .handle-next {
      height: 42px;
      text-align: center;
      background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
      color: #ffffff;
      font-size: 16px;
      line-height: 42px;
      margin-top: 34px;
      cursor: pointer;
    }
    .handle-login {
      text-align: right;
      padding-top: 24px;
      span {
        color: #0088fe;
        cursor: pointer;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
}
</style>
<style lang="scss">
.register-tooltip {
  border-radius: 0;
  border: 1px solid #e4e7ec !important;
  background: #ffffff !important;
  backdrop-filter: blur(4px);
  padding: 14px 18px;
  .popper__arrow {
    border-right-color: #e4e7ec !important;
    left: -10px !important;
    border-width: 10px;
  }
  .popper__arrow::after {
    bottom: -10px !important;
    border-width: 10px;
  }

  .tips-password {
    p {
      align-items: center;
      padding-bottom: 8px;
      font-family: H_Medium;
      img {
        width: 14px;
      }
      span {
        padding-left: 8px;
        color: #515151;
        font-size: 13px;
      }
    }
    p:last-child {
      padding-bottom: 0;
    }
  }
}
.model {
  .form {
    .el-form {
      .el-form-item__content {
        .form-item {
          .el-input__inner {
            font-family: "Courier New", Courier, monospace;
          }
        }
      }
    }
  }
}
</style>
