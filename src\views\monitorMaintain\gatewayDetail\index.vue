<template>
  <div id="main">
    <div class="info-content">
      <div class="info-title">
        <div>基础信息</div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>ProductKey</span>
          <span>{{ this.produceForm.productKey || "————" }}</span>
        </div>
        <div class="item">
          <span>DeviceName</span>
          <span>{{ this.produceForm.deviceName || "————" }}</span>
        </div>
        <div class="item">
          <span>设备备注名称</span>
          <span>{{ this.produceForm.aliasName || "————" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>OS操作系统信息</span>
          <span>{{ this.produceForm.osInfo || "————" }}</span>
        </div>
        <div class="item">
          <span>网关程序版本</span>
          <span>{{ this.produceForm.versionNum || "————" }}</span>
        </div>
        <div class="item">
          <span>所属项目</span>
          <span>{{ this.produceForm.projectName || "————" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>CPU处理器信息</span>
          <span>{{ this.produceForm.cpuInfo || "————" }}</span>
        </div>
        <div class="item">
          <span>IP地址</span>
          <span>{{ this.produceForm.ipAddress || "————" }}</span>
        </div>
        <div class="item">
          <span>上报时间间隔</span>
          <span>{{
            this.produceForm.uploadTimeInterval + "分钟" || "————"
          }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>部署上线时间</span>
          <span>{{ this.produceForm.activeTime || "————" }}</span>
        </div>
        <div class="item">
          <span>{{
            this.params.status == 6 ? "最后一次在线时间" : "最近一次上线时间"
          }}</span>
          <span>{{ this.produceForm.lastOnlineTime || "————" }}</span>
        </div>
        <div class="item">
          <span>心跳配置时间</span>
          <span>{{ this.produceForm.hbCfTime + "秒" || "————" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>{{ this.params.status == 6 ? "离线时长" : "运行时长" }}</span>
          <span>{{ getRunTime || "————" }}</span>
        </div>
      </div>
    </div>

    <div class="info-content" style="margin-top: 32px">
      <div class="temperature" v-if="this.produceForm.cpuTemperature!=='0°C'&&this.produceForm.cpuTemperature">
        <p>温度</p>
        <p>{{ this.produceForm.cpuTemperature }}</p>
      </div>
      <div class="info-title">
        <div>系统检测</div>
      </div>
      <div class="systemdetection">
        <div class="systemdetection_box">
          <!-- 保留，防止后续需要更改 -->
          <!-- <div class="systemdetection_top">
            <div>
              <span>CPU使用率</span>
              <div>
                {{
                  this.params.status == 6 ? "--" : this.produceForm.cpuUsageRate
                }}
              </div>
            </div>
            <div>
              <span>温度</span>
              <div>
                {{
                  this.params.status == 6
                    ? "--"
                    : this.produceForm.cpuTemperature
                }}
              </div>
            </div>
          </div> -->
          <div class="systemdetection_bottom1"></div>
        </div>
        <p></p>
        <div class="systemdetection_box">
          <!-- <div class="systemdetection_top">
            <div>
              <span>内存已使用</span>
              <div>
                {{
                  this.params.status == 6
                    ? "--"
                    : this.produceForm.memoryUsageRate
                }}
              </div>
            </div>
            <div>
              <span>内存总量</span>
              <div>{{ this.produceForm.memoryCapacity }}</div>
            </div>
          </div> -->
          <div class="systemdetection_bottom2"></div>
        </div>
        <p></p>
        <div class="systemdetection_box">
          <!-- <div class="systemdetection_top">
            <div>
              <span>JVM内存已使用</span>
              <div>
                {{
                  this.params.status == 6 ? "--" : this.produceForm.jvmMemRate
                }}
              </div>
            </div>
            <div>
              <span>JVM内存总量</span>
              <div>{{ this.produceForm.jvmMemCapacity }}</div>
            </div>
          </div> -->
          <div class="systemdetection_bottom3"></div>
        </div>
        <p></p>
        <div class="systemdetection_box">
          <!-- <div class="systemdetection_top">
            <div>
              <span>磁盘已使用</span>
              <div>
                {{
                  this.params.status == 6
                    ? "--"
                    : this.produceForm.diskUsageRate
                }}
              </div>
            </div>
            <div>
              <span>磁盘总量</span>
              <div>{{ this.produceForm.diskCapacity }}</div>
            </div>
          </div> -->
          <div class="systemdetection_bottom4"></div>
        </div>
      </div>
    </div>

    <div>
      <div class="info-content" style="margin-top: 32px">
        <div class="info-title">
          <div>事件监控</div>
          <div class="info-edit color2">
            <el-radio-group
              v-model="params.type"
              size="mini"
              style="margin-right: 14px"
              @input="changeOP"
            >
              <el-radio-button label="hour">今日</el-radio-button>
              <el-radio-button label="day">最近7天</el-radio-button>
              <el-radio-button label="month">最近12个月</el-radio-button>
            </el-radio-group>
            <span @click="fn_toDetail">详情</span>
          </div>
        </div>
        <div class="eventCanvas">
          <div class="test"></div>
          <div class="test2"></div>
          <p></p>
          <div class="test3"></div>
        </div>
      </div>
    </div>
    <div class="info-content" style="margin-top: 32px">
      <div class="info-title">
        <div>今日连接器处理数据监测</div>
      </div>

      <div class="connectorEcharts"></div>
    </div>

    <div class="info-content" style="margin-top: 32px">
      <div class="info-title">
        <div>数据流量监测</div>
        <div class="info-edit color2">
          <el-radio-group v-model="params.type2" size="mini" @input="changeOP2">
            <el-radio-button label="hour">今日</el-radio-button>
            <el-radio-button label="day">最近7天</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="flow">
        <div class="upAndDown">
          <div>
            上行：<span>{{ upLink || "0" }}</span> 条
          </div>
          <div>
            下行： <span>{{ downLink || "0" }}</span> 条
          </div>
        </div>
        <div class="flowLook"></div>
      </div>
    </div>
    <div style="height: 100px"></div>
  </div>
</template>

<script>
import {
  getfoundationInfo,
  getabnormalEvents,
  getconnectorData,
  getflowData,
} from "@/api/monitorMaintain";
export default {
  data() {
    return {
      params: {
        productKey: null,
        deviceName: "",
        type: "hour",
        type2: "hour",
        key: "",
        id: "",
      },
      type3: "hour",
      upLink: null,
      downLink: null,

      //网关基本信息
      produceForm: {
        aliasName: "", //网关别名
        connDealDataInfo: "", //网关连接器数据信息
        cpuInfo: "", //CUP信息
        cpuTemperature: "", //CPU温度
        cpuUsageRate: "", //网关cpu使用率
        deviceName: "", //网关名称
        diskCapacity: "", //网关磁盘容量
        diskUsageRate: "", //网关磁盘使用率
        firstOnlineTime: "", //最后上线时间
        hbCfTime: "", //心跳配置时间
        ipAddress: "", //IP地址
        jvmMemCapacity: "", //jvm内存容量
        jvmMemRate: "", //jvm内存使用率
        macAddress: "", //网关mac地址
        memoryCapacity: "", //网关内存容量
        memoryUsageRate: "", //网关内存使用率
        netDownRate: "", //网络下行速率
        netUpRate: "", //网络上行速率
        osInfo: "", //OS信息
        productKey: "", //产品KEY
        productName: "", //产品名称
        projectName: "", //项目名称
        uploadTimeInterval: "", //上报时间间隔
        versionNum: "", //网关版本号
        lastOnlineTime: "", //最后上线时间
        activeTime: "", //部署上线时间
        runtime: "",
        status: "",
      },
      currentTime: Date.now(),
      isShowEcharts: true,
      replaceData: {
        cpu: [{ value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } }],
        disk: [{ value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } }],
        info: [{ value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } }],
        jvmMem: [{ value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } }],
        memory: [{ value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } }],
        arrayData: {
          downLinkList: [0],
          timeList: ["网关离线"],
          upLinkList: [0],
        },
      },
    };
  },
  created() {
    this.params.productKey = this.$route.query.productKey;
    this.params.deviceName = this.$route.query.deviceName;
    this.params.status = this.$route.query.status;
    this.params.key = this.$route.query.key;
    this.params.id = this.$route.query.id;
    this.$store.dispatch("setLayoutInfo", {
      id: this.params.id,
      title: this.params.deviceName,
      status: this.params.status,
      key: this.params.key,
    });
    setInterval(() => {
      this.currentTime = Date.now(); // 更新当前时间
    }, 1000); // 每秒钟更新一次当前时间
  },
  mounted() {
    this.getInfo();
    this.abnormalEvents();
    this.getconnectorData();
    this.flowData();
  },

  beforeDestroy() {
    clearInterval(this.timer); //离开组件清除定时器
  },
  watch: {
    currentTime() {
      this.$forceUpdate(); // 强制更新计算属性，使其保持计算状态
    },
  },

  computed: {
    //处理时间
    getRunTime() {
      const lastOnlineTimestamp = Date.parse(this.produceForm.lastOnlineTime); // 指定时间的时间戳

      let timeDiff = this.currentTime - lastOnlineTimestamp; // 时间差（毫秒）
      timeDiff = Math.abs(timeDiff); // 取绝对值，确保时间差为正数

      const hours = Math.floor(timeDiff / (1000 * 60 * 60)); // 小时
      const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60)); // 分钟
      const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000); // 秒

      return `${hours}小时 ${minutes}分钟 ${seconds}秒`;
    },
  },
  methods: {
    //获取基本信息
    getInfo() {
      getfoundationInfo({
        productKey: this.$route.query.productKey,
        deviceName: this.$route.query.deviceName,
      }).then((res) => {
        if (200 == res.code) {
          console.log(res);
          if (res.data.info) {
            this.produceForm = Object.assign(res.data.info);
            this.initData(res.data);
          } else {
            this.initData(this.replaceData);
          }
        }
      });
    },
    // 仪表盘
    initData(data) {
      //cpu
      var myChart = this.$echarts.init(
        document.querySelector(".systemdetection_bottom1")
      );
      var option = {
        title: [
          {
            //标题
            text: "CPU使用率",
            left: "center",
            top: "85%",
            textStyle: {
              //标题样式
              color: "rgba(81, 81, 81, 1)",
              fontSize: 14,
              fontWeight: 400,
              fontFamily: "HarmonyOS Sans SC",
            },
          },
        ],
        series: [
          {
            type: "gauge",
            splitNumber: null,
            radius: "80%",
            axisLine: {
              lineStyle: {
                width: 18,
                color: [[1, "rgba(228, 228, 228)"]],
              },
            },
            axisTick: {
              show: false,
            },
            detail: {
              valueAnimation: false,
              fontSize: 0,
              offsetCenter: [0, "80%"],
              color: "white",
            },
            pointer: {
              show: false, // 隐藏指针
            },
            data: [0],
          },
          {
            type: "gauge",
            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, "#1875f0"],
                  [0.7, "#4ed065"],
                  [0.9, "#f18f1c"],
                  [1, "#f13a30"],
                ],
              },
            },
            axisTick: {
              show: true,
              distance: 3,
              lineStyle: {
                color: "rgba(201, 205, 212, 1)",
              },
            },
            splitLine: {
              show: true,
              distance: 3,
              length: 10,
              lineStyle: {
                width: 2,
                color: "rgba(134, 144, 156, 1)",
              },
            },
            axisLabel: {
              distance: -50,
              fontSize: 12,
              formatter: function (value) {
                if (value === 0 || value === 100) {
                  return value;
                } else {
                  return "";
                }
              },
              color: "rgba(134, 144, 156, 1)",
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 0,
              itemStyle: {
                borderWidth: 10,
              },
            },
            title: {
              show: false,
            },
            detail: {
              formatter: "{value}%",
              valueAnimation: true,
              fontSize: 30,
              offsetCenter: [0, "80%"],
              color: "black",
            },
            markPoint: {
              // 仪表盘指针圆
              animation: true,
              silent: true,
              data: [
                {
                  x: "50%",
                  y: "50%",
                  symbol: "circle",
                  symbolSize: 5, //指针中心圆大小
                  itemStyle: {
                    color: "white",
                  },
                },
              ],
            },
            pointer: {
              show: true, // 隐藏指针
              width: 5, //指针的宽度
              length: "60%", //指针长度，按照半圆半径的百分比
              shadowColor: "#ccc", //默认透明
              shadowBlur: 5,
              itemStyle: {
                color: "black", // 设置颜色
              },
            },
            data: [data.cpu[0].value],
          },
        ],
      };
      // var option = {
      //   tooltip: {
      //     trigger: "item",
      //     formatter: "CPU使用率 : <br/>{b}:{d}%",
      //   },
      //   color: ["#7f83f7", "#ccc"],
      //   series: [
      //     {
      //       name: "CPU使用率",
      //       type: "pie",
      //       radius: ["0%", "75%"],
      //       avoidLabelOverlap: true,
      //       label: {
      //         color: "black",
      //         show: true,
      //         position: "outside",
      //         alignTo: "none",
      //         formatter: "{b} {d}%",
      //       },
      //       emphasis: {
      //         label: {
      //           show: true,
      //           fontSize: 14,
      //         },
      //       },
      //       labelLine: {
      //         show: true,
      //       },
      //       data: data.cpu,
      //     },
      //   ],
      // };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      //内存
      var myChart2 = this.$echarts.init(
        document.querySelector(".systemdetection_bottom2")
      );
      var option2 = {
        title: [
          {
            //标题
            text: data.info.memoryCapacity
              ? "内存使用率（" + data.info.memoryCapacity + "）"
              : "内存使用率",
            left: "center",
            top: "85%",
            textStyle: {
              //标题样式
              color: "rgba(81, 81, 81, 1)",
              fontSize: 14,
              fontWeight: 400,
              fontFamily: "HarmonyOS Sans SC",
            },
          },
        ],
        series: [
          {
            type: "gauge",
            splitNumber: null,
            radius: "80%",
            axisLine: {
              lineStyle: {
                width: 18,
                color: [[1, "rgba(228, 228, 228)"]],
              },
            },
            axisTick: {
              show: false,
            },
            detail: {
              valueAnimation: false,
              fontSize: 0,
              offsetCenter: [0, "80%"],
              color: "white",
            },
            pointer: {
              show: false, // 隐藏指针
            },
            data: [0],
          },
          {
            type: "gauge",
            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, "#1875f0"],
                  [0.7, "#4ed065"],
                  [0.9, "#f18f1c"],
                  [1, "#f13a30"],
                ],
              },
            },
            axisTick: {
              show: true,
              distance: 3,
              lineStyle: {
                color: "rgba(201, 205, 212, 1)",
              },
            },
            splitLine: {
              show: true,
              distance: 3,
              length: 10,
              lineStyle: {
                width: 2,
                // color:"rgba(134, 144, 156, 1)"
              },
            },
            axisLabel: {
              distance: -50,
              fontSize: 12,
              formatter: function (value) {
                if (value === 0 || value === 100) {
                  return value;
                } else {
                  return "";
                }
              },
              color: "rgba(134, 144, 156, 1)",
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 0,
              itemStyle: {
                borderWidth: 10,
              },
            },
            title: {
              show: false,
            },
            detail: {
              formatter: "{value}%",
              valueAnimation: true,
              fontSize: 30,
              offsetCenter: [0, "80%"],
              color: "black",
            },
            markPoint: {
              // 仪表盘指针圆
              animation: true,
              silent: true,
              data: [
                {
                  x: "50%",
                  y: "50%",
                  symbol: "circle",
                  symbolSize: 5, //指针中心圆大小
                  itemStyle: {
                    color: "white",
                  },
                },
              ],
            },
            pointer: {
              show: true, // 隐藏指针
              width: 5, //指针的宽度
              length: "60%", //指针长度，按照半圆半径的百分比
              shadowColor: "#ccc", //默认透明
              shadowBlur: 5,
              itemStyle: {
                color: "black", // 设置颜色
              },
            },
            data: [data.memory[0].value],
          },
        ],
      };
      myChart2.setOption(option2);
      window.addEventListener("resize", function () {
        myChart2.resize();
      });
      //JVM
      var myChart3 = this.$echarts.init(
        document.querySelector(".systemdetection_bottom3")
      );
      var option3 = {
        title: [
          {
            //标题
            text: data.info.jvmMemCapacity
              ? "JVM内存使用率（" + data.info.jvmMemCapacity + "）"
              : "JVM内存使用率",
            left: "center",
            top: "85%",
            textStyle: {
              //标题样式
              color: "rgba(81, 81, 81, 1)",
              fontSize: 14,
              fontWeight: 400,
              fontFamily: "HarmonyOS Sans SC",
            },
          },
        ],
        series: [
          {
            type: "gauge",
            splitNumber: null,
            radius: "80%",
            axisLine: {
              lineStyle: {
                width: 18,
                color: [[1, "rgba(228, 228, 228)"]],
              },
            },
            axisTick: {
              show: false,
            },
            detail: {
              valueAnimation: false,
              fontSize: 0,
              offsetCenter: [0, "80%"],
              color: "white",
            },
            pointer: {
              show: false, // 隐藏指针
            },
            data: [0],
          },
          {
            type: "gauge",
            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, "#1875f0"],
                  [0.7, "#4ed065"],
                  [0.9, "#f18f1c"],
                  [1, "#f13a30"],
                ],
              },
            },
            axisTick: {
              show: true,
              distance: 3,
              lineStyle: {
                color: "rgba(201, 205, 212, 1)",
              },
            },
            splitLine: {
              show: true,
              distance: 3,
              length: 10,
              lineStyle: {
                width: 2,
                // color:"rgba(134, 144, 156, 1)"
              },
            },
            axisLabel: {
              distance: -50,
              fontSize: 12,
              formatter: function (value) {
                if (value === 0 || value === 100) {
                  return value;
                } else {
                  return "";
                }
              },
              color: "rgba(134, 144, 156, 1)",
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 0,
              itemStyle: {
                borderWidth: 10,
              },
            },
            title: {
              show: false,
            },
            detail: {
              formatter: "{value}%",
              valueAnimation: true,
              fontSize: 30,
              offsetCenter: [0, "80%"],
              color: "black",
            },
            markPoint: {
              // 仪表盘指针圆
              animation: true,
              silent: true,
              data: [
                {
                  x: "50%",
                  y: "50%",
                  symbol: "circle",
                  symbolSize: 5, //指针中心圆大小
                  itemStyle: {
                    color: "white",
                  },
                },
              ],
            },
            pointer: {
              show: true, // 隐藏指针
              width: 5, //指针的宽度
              length: "60%", //指针长度，按照半圆半径的百分比
              shadowColor: "#ccc", //默认透明
              shadowBlur: 5,
              itemStyle: {
                color: "black", // 设置颜色
              },
            },
            data: [data.jvmMem[0].value],
          },
        ],
      };
      myChart3.setOption(option3);
      window.addEventListener("resize", function () {
        myChart3.resize();
      });
      //磁盘
      var myChart4 = this.$echarts.init(
        document.querySelector(".systemdetection_bottom4")
      );
      var option4 = {
        title: [
          {
            //标题
            text: data.info.diskCapacity
              ? "磁盘使用率（" + data.info.diskCapacity + ")"
              : "磁盘使用率",
            left: "center",
            top: "85%",
            textStyle: {
              //标题样式
              color: "rgba(81, 81, 81, 1)",
              fontSize: 14,
              fontWeight: 400,
              fontFamily: "HarmonyOS Sans SC",
            },
          },
        ],
        series: [
          {
            type: "gauge",
            splitNumber: null,
            radius: "80%",
            axisLine: {
              lineStyle: {
                width: 18,
                color: [[1, "rgba(228, 228, 228)"]],
              },
            },
            axisTick: {
              show: false,
            },
            detail: {
              valueAnimation: false,
              fontSize: 0,
              offsetCenter: [0, "80%"],
              color: "white",
            },
            pointer: {
              show: false, // 隐藏指针
            },
            data: [0],
          },
          {
            type: "gauge",
            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, "#1875f0"],
                  [0.7, "#4ed065"],
                  [0.9, "#f18f1c"],
                  [1, "#f13a30"],
                ],
              },
            },
            axisTick: {
              show: true,
              distance: 3,
              lineStyle: {
                color: "rgba(201, 205, 212, 1)",
              },
            },
            splitLine: {
              show: true,
              distance: 3,
              length: 10,
              lineStyle: {
                width: 2,
                // color:"rgba(134, 144, 156, 1)"
              },
            },
            axisLabel: {
              distance: -50,
              fontSize: 12,
              formatter: function (value) {
                if (value === 0 || value === 100) {
                  return value;
                } else {
                  return "";
                }
              },
              color: "rgba(134, 144, 156, 1)",
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 0,
              itemStyle: {
                borderWidth: 10,
              },
            },
            title: {
              show: false,
            },
            detail: {
              formatter: "{value}%",
              valueAnimation: true,
              fontSize: 30,
              offsetCenter: [0, "80%"],
              color: "black",
            },
            markPoint: {
              // 仪表盘指针圆
              animation: true,
              silent: true,
              data: [
                {
                  x: "50%",
                  y: "50%",
                  symbol: "circle",
                  symbolSize: 5, //指针中心圆大小
                  itemStyle: {
                    color: "white",
                  },
                },
              ],
            },
            pointer: {
              show: true, // 隐藏指针
              width: 5, //指针的宽度
              length: "60%", //指针长度，按照半圆半径的百分比
              shadowColor: "#ccc", //默认透明
              shadowBlur: 5,
              itemStyle: {
                color: "black", // 设置颜色
              },
            },
            data: [data.disk[0].value],
          },
        ],
      };
      myChart4.setOption(option4);
      window.addEventListener("resize", function () {
        myChart4.resize();
      });
    },
    //异常事件监控接口
    abnormalEvents() {
      let data = this.params;
      getabnormalEvents(data).then((res) => {
        if (200 == res.code) {
          this.abnormalEventsCanvas(res.data);
        }
      });
    },
    // 异常事件监控图
    abnormalEventsCanvas(data) {
      if (data.level.length == 0) {
        data.level = [
          { value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } },
        ];
        data.processed = [
          { value: 0, name: "暂无数据", itemStyle: { color: "#ccc" } },
        ];
      }
      //折线
      var myChart = this.$echarts.init(document.querySelector(".test"));
      var option = {
        legend: {
          bottom: 0,
          data: ["系统事件", "设备事件"],
        },
        color: ["rgba(63, 125, 238, 1)", "rgba(0, 194, 80, 1)"],
        tooltip: {
          trigger: "axis", // 设置触发类型为坐标轴轴线触发
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "rgba(255, 82, 82, 1)",
            },
          },
          textStyle: {
            color: "rgba(255, 255, 255, 1)",
          },
        },
        xAxis: {
          type: "category",
          data: data.data.timeList,
          axisLabel: {
            interval: 2, // 设置刻度标签的间隔，即每隔两个刻度显示一个刻度标签
          },

          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed", // 设置为虚线
            },
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true, // 显示坐标轴线
          },
          axisTick: {
            show: true,
          },
          splitLine: {
            show: false, // 隐藏分隔线
          },
        },
        series: [
          {
            name: "系统事件",
            data: data.data.sytemList,
            type: "line",
            areaStyle: {
              color: "rgba(63, 125, 238, 0.05)",
            },
            // smooth: true
          },
          {
            name: "设备事件",
            data: data.data.deviceList,
            type: "line",
            areaStyle: {
              color: "rgba(0, 194, 80, 0.05)",
            },
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
      var myChart2 = this.$echarts.init(document.querySelector(".test2"));
      // 指定图表的配置项和数据
      var option2 = {
        tooltip: {
          trigger: "item",
        },
        color: [
          "rgba(255, 174, 88, 1)",
          "rgba(24, 117, 240, 1)",
          "#77CDF3",
          "#6EDA81",
        ],
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "事件等级",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: true,
            label: {
              color: "black",
              show: true,
              position: "outside",
              alignTo: "none",
              formatter: "{b} {d}%",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
              },
            },
            labelLine: {
              show: true,
            },
            data: data.level,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。
      myChart2.setOption(option2);
      window.addEventListener("resize", function () {
        myChart2.resize();
      });
      var myChart3 = this.$echarts.init(document.querySelector(".test3"));
      // 指定图表的配置项和数据
      var option3 = {
        tooltip: {
          trigger: "item",
        },
        color: ["#cfcfcf", "#FFE27A"],
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "事件处理状态",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: true,
            label: {
              color: "black",
              show: true,
              formatter: "{b} {d}%",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 14,
              },
            },
            labelLine: {
              show: true,
            },
            data: data.processed,
          },
        ],
      };
      myChart3.setOption(option3);
      window.addEventListener("resize", function () {
        myChart3.resize();
      });
    },
    //获取连接器数据
    getconnectorData() {
      let data = this.params;
      getconnectorData(data).then((res) => {
        this.connectorDataCanvas(res.data);
      });
    },
    //连接器数据图
    connectorDataCanvas(data) {
      var myChart = this.$echarts.init(
        document.querySelector(".connectorEcharts")
      );
      var option = {
        tooltip: {
          trigger: "axis", // 设置触发类型为坐标轴轴线触发
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          textStyle: {
            color: "rgba(255, 255, 255, 1)", // 设置文字颜色为红色
          },
        },
        calculable: true,
        xAxis: [
          {
            type: "category",
            data: data.nameList,
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLine: {
              show: true, // 显示坐标轴线
            },
            axisTick: {
              show: true,
            },
          },
        ],
        series: [
          {
            name: "未处理",
            type: "bar",
            data: data.errorsOccurredList,
            itemStyle: {
              color: "rgba(63, 125, 238, 1)", // 自定义颜色
            },
            barWidth: 50, // 调整柱形图的宽度
          },
          {
            name: "已处理",
            type: "bar",
            data: data.messagesProcessedList,
            itemStyle: {
              color: "rgba(119, 205, 243, 1)", // 自定义颜色,
            },
            barWidth: 50, // 调整柱形图的宽度
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    // 数据流量监测接口
    flowData() {
      let data = {
        productKey: this.params.productKey,
        deviceName: this.params.deviceName,
        type: this.params.type2,
      };
      getflowData(data).then((res) => {
        const { data } = res;
        if (data.arrayData) {
          this.flowDataCanvas(data);
        } else {
          this.flowDataCanvas(this.replaceData);
        }
        this.upLink = data.upLink;
        this.downLink = data.downLink;
      });
    },
    // 数据流量图
    flowDataCanvas(data) {
      const { arrayData } = data;
      var myChart = this.$echarts.init(document.querySelector(".flowLook"));
      var option = {
        animationDuration: 3000,
        legend: {
          bottom: 0,
          data: ["上行数据", "下行数据"],
        },
        color: ["rgba(63, 125, 238, 1)", "rgba(0, 194, 80, 1)"],
        tooltip: {
          trigger: "axis", // 设置触发类型为坐标轴轴线触发
          backgroundColor: "rgba(0, 0, 0, 0.5)",
          axisPointer: {
            type: "cross",
            label: {
              backgroundColor: "rgba(255, 82, 82, 1)",
            },
          },
          textStyle: {
            color: "rgba(255, 255, 255, 1)", // 设置文字颜色为红色
          },
        },
        xAxis: {
          type: "category",
          data: arrayData.timeList,
          axisLabel: {
            interval: 2, // 设置刻度标签的间隔，即每隔两个刻度显示一个刻度标签
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed", // 设置为虚线
            },
          },
        },
        yAxis: {
          type: "value",
          axisLine: {
            show: true, // 显示坐标轴线
          },
          splitLine: {
            show: false,
          },
          axisTick: {
            show: true,
          },
        },
        series: [
          {
            name: "上行数据",
            data: arrayData.upLinkList,
            type: "line",
            smooth: true,
            areaStyle: {
              color: "rgba(63, 125, 238, 0.05)",
            },
          },
          {
            name: "下行数据",
            data: arrayData.downLinkList,
            type: "line",
            smooth: true,
            areaStyle: {
              color: "rgba(0, 194, 80, 0.05)",
            },
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },

    changeOP(value) {
      this.params.type = value;
      this.abnormalEvents();
    },

    changeOP2(value) {
      this.params.type2 = value;
      this.flowData();
    },

    fn_toDetail() {
      this.$router.push({
        path: "abnormalevents",
        query: {
          productKey: this.params.productKey,
          deviceName: this.params.deviceName,
        },
      });
    },
  },
};

</script>

<style lang="scss" scoped>
#main {
  width: 100%;
  padding: 16px 32px 0 32px;

  .info-content {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 28px 32px;
    outline: 1px solid #eeeff1;
    position: relative;
    .temperature {
      position: absolute;
      top: 170px;
      left: 18px;
      z-index: 99;
      p:nth-child(1) {
        color: rgba(81, 81, 81, 1);
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
        letter-spacing: 0em;
      }
      p:nth-child(2) {
        color: rgba(51, 51, 51, 1);
        font-family: HarmonyOS Sans SC;
        font-size: 24px;
        font-weight: 500;
        line-height: 28px;
        letter-spacing: 0em;
      }
    }
    .empty {
      width: 100%;
      display: flex;
      justify-content: space-around;
    }
    .info-row {
      .item {
        flex: 1;
        padding-top: 20px;
        width: 50%;
        display: flex;
        height: 100%;
        font-size: 14px;
        font-weight: 400;

        span {
          height: 16px;
          line-height: 16px;
          display: block;

          &:first-child {
            color: #999999;
            width: 112px;
            text-align: right;
            flex-shrink: 0;
          }

          &:last-child {
            // width: 100%;
            flex: 1;
            color: #515151;
            margin-left: 48px;
          }
        }

        .item-span {
          padding-right: 22px;
          width: calc(100% - 100px - 22px);
          color: #515151;
          margin-left: 48px;
          word-wrap: break-word;
        }
      }
    }

    .systemdetection {
      display: flex;
      justify-content: space-between;
      .systemdetection_bottom1 {
        width: 370px;
        height: 320px;
        position: relative;
      }

      .systemdetection_bottom1::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom2 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_bottom2::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom3 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_bottom3::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom4 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_box {
        .systemdetection_top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding: 5px 10px 10px 20px;
          div {
            font-size: 28px;
            font-weight: 500;
            color: rgba(51, 51, 51, 1);
            line-height: 32.82px;
            font-family: HarmonyOS Sans SC;
          }
          span {
            font-weight: 400;
            font-size: 16px;
            color: #515151;
            font-family: HarmonyOS Sans SC;
          }

          p {
            font-weight: 500;
            font-size: 28px;
          }
        }
      }
    }

    .info-title {
      display: flex;
      justify-content: space-between;

      .info-edit {
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: H_Regular;

        .el-radio-group {
          .el-radio-button {
            // width: 80px;
            text-align: center;
          }
        }
        /deep/ {
          .el-radio-button__inner {
            width: 96px;
          }
        }
      }
    }

    .eventCanvas {
      display: flex;
      justify-content: space-between;
      .test {
        width: 600px;
        height: 300px;
        position: relative;
      }
      .test::after {
        content: "";
        position: absolute;
        left: 400px;
        right: -160px;
        bottom: 160px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5) rotate(90deg);
      }
      .test2 {
        height: 300px;
        width: 450px;
        position: relative;
      }
      .test2::after {
        content: "";
        position: absolute;
        left: 280px;
        right: -170px;
        bottom: 160px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5) rotate(90deg);
      }
      .test3 {
        height: 300px;
        width: 450px;
        position: relative;
      }
      .identifying {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 400;
        color: #515151;
        div:nth-child(1) {
          width: 10px;
          height: 5px;
          background-color: #00c250;
          margin: 0px 10px;
        }
        div:nth-child(3) {
          width: 10px;
          height: 5px;
          // background-color: #3F7DEE;
          margin: 0px 10px;
        }
      }

      p {
        flex-shrink: 0;
      }
    }

    .flow {
      .upAndDown {
        display: flex;

        div {
          width: 50%;
          font-size: 16px;

          span {
            font-size: 28px;
            color: #333333;
          }
        }

        div:first-child {
          text-align: end;
          padding-right: 50px;
        }
      }
      .flowLook {
        width: 100%;
        height: 400px;
      }
    }
    .identifying2 {
      // width: 90%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 400;
      color: #515151;
      div:nth-child(1) {
        width: 10px;
        height: 5px;
        background-color: #00c250;
        margin: 0px 10px;
      }
      div:nth-child(3) {
        width: 10px;
        height: 5px;
        background-color: #3f7dee;
        margin: 0px 10px;
      }
    }
    .connectorEcharts {
      height: 400px;
      width: 100%;
    }
  }
}
</style>