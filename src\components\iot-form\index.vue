<!--
 * @Description: 用来清理form默认格式，自定属于iot的样式
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 17:00:22
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-15 16:15:02
-->
<template>
	<div class="iot-form">
		<slot name="default"></slot>
	</div>
</template>

<script>
export default {
	name: 'IotForm',
	props: {},
}
</script>

<style lang="scss" scoped>
:deep(.el-form-item) {
	margin-bottom: 22px !important;
	margin-right: 0px;
	.el-form-item__content {
		line-height: normal;
		.el-input__inner {
			height: 36px;
			line-height: 36px;
			border-radius: 0px;
		}
	}
}

:deep(.el-form--label-top .el-form-item__label) {
	position: relative;
	padding: 0px;
	line-height: 30px;
}
:deep(.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before),
:deep(.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before) {
	margin-right: 0px;
	position: absolute;
	right: -8px;
}
:deep(.el-textarea__inner) {
	min-height: 100px !important;
	border-radius: 0px;
}
:deep(.el-form-tips) {
	font-size: 12px;
	font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
	font-weight: 400;
	color: #999999;
	margin-top: -25px;
	margin-bottom: 9px;
}
:deep(.el-select) {
	width: 100%;
}
:deep(.el-cascader) {
	width: 100%;
}
</style>
