<template>
  <div class="iot-select">
    <el-select
      v-model="value"
      :placeholder="placeholder"
      :clearable="clearable"
      :filterable="filterable"
      @clear="clearVal"
      @change="changeVal"
      :disabled="disabledSelect"
    >
      <!-- 分组 -->
      <template v-if="options[0].options">
        <el-option-group
          v-for="group in options"
          :key="group.label"
          :label="group[needName]"
        >
        
          <el-option
            v-for="item in group.options"
            :key="item.value"
            :label="item.label"
            :value="item[needName]"
          >
          <!-- 插槽 -->
          <slot :item="item"></slot>
          </el-option>
        </el-option-group>
      </template>
      <!-- 没有分组 -->
      <template v-else>
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item[needName]"
        >
        <!-- 插槽 -->
        <slot :item="item"></slot>
        </el-option>
      </template>
    </el-select>
  </div>
</template>

<script>
export default {
  props: {
    //是否显示清除按钮
    clearable: {  
      type: Boolean,
      default: false,
    },
    // 是否可搜索
    filterable: {
      type: Boolean,
      default: false,
    },
    placeholder: {
      type: String,
      default: "请选择",
    },
    options: {
      type: Array,
      default: () => [],
    },
    // 返回值的字段名
    needName: {
      type: String,
      default: "value",
    },
    // 是否禁止选择
    disabledSelect: {
      type: Boolean,
      default: false,
    }
  },
  data() {
    return {
      value: "",
    };
  },
  methods: {
    clearVal() {
      this.$emit("clearVal");
    },
    changeVal(val) {
      this.$emit("selected", val);
    },
  },
};
</script>

<style scoped lang="scss"></style>
