/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-23 14:25:31
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:13:55
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const manage = BASE_SERVER;

/**
 * @desc 新增规则
 * @params params
 * @returns
 */
export const getRuleSave = (data) => {
  return request({
    url: `${manage}/rule/scene/save`,
    method: "post",
    data,
  });
};

/**
 * @desc 规则列表
 * @params params
 * @returns
 */
export const getRuleList = (params) => {
  return request({
    url: `${manage}/rule/scene/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 删除规则
 * @params
 * @returns
 */
export const getRuleDelete = (params) => {
  return request({
    url: `${manage}/rule/scene/remove`,
    method: "delete",
    params,
  });
};

/**
 * @desc 规则详情
 * @params params
 * @returns
 */
export const getRuleInfo = (params) => {
  return request({
    url: `${manage}/rule/scene/info`,
    method: "get",
    params,
  });
};

/**
 * @desc 日志列表
 * @params params
 * @returns
 */
export const getLogList = (params) => {
  return request({
    url: `${manage}/rule/log/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 规则启动
 * @params params
 * @returns
 */
export const getRuleStart = (data) => {
  return request({
    url: `${manage}/rule/scene/start`,
    method: "post",
    data,
  });
};

/**
 * @desc 规则停止
 * @params params
 * @returns
 */
export const getRuleStop = (data) => {
  return request({
    url: `${manage}/rule/scene/stop`,
    method: "post",
    data,
  });
};

/**
 * @desc 规则触发
 * @params params
 * @returns
 */
export const getRuleTrigger = (params) => {
  return request({
    url: `${manage}/rule/scene/trigger`,
    method: "post",
    params,
  });
};

/**
 * @desc 规则查看详情结构
 * @params params
 * @returns
 */
export const getRuleSceneUpdateRule = (data) => {
  return request({
    url: `${manage}/rule/scene/update/rule`,
    method: "post",
    data,
  });
};

/**
 * @desc 规则查看详情结构
 * @params params
 * @returns
 */
export const getRuleSceneUpdate = (data) => {
  return request({
    url: `${manage}/rule/scene/update`,
    method: "post",
    data,
  });
};
