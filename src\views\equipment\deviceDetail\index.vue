<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:06:54
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2023-04-21 17:12:47
-->
<template>
  <div class="detail">
    <div class="detail-top">
      <el-tabs v-model="tabIndex"
               type="border-card">
        <el-tab-pane label="设备信息"
                     name="0">
          <!-- <info-tab
						:id="deviceId"
						:form="deviceForm"
						:tenant_id="tenant_id"
					/> -->
        </el-tab-pane>
        <el-tab-pane label="物模型数据"
                     name="1">
          <model-tab :id="deviceId"
                     :productKey="productKey"
                     :deviceName="deviceName"
                     :tenant_id="tenant_id" />
        </el-tab-pane>
        <el-tab-pane v-if="deviceType == 1"
                     label="子设备管理"
                     name="2">
          <sub-device :id="deviceId"
                      :hostProductKey="productKey"
                      :hostDeviceName="deviceName"
                      :tenant_id="tenant_id" />
        </el-tab-pane>
      </el-tabs>
      <info-tab v-show="tabIndex == '0'"
                :id="deviceId"
                :form="deviceForm"
                :tags="tagsForm"
                :tenant_id="tenant_id" />
    </div>
  </div>
</template>

<script>
import InfoTab from './components/infoTab'
import ModelTab from './components/modelTab'
import SubDevice from './components/subDevice'
import { mapGetters } from 'vuex'
import { getDeviceInfo, getDeviceTags } from '@/api/device'
export default {
  name: 'ProductDetail',
  components: {
    InfoTab,
    ModelTab,
    SubDevice,
  },
  data() {
    return {
      deviceId: '',
      tabIndex: '0',
      deviceType: 1,

      deviceForm: {},
      tagsForm: [],
      productKey: '',
      deviceName: '',

      tenant_id: '',
    }
  },
  computed: {
    ...mapGetters(['xtSocket', 'layoutInfo']),
  },
  created() {
    this.tenant_id = localStorage.getItem('tenant_id')
    this.tenant_id = this.Encrypt.decryptoByAES(
      localStorage.getItem('tenant_id')
    )
    if (this.$route.query.id) this.deviceId = this.$route.query.id
    let flag = this.$route.query.num
    // this.deviceType = this.$route.query.key || 0;
    if (flag == 0) {
      //  来源子设备
      this.tabIndex = '2'
    }
    this.getData()
  },
  methods: {
    getData() {
      let params = {
        id: this.deviceId,
      }
      getDeviceInfo(params).then((res) => {
        if (res.code == 200) {
          this.deviceForm = res.data
          this.deviceName = res.data.deviceName
          this.productKey = res.data.productKey
          // this.deviceType = res.data.nodeTypeKey || 0
          let data = {
            title: res.data.deviceName,
            status: res.data.status,
            key: res.data.nodeTypeKey,
          }
          this.$store.dispatch('setLayoutInfo', data)
          // this.$store.dispatch('http_getDeviceRealData', {
          //   deviceName: this.deviceName,
          //   productKey: this.productKey,
          //   tenantId: this.tenant_id,
          // })
          this.$store.dispatch('fn_init_socket')
          this.$store.dispatch('fn_runstatus__subscribe')
        } else {
          this.$newNotify.error({
            message: res.message,
          })
        }
      })

      let paramsTags = {
        deviceId: this.deviceId,
      }
      getDeviceTags(paramsTags).then((res) => {
        if (res.code == 200) {
          this.tagsForm = res.data
        }
      })
    },
  },
  beforeDestroy() {
    if (this.xtSocket) {
      this.xtSocket.asyncDisconnect()
      this.$store.commit('MUTATIONS_DEVICE__WEBSOCKET', null)
    }
  },
}
</script>

<style lang="scss" scoped>
.detail {
  padding: 0px 32px 20px 32px;
  .detail-top {
    margin-top: 20px;
  }
}

.el-tabs--border-card {
  box-shadow: none;
  border: none;
}
/deep/ {
  .el-tabs__item {
    height: 48px;
    line-height: 48px;
    color: rgba(51, 51, 51, 1);
  }
  .el-tabs--border-card > .el-tabs__header {
    background-color: #edf1f7;
    border-bottom: none;
  }
  .el-tabs--border-card > .el-tabs__content {
    padding: 0px;
  }
  .el-tabs__item {
    padding: 0px 35px;
    height: 42px;
    line-height: 42px;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    color: rgba(51, 51, 51, 1);
    font-family: HarmonyOS Sans SC;
    font-style: normal;
    font-weight: normal;
    font-size: 16px;
    letter-spacing: 1px;
  }
  .el-tabs--top.el-tabs--border-card
    > .el-tabs__header
    .el-tabs__item:nth-child(2) {
    padding-left: 35px;
  }
  .el-tabs--top.el-tabs--border-card
    > .el-tabs__header
    .el-tabs__item:last-child {
    padding-right: 35px;
  }
  .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 3px;
    background-color: #515151;
    z-index: 1;
  }
  /deep/ .is-active {
    color: rgba(51, 51, 51, 1);
  }
}
</style>
