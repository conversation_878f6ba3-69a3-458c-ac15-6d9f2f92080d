<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: --
 * @Date: 2023-8-26 16:54
 * @LastEditors: --
 * @LastEditTime: 2023-8-26 16:54
-->

<template>
  <div id="main">
    <div class="product_bg">
      <h3 class="bg_title">网关运维监控</h3>
      <span class="bg_desc"
        >平台针对所属的所有网关设备运行过程的实时监测、异常监控、通知预警，及通过数据的统计分析，提升网关运维效率，保障网关设备运行的可靠性、稳定性。</span
      >
    </div>
    <div class="content">
      <div class="content-top">
        <div
          class="box"
          style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
        >
          <div class="num">
            <img src="~@/assets/images/monitorMaintain/gatewayNum.png" alt="" />
          </div>
          <div class="name">
            <div>
              <p>
                <span>{{ this.static.gatewayCount || "0" }}</span>
              </p>
              <p>网关总数</p>
            </div>
          </div>
        </div>
        <div
          class="box"
          style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
        >
          <div class="num">
            <img src="~@/assets/images/monitorMaintain/gatewayNum.png" alt="" />
          </div>
          <div class="name">
            <div>
              <p>
                <span>{{ this.static.gatewayActiveCount || "0" }}</span>
              </p>
              <p>网关激活数</p>
            </div>
          </div>
        </div>
        <div
          class="box"
          style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
        >
          <div class="num">
            <img src="~@/assets/images/monitorMaintain/gatewayNum.png" alt="" />
          </div>
          <div class="name">
            <div>
              <p>
                <span>{{ this.static.gatewayOnlineCount || "0" }}</span>
              </p>
              <p>网关在线数</p>
            </div>
          </div>
        </div>
        <div
          class="box"
          style="background: linear-gradient(to bottom, #ffe8cd, #fffaf2)"
        >
          <div class="num">
            <img src="~@/assets/images/monitorMaintain/active.png" alt="" />
          </div>
          <div class="name">
            <div>
              <p>
                <span>{{ this.static.gatewayOfflineCount || "0" }}</span>
              </p>
              <p>网关离线数</p>
            </div>
          </div>
        </div>
        <div
          class="box"
          style="background: linear-gradient(to bottom, #ffe8cd, #fffaf2)"
        >
          <div class="num">
            <img src="~@/assets/images/alarm/todayAlarm.png" alt="" />
          </div>
          <div class="name">
            <div>
              <p>
                <span>{{ this.static.todayEventCount || "0" }}</span>
              </p>
              <p>
                今日事件数
                <b class="todayDatail" @click="open_todayDatail()">详情</b>
              </p>
            </div>
          </div>
        </div>
      </div>
      <div class="table">
        <div class="searchNav">
          <el-select v-model="params.type" @change="clear_optionsData($event)">
            <el-option
              v-for="item in searchOptonsList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <div class="searh_optionsBox">
            <el-input
              v-if="params.type == 0"
              class="alarm-input"
              v-model="params.keyword"
              @keyup.enter.native="handleSearch"
              :disabled="params.type == 0"
              placeholder="输入关键词搜索"
            >
            </el-input>

            <el-input
              v-if="params.type == 1"
              class="alarm-input"
              v-model="params.deviceName"
              @keyup.enter.native="handleSearch"
              placeholder="输入关键词搜索"
            >
            </el-input>

            <el-input
              v-if="params.type == 2"
              class="alarm-input"
              v-model="params.aliasName"
              @keyup.enter.native="handleSearch"
              placeholder="输入关键词搜索"
            >
            </el-input>

            <el-select
              v-model="params.status"
              placeholder="请选择在线状态"
              v-if="params.type == 3"
            >
              <el-option
                v-for="item in StatusList"
                :key="item.value"
                :label="item.name"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </div>
          <iot-button
            text="查询"
            type="default"
            style="margin-right: 12px"
            @search="handleSearch"
          ></iot-button>

          <iot-button
            text="重置"
            type="white"
            @search="fn_clear_search_info"
          ></iot-button>
        </div>
        <div class="tableContent">
          <iot-table :columns="columns" :data="tableData" :loading="loading">
            <template slot="status" slot-scope="scope">
              <div class="table-status">
                <div class="status flex" v-if="scope.row.status == 6">
                  <div class="red"></div>
                  <div>离线</div>
                </div>
                <div class="status flex" v-if="scope.row.status == 5">
                  <div class="green"></div>
                  <div>在线</div>
                </div>
                <div class="status flex" v-if="scope.row.status == 4">
                  <div class="yellow"></div>
                  <div>未激活</div>
                </div>
              </div>
            </template>
            <template slot="operation" slot-scope="scope">
              <div class="flex table-edit">
                <p
                  slot="operation"
                  :class="scope.row.status !== 4 ? 'color2' : 'not_p'"
                  @click="openDetail(scope.row)"
                >
                  详情
                </p>
                <p></p>
                <p @click="fn_dele(scope.row)" class="color2">删除</p>
                <p></p>
                <p @click="fn_toDeviceDetail(scope.row, 0)" class="color2">
                  子设备({{ scope.row.subDeviceNum }})
                </p>
              </div>
            </template>
          </iot-table>
        </div>
      </div>
      <!-- 分页 -->
      <div class="produce-bottom" v-if="tableData.length">
        <iot-pagination
          :pagination="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      <!-- 弹出框 -->
      <iot-dialog
        :visible.sync="visible"
        :title="title"
        :width="dialogWidth"
        :showLoading="true"
        :top="'30vh'"
        @callbackSure="fn_sure"
      >
        <template #body> 您确定要删除网关设备么，删除后无法还原！ </template>
      </iot-dialog>
    </div>
  </div>
</template>

<script>
import IotTable from "@/components/iot-table";
// import FormSearch from "@/components/form-search";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotButton from "@/components/iot-button";
import { getTableList, getStatistics, deleDevice } from "@/api/monitorMaintain";
export default {
  name: "Device",
  components: {
    IotTable,
    // FormSearch,
    IotPagination,
    IotDialog,
    IotButton,
  },
  data() {
    return {
      static: {
        //统计数据
        gatewayActiveCount: null,
        gatewayCount: null,
        gatewayOfflineCount: null,
        gatewayOnlineCount: null,
        todayEventCount: null,
      },
      tableData: [],
      loading: true,
      columns: [
        { label: "DeviceName", prop: "deviceName" },
        { label: "设备名称", prop: "aliasName" },
        { label: "所属产品", prop: "productName" },
        { label: "归属项目", prop: "projectName" },
        { label: "状态", prop: "status", slotName: "status" },
        { label: "最后一次上线时间", prop: "lastReportTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      searchOptonsList: [
        { name: "全部", value: "0" },
        { name: "DeviceName", value: "1" },
        { name: "设备名称", value: "2" },
        { name: "在线状态", value: "3" },
      ],
      StatusList: [
        { name: "在线", value: "5" },
        { name: "离线", value: "6" },
        { name: "未激活", value: "4" },
      ],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      params: {
        productName: "通用标准网关",
        aliasName: "",
        deviceName: "",
        type: "0",
        status: "",
      },
      visible: false,
      dialogWidth: "718px",
      title: "确定删除网关设备？",
      deleId: "",
    };
  },
  created() {
    this.getList();
    this.gatewayStatistics();
  },
  methods: {
    getList() {
      //获取网关列表
      getTableList({ ...this.params, ...this.pagination })
        .then((res) => {
          const { data } = res;
          this.tableData = data.records;
          this.pagination.total = data.total;
          this.pagination.current = data.current;
          this.pagination.pages = data.pages;
          this.pagination.size = data.size;
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 删除网关
    fn_dele(row) {
      this.visible = true;
      this.deleId = row.id;
    },
    //弹出框确定
    fn_sure() {
      let id = {
        ids: this.deleId,
      };
      deleDevice(id).then((res) => {
        console.log(res);
        if (200 == res.code) {
          this.$newNotify.success({
            message: res.message,
          });
          this.visible = false;
          this.getList();
          this.gatewayStatistics();
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    gatewayStatistics() {
      //获取网关统计数量
      let params = {
        productName: "通用标准网关",
      };
      getStatistics(params)
        .then((res) => {
          if (200 == res.code) {
            const { data } = res;
            this.static = data;
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },

    // 查询
    handleSearch() {
      this.getList();
    },

    //清除查询关键字
    clear_optionsData(event) {
      this.params.aliasName = '',
      this.params.deviceName = '',
      this.params.status = ''
    },
    fn_clear_search_info() {
      this.params = {
        type:"0",
        aliasName: "",
        deviceName: "",
        productName: "通用标准网关",
        status:""
      };
      this.pagination.current = 1;
      this.getList();
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.getList();
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.getList();
    },
    fn_toDeviceDetail(row, num) {
      console.log(row.id);
      this.$router.push({
        path: "/deviceDetail",
        query: {
          id: row.id,
          // title: data.deviceName,
          // status: data.status,
          // key: data.nodeTypeKey,
          num: num,
        },
      });
    },
    //打开详情页面
    openDetail(row) {
      // console.log(row);

      if (row.status !== 4) {
        this.$router.push({
          path: "/gatewaydetail",
          query: {
            id: row.id,
            productKey: row.productKey,
            deviceName: row.deviceName,
            status: row.status,
            key: row.nodeTypeKey,
          },
        });
      }
    },
    open_todayDatail() {
      this.$router.push({
        path: "/gatewaytodayevent",
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#main {
  .product_bg {
    width: 100%;
    height: 136px;
    background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px 0px;
    background-size: 100%;
    padding: 35px 0px 0px 32px;
    .bg_title {
      font-weight: 500;
      font-size: 22px;
      line-height: 30px;
      color: #333333;
    }
    .bg_desc {
      font-weight: 400;
      font-size: 16px;
      line-height: 30px;
      color: #77797c;
    }
  }
  .content {
    width: 100%;
    padding: 32px 32px 0 32px;
    .content-top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      // padding-top: 30px;
      .box {
        width: 306px;
        height: 102px;
        display: flex;
        padding: 0px 60px 0px 32px;
        box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
        border-radius: 2px;
        .num {
          width: 153px;
          display: flex;
          justify-content: left;
          align-items: center;
          img {
            width: 60%;
            vertical-align: middle;
            text-align: center;
          }
        }
        .name {
          // font-family: YSBT;
          width: 153px;
          text-align: right;
          display: flex;
          justify-content: right;
          align-items: center;
          p {
            text-align: center;
            font-size: 14px;
            font-weight: 400;
            line-height: 16.41px;
            color: #515151;
            font-family: HarmonyOS Sans SC;
            margin-top: 9px;
            .todayDatail {
              color: #3e8fff;
              font-weight: normal;
            }
            .todayDatail:hover {
              cursor: pointer;
            }
          }
          span {
            font-size: 28px;
            font-weight: 700;
            line-height: 28.13px;
          }
        }
      }
    }
    .table {
      width: 100%;
      padding-top: 32px;
      .searchNav {
        width: 100%;
        // background-color: rebeccapurple;
        display: flex;
        justify-content: end;
        align-items: center;
        .el-select {
          width: 188px;
          border-radius: 0;
          margin-right: 14px;
        }
        .searh_optionsBox {
          .el-input {
            width: 188px !important;
          }
          .el-range-editor {
            border-radius: 0;
            :deep(.el-input__inner) {
              padding: 0;
            }
            :deep(.el-input__icon) {
              height: auto;
            }
            :deep(.el-range-separator) {
              height: 32px;
            }
          }
          .el-select {
            width: 188px;
            border-radius: 0;
            margin-right: 14px;
            .el-select-dropdown__item {
              height: 38px;
              line-height: 38px;
            }
          }
          :deep(.el-input__inner) {
            border-radius: 0;
          }
          .alarm-input {
            width: 240px;
            margin-right: 14px;
          }
        }
        :deep(.el-input__inner) {
          /* 移除边框的圆角 */
          border-radius: 0;
          font-family: H_Medium;
        }
        :deep(.el-option) {
          margin: 0px;
        }
      }
      .tableContent {
        margin-top: 16px;
        .table-edit {
          display: flex;
          align-items: center;
          p {
            flex-shrink: 0;
            cursor: pointer;
          }
          p:nth-child(2) {
            margin: 0px 12px;
            width: 1px;
            height: 13px;
            border: 1px solid #ededed;
          }
          p:nth-child(4) {
            margin: 0px 12px;
            width: 1px;
            height: 13px;
            border: 1px solid #ededed;
          }

          .not_p {
            cursor: not-allowed;
            color: #178dff;
          }
        }
        .table-status {
          .status {
            .red {
              background: #ff4d4f;
              width: 8px;
              height: 8px;
              border-radius: 4px;
              margin-top: 8px;
              margin-right: 6px;
            }
            .green {
              background: #00c250;
              width: 8px;
              height: 8px;
              border-radius: 4px;
              margin-top: 8px;
              margin-right: 6px;
            }
            .yellow {
              background: #e6a23c;
              width: 8px;
              height: 8px;
              border-radius: 4px;
              margin-top: 8px;
              margin-right: 6px;
            }
          }
        }
      }
    }
    .produce-bottom {
      text-align: right;
      margin-top: 16px;
      padding-bottom: 10px;
    }
  }
}
</style>