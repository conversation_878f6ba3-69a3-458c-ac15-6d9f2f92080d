import request from "./index";
import { BASE_SERVER } from "../conf/env";

const baseServer = BASE_SERVER;
const gatewayMaintenance = `${baseServer}/tenant/device`;
const gatewayStatistics = `${baseServer}/tenant/gateway/statistics`;
const gatewayDetail = `${baseServer}/tenant/universalGateway/monitor`

/**
 * @desc 运维设备列表
 * @params data
 * @returns
 */
export const getTableList = (params) => {
    return request({
      url: `${gatewayMaintenance}/list`,
      method: "get",
      params,
    });
  };


  /**
 * @desc 删除设备
 * @params data
 * @returns
 */
export const deleDevice = (params) => {
    return request({
      url: `${gatewayMaintenance}/remove`,
      method: "delete",
      params,
    });
  };


/**
 * @desc 网关统计设备列表
 * @params data
 * @returns
 */
export const getStatistics = (params) => {
    return request({
      url: `${gatewayStatistics}/list`,
      method: "get",
      params,
    });
  };


  /**
 * @desc 网关详情页基本信息
 * @params data
 * @returns
 */
export const getfoundationInfo = (params) => {
    return request({
      url: `${gatewayDetail}/baseInfo`,
      method: "get",
      params,
    });
  };

   /**
 * @desc 网关详情页异常事件数据
 * @params data
 * @returns
 */
export const getabnormalEvents = (params) => {
    return request({
      url: `${gatewayDetail}/statistics`,
      method: "get",
      params,
    });
  };

     /**
 * @desc 网关详情页今日连接器处理数据监测
 * @params data
 * @returns
 */
export const getconnectorData = (params) => {
    return request({
      url: `${gatewayDetail}/connectorFlow`,
      method: "get",
      params,
    });
  };

/**
 * @desc 网关详情页今日连接器处理数据监测
 * @params data
 * @returns
 */
export const getflowData = (params) => {
    return request({
      url: `${gatewayDetail}/gatewayFlow`,
      method: "get",
      params,
    });
  };


  /**
 * @desc 异常事件监控详情
 * @params data
 * @returns
 */
export const getEventsDatail = (params) => {
    return request({
      url: `${gatewayDetail}/eventQuery`,
      method: "get",
      params,
    });
  };

   /**
 * @desc 异常事件处理
 * @params data
 * @returns
 */
export const handleEvents = (data) => {
    return request({
      url: `${gatewayDetail}/alarmEventHandle`,
      method: "post",
      data,
    });
  };