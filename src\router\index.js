/*
 * @Author: lb <EMAIL>
 * @Date: 2021-11-03 13:39:56
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2022-05-13 14:36:51
 * @FilePath: \tenant-web\src\router\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import Vue from "vue";
import VueRouter from "vue-router";
import routes from "./route";

Vue.use(VueRouter);

// 解决 vue-router 3.0+ 跳转相同地址会报错误的问题
const ROUTER_PUSH = VueRouter.prototype.push;
const ROUTER_PRLACE = VueRouter.prototype.replace;
VueRouter.prototype.push = function push (location, onComplete, onAbort) {
    if (onComplete || onAbort) {
        return ROUTER_PUSH.call(this, location, onComplete, onAbort)
    }
    return ROUTER_PUSH.call(this, location).catch(err => err)
};

VueRouter.prototype.replace = function replace (location, onComplete, onAbort) {
    if (onComplete || onAbort) {
        return ROUTER_PRLACE.call(this, location, onComplete, onAbort)
    }
    return ROUTER_PRLACE.call(this, location).catch(err => err)
}

const router = new VueRouter({
    scrollBehavior (to, from, savedPosition) {
        // savedPosition 这个参数当且仅当导航 (通过浏览器的 前进/后退 按钮触发) 时才可用  效果和 router.go() 或 router.back()
        if (savedPosition) {
            // 返回savedPosition 其实就是 当用户点击 返回的话，保持之前游览的高度
            return savedPosition;
        } else {
            if (from.meta && from.meta.keepAlive) {
                from.meta.savedPosition = document.body.scrollTop;
            }
            return {
                x: 0,
                y: to.meta.savedPosition || 0,
            };
        }
    },
    mode: "history",
    routes: routes,
});

export default router;
