<template>
	<div class="model">
		<h4>忘记密码</h4>
		<div class="form">
			<el-form ref="form" :model="form" :rules="rules">
				<el-form-item label="" prop="phone">
					<div class="form-item">
						<!-- <p><span>*</span>手机号码</p> -->
						<el-input
							v-model="form.phone"
							maxlength="11"
							tabindex="-1"
							placeholder="手机号"
						></el-input>
					</div>
				</el-form-item>
			</el-form>
			<div class="handle-next" @click="handleConfirm">
				<span>下一步</span>
			</div>
			<div class="handle-login">
				<span @click="handleLogin">已有账号，立即登录</span>
			</div>
		</div>
	</div>
</template>

<script>
import { checkFieldPhone, checkPhoneMustExists } from '@/api/user'
export default {
	data() {
		const phoneBlur = (rule, value, callback) => {
			if (value === '') {
				callback(new Error('请输入手机号'))
			} else {
				let reg = /^[1][3,4,5,7,8,9][0-9]{9}$/
				if (reg.test(value)) {
					checkPhoneMustExists({
						phone: value,
					})
						.then((res) => {
							if (res.code == 200) {
								// 可以使用
							} else {
								callback(res.message)
							}
						})
						.finally(() => {
							callback()
						})
				} else {
					callback(new Error('请填写正确的手机号'))
				}
			}
		}
		return {
			form: {
				phone: '',
			},
			rules: {
				phone: [
					{
						required: true,
						trigger: 'blur',
						validator: phoneBlur,
					},
					// {
					//   pattern: /^1[34578]\d{9}$/,
					//   message: "请填写正确的手机号",
					// },
					{ min: 11, max: 11, message: '请填写正确的手机号' },
				],
			},
		}
	},
	props: {
		step: {
			type: [Number, String],
		},
	},
	methods: {
		handleLogin() {
			this.$emit('route')
		},
		handleConfirm() {
			this.$refs.form.validate((valid) => {
				if (valid) {
					this.$emit('next', {
						step: 1,
						phone: this.form.phone,
					})
				} else {
					return false
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.model {
	width: 1020px;
	height: 460px;
	margin: 0 auto;
	background: #ffffff;
	box-shadow: 0px 48px 48px rgba(0, 0, 0, 0.05);
	border-radius: 3px;
	h4 {
		height: 98px;
		line-height: 98px;
		color: #333333;
		font-weight: 500;
		font-size: 28px;
		text-align: center;
		border-bottom: 1px solid #f5f5f5;
	}
	.form {
		width: 330px;
		margin: 0 auto;
		padding-top: 48px;
		.form-item {
			align-items: center;
			p {
				font-size: 14px;
				line-height: 16px;
				color: #262626;
				padding-bottom: 8px;
				span {
					color: #f53e3e;
				}
			}
			/deep/ {
				.el-form-item {
					margin-bottom: 34px;
				}
				.el-input {
					input {
						border-radius: 0;
						height: 42px;
						font-size: 14px;
						border: 1px solid #e4e7ec;
					}
					input:focus,
					input:hover {
						border: 1px solid #018aff;
					}
					input::placeholder {
						color: #bfbfbf;
					}
				}
			}
		}
		.handle-next {
			height: 42px;
			text-align: center;
			background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
			color: #ffffff;
			font-size: 16px;
			line-height: 42px;
			margin-top: 34px;
			cursor: pointer;
		}
		.handle-login {
			text-align: right;
			padding-top: 24px;
			span {
				color: #0088fe;
				cursor: pointer;
				font-size: 14px;
				line-height: 16px;
			}
		}
	}
}
</style>
