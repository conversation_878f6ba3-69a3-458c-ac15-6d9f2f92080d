<template>
  <div class="item">
    <el-form-item prop="data.type">
      <div class="form-item">
        <p class="form-item-label">数据类型</p>
        <el-radio-group
          v-model="type"
          size="medium"
          @change="radioChange('type')"
        >
          <el-radio-button label="int">int</el-radio-button>
          <el-radio-button label="float">float</el-radio-button>
          <el-radio-button label="double">double</el-radio-button>
          <el-radio-button label="enum">enum</el-radio-button>
          <el-radio-button label="bool">bool</el-radio-button>
          <el-radio-button label="text">text</el-radio-button>
        </el-radio-group>
        <p class="gap"></p>
        <el-radio-group
          v-model="type"
          size="medium"
          @change="radioChange('type')"
        >
          <el-radio-button label="time">time</el-radio-button>
          <el-radio-button label="object" v-if="!isChild"
            >object</el-radio-button
          >
          <el-radio-button label="array" v-if="!isChild">array</el-radio-button>
        </el-radio-group>
      </div>
    </el-form-item>
    <!-- int / float / double -->
    <template v-if="type === 'int' || type === 'float' || type === 'double'">
      <el-form-item prop="data.rules.min">
        <div class="form-item">
          <p class="form-item-label">取值范围</p>
          <div class="int-range flex">
            <!-- :style="{ border: isIntFocus ? ' 1px solid #018AFF' : '' }" -->
            <el-input
              type="text"
              v-model="min"
              placeholder="最小值"
              @focus="isIntFocus = true"
              @blur="isIntFocus = false"
              @input="inputChange('min', 'int')"
            />
            <span>-</span>
            <el-input
              type="text"
              v-model="max"
              placeholder="最大值"
              @focus="isIntFocus = true"
              @blur="isIntFocus = false"
              @input="inputChange('max', 'int')"
            />
          </div>
        </div>
      </el-form-item>
      <el-form-item prop="data.rules.step">
        <div class="form-item">
          <p class="form-item-label">步长</p>
          <el-input
            type="text"
            v-model="step"
            placeholder="请输入步长"
            @change="inputChange('step', 'int')"
          ></el-input>
        </div>
      </el-form-item>

      <el-form-item prop="data.rules.unit">
        <div class="form-item">
          <p class="form-item-label">单位</p>
          <el-select v-model="unit" placeholder="请选择" @change="selectChange">
            <!-- <el-option label="无" value="无"></el-option> -->
            <el-option
              v-for="item in unitDefaultList"
              :key="item.id"
              :label="`${item.dictValue} / ${item.dictKey}`"
              :value="item.id"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
    </template>

    <!-- int / float / double -->
    <!-- enum -->
    <template v-if="type === 'enum'">
      <el-form-item>
        <div class="form-item">
          <p class="form-item-label">枚举项</p>
          <div class="enum flex" v-for="(item, index) in enumList" :key="index">
            <div
              class="enum-range flex"
              :style="{ border: item.isIntFocus ? ' 1px solid #018AFF' : '' }"
            >
              <input
                type="text"
                v-model="item.key"
                placeholder="数值"
                @focus="item.isIntFocus = true"
                @blur="item.isIntFocus = false"
                @input="inputChange('enumList', 'enum')"
              />
              <span>-</span>
              <input
                type="text"
                v-model="item.value"
                placeholder="说明"
                @focus="item.isIntFocus = true"
                @blur="item.isIntFocus = false"
                @input="inputChange('enumList', 'enum')"
              />
            </div>
            <p class="delete" @click="enmuDelete(index)">删除</p>
          </div>
          <div class="enum-add">
            <p @click="menuAdd">+ 添加枚举</p>
          </div>
        </div>
      </el-form-item>
      <div class="el-form-tips" v-if="enumTips">{{ enumTips }}</div>
    </template>

    <!-- enum -->
    <!-- bool -->
    <template v-if="type === 'bool'">
      <el-form-item>
        <div class="form-item">
          <p class="form-item-label">布尔值</p>
          <div class="bool">
            <div class="bool-item flex">
              <p>0 -</p>
              <el-input
                v-model="boolFalse"
                placeholder="关闭"
                @change="inputChange('boolFalse', 'bool')"
              ></el-input>
            </div>
            <div class="bool-item flex">
              <p>1 -</p>
              <el-input
                v-model="boolTrue"
                placeholder="开启"
                @change="inputChange('boolTrue', 'bool')"
              ></el-input>
            </div>
          </div>
        </div>
      </el-form-item>
      <div class="el-form-tips" v-if="boolTips">{{ boolTips }}</div>
    </template>

    <!-- bool -->

    <!-- text -->
    <template v-if="type === 'text'">
      <el-form-item prop="data.rules.length">
        <div class="form-item">
          <p class="form-item-label">文本长度</p>
          <el-input
            v-model="length"
            @change="inputChange('length', 'int')"
            maxlength="1000"
          ></el-input>
        </div>
      </el-form-item>
    </template>
    <!-- text -->
    <!-- time -->
    <template v-if="type === 'time'">
      <el-form-item>
        <div class="form-item">
          <p class="form-item-label">时间格式</p>
          <el-input
            disabled
            placeholder="字符串，unix 时间戳，精确到毫秒"
          ></el-input>
        </div>
      </el-form-item>
    </template>

    <!-- time -->

    <!-- struct -->
    <template v-if="type === 'object'">
      <json-struct
        ref="struct"
        :data="jsonData"
        key="jsonStruct"
        @addAttribute="(data) => addAttribute(data, 'object')"
        @editAttribute="(data) => editAttribute(data, 'object')"
        @deleteAttribute="(data) => deleteAttribute(data, 'object')"
      />
      <div class="el-form-tips" v-if="objectTips">{{ objectTips }}</div>
    </template>
    <!-- struct -->

    <!-- array -->
    <template v-if="type === 'array'">
      <el-form-item>
        <div class="form-item">
          <p class="form-item-label">元素类型</p>
          <el-radio-group
            v-model="arrayType"
            size="medium"
            @change="radioChange('arrayType')"
          >
            <el-radio-button label="int">int</el-radio-button>
            <el-radio-button label="float">float</el-radio-button>
            <el-radio-button label="double">double</el-radio-button>
            <el-radio-button label="text">text</el-radio-button>
            <el-radio-button label="object">object</el-radio-button>
          </el-radio-group>
        </div>
      </el-form-item>

      <!-- <el-form-item v-if="arrayType !== 'object'" prop="data.rules.size"> -->
      <el-form-item  prop="data.rules.size">
        <div class="form-item">
          <p class="form-item-label">元素个数</p>
          <el-input
            v-model="size"
            placeholder=""
            @change="inputChange('size', 'int')"
          ></el-input>
        </div>
      </el-form-item>
      <json-struct
        v-if="arrayType === 'object'"
        ref="arrayStruct"
        key="arrayStruct"
        :data="arrayData"
        @addAttribute="(data) => addAttribute(data, 'array')"
        @editAttribute="(data) => editAttribute(data, 'array')"
        @deleteAttribute="(data) => deleteAttribute(data, 'array')"
      />
      <div class="el-form-tips" v-if="arrayTips">{{ arrayTips }}</div>
    </template>

    <!-- array -->
  </div>
</template>

<script>
import jsonStruct from "../json-struct";
export default {
  data() {
    return {
      isIntFocus: false,
      enumList: [
        {
          isIntFocus: false,
          key: "",
          value: "",
        },
      ],
      boolFalse: "", //bool false 描述
      boolTrue: "", // bool true  描述
      // 表单字段
      type: "int", //数据类型
      //   int  ----
      min: "",
      max: "",
      step: "", //步长
      unit: "", //单位
      unitDefaultList: [],
      //   int   ----end
      length: "", //text  文本类型  长度限制
      jsonData: [], //json 数据
      arrayLength: "", //数组    选择基本类型时
      size: "",
      arrayType: "int", //子集  数据类型
      arrayData: [],

      // 校验
      enumTrue: false,
    };
  },
  props: {
    isChild: {
      type: Boolean,
      default: false,
    },
    form: {
      type: Object,
    },
    enumTips: {
      type: String,
    },
    boolTips: {
      type: String,
    },
    objectTips: {
      type: String,
    },
    arrayTips: {
      type: String,
    },
    unitList: {
      type: Array,
    },
  },
  watch: {
    type() {},
    form: {
      deep: true,
      handler: "formater",
    },
    unitList: {
      deep: true,
      handler: "defaultUnit",
    },
  },
  components: { jsonStruct },
  mounted() {
    this.formater();
    this.defaultUnit();
  },
  methods: {
    defaultUnit() {
      if (this.unitList) {
        this.unitDefaultList = this.unitList;
      }
    },
    formater() {
      this.type = (this.form.data && this.form.data.type) || "int";
      // object 回显
      this.jsonData = this.form.data.jsonData || [];
      // array 回显
      this.arrayType = this.form.data.rules.arrayType || "int";
      this.size = this.form.data.rules.size || "";
      this.arrayData = this.form.data.arrayData || [];
      // 基本类型数据回显-------  int  float double text
      this.min = (this.form.data.rules && this.form.data.rules.min) || "";
      this.max = (this.form.data.rules && this.form.data.rules.max) || "";
      this.step = (this.form.data.rules && this.form.data.rules.step) || "";
      if (this.form.data.rules && this.form.data.rules.unit) {
        this.unit = `${this.form.data.rules.unitName} / ${this.form.data.rules.unit}`;
      } else {
        this.unit = "";
      }
      this.length = (this.form.data.rules && this.form.data.rules.length) || "";
      // ------
      // bool
      this.boolFalse =
        (this.form.data.rules && this.form.data.rules["0"]) || "";
      this.boolTrue = (this.form.data.rules && this.form.data.rules["1"]) || "";
      // enum
      this.enumList = (this.form.data && this.form.data.enumList) || [];
    },
    menuAdd() {
      this.enumList.push({
        isIntFocus: false,
        key: "",
        value: "",
      });
    },
    enmuDelete(index) {
      this.enumList.splice(index, 1);
    },
    addAttribute(data, type) {
      let params = {
        data,
        type,
      };
      this.$emit("addAttribute", params);
    },
    editAttribute(data, type) {
      let params = {
        data,
        type,
      };
      this.$emit("editAttribute", params);
    },
    deleteAttribute(data, type) {
      let params = {
        data,
        type,
      };
      this.$emit("deleteAttribute", params);
    },
    emitChange(data) {
      this.$emit("change", data);
    },
    // 值变化事件--------------------------------
    radioChange(key) {
      let name = key;
      if (key === "type") {
        name = "data." + key;
      } else {
        // 属性为数组
        name = "data.rules." + key;
      }
      this.emitChange({
        key: name,
        value: this[key],
      });
    },
    inputChange(key, type) {
      let name = "";
      if (type === "bool") {
        if (key === "boolFalse") {
          name = "data.rules." + "0";
        } else {
          name = "data.rules." + "1";
        }
      } else if (type === "int") {
        name = "data.rules." + key;
      } else if (type === "enum") {
        name = "data." + key;
      }
      this.emitChange({
        key: name,
        value: this[key],
      });
    },
    selectChange(value) {
      // 只有一个单位的选择  暂时固定处理 value 位id
      // 如果选择数据字典“单位”，需在JSON格式里面同时填充单位标识和单位说明，对应 unit、unitName
      let name = "data.rules.unit";
      let unitName = "data.rules.unitName";
      let unitNameVal = this.unitList.filter((item) => item.id == value);
      this.emitChange({
        key: name,
        value: unitNameVal.length > 0 ? unitNameVal[0].dictKey : "无",
      });
      this.emitChange({
        key: unitName,
        value: unitNameVal.length > 0 ? unitNameVal[0].dictValue : "无",
      });
    },
    // 重置数据  基本类型
    resetData() {
      this.type = "int";
      this.min = "";
      this.max = "";
      this.step = "";
      this.unit = "";
      this.enumList = [
        {
          isIntFocus: false,
          key: "",
          value: "",
        },
      ];
      this.boolFalse = ""; //bool false 描述
      this.boolTrue = ""; // bool true  描述
      this.length = "";
    },
    resetDataComplex() {
      this.resetData();
      this.jsonData = [];
      this.arrayData = [];
    },
  },
};
</script>

<style lang="scss" scoped>
.item {
  .form-item {
    .int-range {
      // border: 1px solid #eeeff1;
      transition: all 0.2s;
      input {
        flex: 1;
        border: none;
        outline: none;
        color: #515151;
        padding: 0 14px;
        font-size: 14px;
      }
      span {
        text-align: center;
        color: #515151;
      }
    }
    .enum {
      align-items: center;
      margin-bottom: 8px;
      /deep/ .el-form-item__content {
        line-height: 34px !important;
      }
      /deep/ .form-item-label {
        line-height: 34px !important;
      }
      .enum-range {
        flex: 1;
        align-items: center;
        border: 1px solid #eeeff1;
        transition: all 0.2s;
        height: 34px;
        input {
          flex: 1;
          border: none;
          outline: none;
          color: #515151;
          padding: 0 14px;
          font-size: 14px;
        }
        span {
          text-align: center;
          color: #515151;
        }
      }
      .delete {
        flex-shrink: 0;
        font-size: 14px;
        color: #018aff;
        padding-left: 14px;
        cursor: pointer;
      }
    }
    .enum-add {
      p {
        background: #ebf6ff;
        width: 160px;
        height: 34px;
        color: #0088fe;
        font-size: 14px;
        line-height: 34px;
        text-align: center;
        cursor: pointer;
      }
    }
    // bool
    .bool {
      .bool-item {
        align-items: center;
        margin-bottom: 8px;
        p {
          flex-shrink: 0;
          color: #333333;
          font-size: 14px;
          padding-right: 14px;
        }
      }
    }
  }

  /deep/ {
    .el-select {
      width: 100%;
    }
    .el-radio-button__inner {
      width: 106px !important;
      padding: 9px 18px;
      user-select: none;
    }
    .el-input__inner {
      border-radius: 0;
      height: 34px;
    }
    .el-radio-button__inner {
      width: 106px;
      background: #ffffff;
      border-radius: 0px;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: linear-gradient(180deg, #0088fe, #006eff 100%);
    }
    .el-radio-button:first-child .el-radio-button__inner {
      border-radius: 0px;
    }
    .el-radio-button:last-child .el-radio-button__inner {
      border-radius: 0px;
    }
    .form-item-label {
      line-height: 30px !important;
    }
    .el-form-item__content {
      line-height: 30px !important;
    }
    .el-form-item {
      margin-bottom: 17px !important;
    }
  }
}
.gap {
  height: 8px;
}
.el-form-tips {
  font-size: 12px;
  font-weight: 400;
  color: #f56c6c;
  margin-top: -16px;
  // margin-bottom: 9px;
}
</style>
