<template>
  <div class="onlineDebug">
    <div class="device-top flex">
      <p>请选择设备</p>
      <el-select
        placeholder="请选择产品"
        v-model="productKey"
        filterable
        @change="handleReset"
      >
        <template slot="empty">
          <div class="empty-project">
            <span>暂无数据,请去创建产品</span>
          </div>
        </template>
        <el-option
          v-for="item in productOptions"
          :key="item.productKey"
          :label="item.name"
          :value="item.productKey"
        ></el-option>
      </el-select>
      <el-select
        placeholder="请选择设备"
        :disabled="productKey == ''"
        v-model="deviceName"
        filterable
        @change="handleReset"
      >
        <template slot="empty">
          <div class="empty-project">
            <span>暂无数据,请去创建设备</span>
          </div>
        </template>
        <el-option
          v-for="item in deviceOptions"
          :key="item.id"
          :label="item.name"
          :value="item.name"
        ></el-option>
      </el-select>
    </div>

    <template v-if="productOptions.length == 0">
      <div class="device-content">
        <div class="empty-content">
          <img src="~@/assets/images/empty/emptyData.png" alt />
          <div class="empty-route flex">
            <span>您还没有创建产品，请去</span>
            <p @click="handleRouteProduct">创建产品</p>
          </div>
        </div>
      </div>
    </template>
    <template v-else>
      <div
        class="device-content"
        v-if="
          productKey == '' || (deviceOptions.length != 0 && deviceName == '')
        "
      >
        <div class="empty-content">
          <img src="~@/assets/images/empty/emptyData.png" alt />
          <div class="empty-route flex">
            <span>请选择产品设备后再进行调试</span>
          </div>
        </div>
      </div>
    </template>

    <div
      class="device-content"
      v-if="productKey != '' && deviceOptions.length == 0"
    >
      <div class="empty-content">
        <img src="~@/assets/images/empty/emptyData.png" alt />
        <div class="empty-route flex">
          <span>该产品没有添加设备，请去</span>
          <p @click="handleRouteDevice">添加设备</p>
        </div>
      </div>
    </div>
    <div
      class="device-content flex"
      v-if="productKey !== '' && deviceName !== ''"
    >
      <online-config
        ref="online"
        :tenant_id="tenant_id"
        :productKey="productKey"
        :deviceName="deviceName"
        :productId="productId"
        :online="onlineFlag"
      ></online-config>
      <debug-log
        ref="debuglog"
        :deviceName="deviceName"
        :productKey="productKey"
        :online="onlineFlag"
        :logList="logList"
      ></debug-log>
    </div>
  </div>
</template>
<script>
import {
  getProductSelect,
  getDeviceSelect,
  getDeviceiStatus,
} from "@/api/onlineDubug";
import onlineConfig from "./component/onlineConfig";
import DebugLog from "./component/debugLog";
import { mapGetters } from "vuex";
export default {
  name: "onlineDebug",
  components: {
    DebugLog,
    onlineConfig,
  },
  data() {
    return {
      tenant_id: "",
      productKey: "",
      productOptions: [],
      deviceName: "",
      deviceOptions: [],
      productId: "",
    };
  },
  computed: {
    ...mapGetters(["logData", "onlineFlag", "xtLogsSocket"]),
    logList() {
      return this.logData;
    },
  },
  created() {
    this.tenant_id = this.Encrypt.decryptoByAES(
      localStorage.getItem("tenant_id")
    );
    this.fn_get_product_select();
    this.$store.dispatch("fn_init_onlinesocket");
  },
  methods: {
    // 初始化设备状态
    http_getDeviceiStatus() {
      const data = {
        productKey: this.productKey,
        deviceName: this.deviceName,
      };
      getDeviceiStatus(data).then((res) => {
        if (res.code == 200) {
          this.$store.commit("MUTATIONS_ONLINE__ONLINEFLAGE", res.data);
        } else if (res.code !== 200) {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 产品列表下拉
    fn_get_product_select() {
      getProductSelect().then((res) => {
        if (res.code == 200) {
          this.productOptions = res.data;
          if (this.productOptions.length) {
            // this.productKey = this.productOptions[0].productKey
          }
        } else if (res.code !== 200) {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    fn_get_device_select(productKey) {
      let params = {
        productKey: productKey,
      };
      getDeviceSelect(params).then((res) => {
        if (res.code == 200) {
          this.deviceOptions = res.data;
          if (this.deviceOptions.length) {
            // this.deviceName = this.deviceOptions[0].id
          }
        }
      });
    },
    // 跳转产品
    handleRouteProduct() {
      this.$router.push("./product");
    },
    // 跳转设备
    handleRouteDevice() {
      this.$router.push("./device");
    },
    // 下架选项清空
    handleReset() {
      if (this.$refs.online && this.$refs.debuglog) {
        // this.$refs.online.handleReset()
        // this.$refs.debuglog.handleReset()
      }
    },
  },
  watch: {
    productKey(newVal) {
      this.productKey = newVal;
      if (newVal)
        this.productId = this.productOptions.find(
          (item) => item.productKey === newVal
        ).id;
      this.deviceName = "";
      this.$store.commit("MUTATIONS_ONLINE__ONLINEFLAGE", false);
      this.$store.commit("MUTATIONS_ONLINE__DEBUGLOGCLEAR");
      this.$store.dispatch("fn_logstatus__unsubscribe");
      this.fn_get_device_select(this.productKey);
    },
    deviceName(newVal) {
      this.http_getDeviceiStatus();

      this.$store.commit("MUTATIONS_ONLINE__ONLINEFLAGE", false);
      this.$store.commit("MUTATIONS_ONLINE__DEBUGLOGCLEAR");
      this.$store.dispatch("fn_logstatus__unsubscribe");
      this.$store.dispatch(
        "fn_deviceStatus__subscribe",
        `${this.productKey}_${newVal}`
      );
    },
  },
  beforeDestroy() {
    this.$store.commit("MUTATIONS_ONLINE__DEBUGLOGCLEAR");
    if (this.xtLogsSocket) {
      this.$store.dispatch("fn_logstatus__unsubscribe");
      this.xtLogsSocket.asyncDisconnect();
      this.$store.commit("MUTATIONS_ONLINE__WEBSOCKET", null);
    }
  },
};
</script>
<style lang="scss" scoped>
.onlineDebug {
  padding: 32px 32px 0px 32px;
  .device-top {
    margin-top: 18px;
    p {
      height: 34px;
      line-height: 34px;
      font-size: 14px;
      font-weight: 400;
      margin-right: 14px;
    }
    .el-select {
      width: 240px;
      margin-right: 14px;
      /deep/ .el-input__inner {
        border-radius: 0;
      }
    }
  }
  .device-content {
    margin-top: 18px;
    width: 100%;
    .empty-content {
      width: 100%;
      text-align: center;
      margin-top: 150px;
      img {
        margin-bottom: 28px;
      }
      .empty-route {
        margin-left: 44%;
        font-size: 14px;
        span {
          color: #888888;
        }
        p {
          color: #018aff;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
