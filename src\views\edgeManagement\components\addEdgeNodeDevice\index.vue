<template>
  <div>
    <IotDialog
      :visible.sync="visible"
      @close="close_dialog"
      @callbackSure="dialog_sure"
      :title="dialogTitle"
    >
      <template #body>
        <div style="margin-bottom: 10px">节点名称</div>
        <IotInput
          v-model="dialogNodeVal"
          placeholder="请输入节点名称"
          width="240px"
        ></IotInput>
      </template>
    </IotDialog>
  </div>
</template>

<script>
import IotDialog from "@/components/iot-dialog/index.vue";
import IotInput from "@/components/iot-input/index.vue";
export default {
  components: { IotDialog,IotInput },
  props: {
    dialogTitle:{
        type:String
    }
  },
  data(){
    return{
        dialogNodeVal:"",
        visible:false
    }
  },
  methods:{
    close_dialog(){
        this.dialogNodeVal = ""
        this.visible = false
    },
    dialog_sure(){
      if(!this.dialogNodeVal.trim()){
        this.$newNotify.warning({
          message: "节点名称不能为空",
        });
        return
      }
        this.$emit('dialogSure',this.dialogNodeVal)
        this.close_dialog()
    }
  }
};
</script>

<style scoped lang="scss"></style>
