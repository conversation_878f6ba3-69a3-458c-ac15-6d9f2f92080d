<template>
	<el-dialog
		title="提示"
		:visible.sync="dialogVisible"
		width="410px"
		:append-to-body="true"
		top="20vh"
		:before-close="handleClose"
	>
		<div class="content flex">
			<div class="info flex">
				<div class="success-icon flex">
					<img src="~@/assets/images/index/hook-icon.png" alt="" />
				</div>
				<div>
					<h5>{{ title }}</h5>
					<p>{{ time }} 秒后跳转到登录页面</p>
				</div>
			</div>
			<div class="action flex" v-if="isConfirm">
				<p class="btn" @click="handleConfirm">确定</p>
			</div>
		</div>
	</el-dialog>
</template>

<script>
export default {
	data() {
		return {
			dialogVisible: false,
			time: 3,
			timer: null,
		}
	},
	props: {
		title: {
			type: String,
			default: '操作成功',
		},
		isConfirm: {
			type: Boolean,
			default: true,
		},
	},
	methods: {
		open() {
			this.dialogVisible = true
			this.timer = setInterval(() => {
				if (this.time <= 1) {
					this.dialogVisible = true
					this.$router.replace({ path: '/login' })
					clearInterval(this.timer)
				}
				this.time--
			}, 1000)
		},
		handleConfirm() {
			clearInterval(this.timer)
			this.$router.replace({ path: '/login' })
		},
		handleClose() {},
	},
}
</script>

<style lang="scss" scoped>
:deep(.el-dialog) {
	.el-dialog__header,
	.el-dialog__footer {
		display: none;
	}
	.el-dialog__body {
		padding: 0;
	}
}
.content {
	flex-direction: column;
	border-radius: 0px 0px 2px 2px;
	.info {
		align-items: center;
		padding: 32px;
		.success-icon {
			width: 60px;
			height: 60px;
			border-radius: 50%;
			background: #52c41a;
			align-items: center;
			justify-content: center;
			margin-right: 24px;
			img {
				width: 28px;
			}
		}
		h5 {
			color: #333333;
			font-size: 18px;
			line-height: 14px;
			font-weight: normal;
		}
		p {
			color: #888888;
			font-size: 14px;
			line-height: 14px;
			padding-top: 16px;
		}
	}
	.action {
		height: 72px;
		align-items: center;
		justify-content: flex-end;
		background: #fbfbfb;
		padding: 0 32px;
		.btn {
			width: 96px;
			height: 36px;
			line-height: 36px;
			text-align: center;
			background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
			color: #ffffff;
			font-size: 14px;
			cursor: pointer;
			border-radius: 2px;
		}
	}
}
</style>
