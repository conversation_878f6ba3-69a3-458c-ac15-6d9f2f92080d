<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-14 14:46:51
-->
<template>
  <div class="model">
    <div class="model-table">
      <!-- 表格 -->
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="outputPara" slot-scope="scope">
          <el-tooltip
            class="item"
            effect="light"
            placement="top"
            popper-class="event-tooltip"
          >
            <!--  -->
            <template #content>
              <p :style="fn_formatter_style(scope.row)">
                {{ scope.row.outputPara }}
              </p>
            </template>
            <div class="flex">
              <p class="outputPara">{{ scope.row.outputPara }}</p>
              <p v-copy="scope.row.outputPara" class="output-copy color2">
                复制
              </p>
            </div>
          </el-tooltip>
        </template>
        <!-- <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_open(scope.row)" class="color2">查看数据</p>
          </div>
        </template>-->
      </iot-table>
    </div>

    <div class="model-bottom" v-if="tableData.length > 0">
      <iot-pagination
        :pagination="pagination"
        @size-change="sizeChange"
        @current-change="currentChange"
      />
    </div>
  </div>
</template>

<script>
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import { getEventsData } from "@/api/device.js";
import { fn_util__date_format } from "@/util/util";
export default {
  name: "Event",
  components: {
    IotTable,
    IotPagination,
  },
  data() {
    return {
      loading: false,
      pageSource: [],
      tableData: [],
      columns: [
        {
          label: "时间",
          prop: "time",
          // slotName: "time",
        },
        { label: "标识符", prop: "identifier" },
        { label: "事件名称", prop: "eventName" },
        { label: "事件类型", prop: "eventType" },
        {
          label: "输出参数",
          prop: "outputPara",
          width: "400",
          slotName: "outputPara",
        },
        // { label: '操作', prop: 'operation', slotName: 'operation' }
      ],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      startTime: "",
      endTime: "",
      rangeDate: null,
    };
  },
  props: {
    productKey: {
      type: String,
    },
    tenant_id: {
      type: String,
    },
    deviceName: {
      type: String,
    },
    eventType: {
      type: [Number, String],
      default: 1,
    },
    time: {
      type: [Number, String],
    },
  },
  watch: {
    eventType() {
      this.formatTime(this.time);
    },
    time() {
      this.formatTime(this.time);
    },
    rangeDate: {
      deep: true,
      handler: function () {
        this.formatTime(this.time);
      },
    },
  },
  mounted() {
    if (this.time == 1) {
      this.formatTime(this.time);
    }
  },
  methods: {
    formatTime(type) {
      let format;
      let { yy, MM, dd, hh, mm, ss, timestamp } = fn_util__date_format();
      this.endTime = `${yy}-${MM}-${dd} ${hh}:${mm}:${ss}`;
      // timestamp 13位时间戳
      if (type == 1) {
        // 1小时
        let time = timestamp - 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 2) {
        // 24小时
        let time = timestamp - 24 * 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 3) {
        // 7天
        let time = timestamp - 7 * 24 * 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 4) {
        // 自定义
        if (this.rangeDate && this.rangeDate.length > 0) {
          format = fn_util__date_format(this.rangeDate[0]);
          let endFormat = fn_util__date_format(this.rangeDate[1]);
          this.endTime = `${endFormat.yy}-${endFormat.MM}-${endFormat.dd} ${endFormat.hh}:${endFormat.mm}:${endFormat.ss}`;
        } else return;
      }
      this.startTime = `${format.yy}-${format.MM}-${format.dd} ${format.hh}:${format.mm}:${format.ss}`;
      this.getData();
    },
    getData() {
      this.loading = true;
      let params = {
        productKey: this.productKey, //"1mwFX6l9vVM6Tn55" ||
        deviceName: this.deviceName, //"eTrG0jtiVS30r765" ||
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
        startTime: this.startTime,
        endTime: this.endTime,
        // type: this.eventType,
      };
      if (this.eventType != 1) {
        params["type"] = this.eventType;
      }
      getEventsData(params)
        .then((res) => {
          this.pagination.total = res.data.total || 0;
          this.tableData = res.data.records || [];
        })
        .finally(() => {
          this.loading = false;
        });
    },
    currentChange(data) {
      this.pagination.current = data;
      this.getData();
    },
    sizeChange(data) {
      this.pagination.current = 1;
      this.pagination.size = data;
      this.getData();
    },
    fn_open() {
      this.$refs.add.add(this.thingModel);
    },
    fn_formatter_style(row) {
      if (row.outputPara && row.outputPara.length > 1006) {
        return "overflow: auto;height: 100%;max-height: 300px;";
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.model {
  .model-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 18px 0px 18px 0px;
    // .top-left {
    // }
    .top-right {
      align-items: center;
    }
  }
  .model-table {
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
    .outputPara {
      width: 330px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #515151;
      font-size: 14px;
    }
    .output-copy {
      cursor: pointer;
      margin-left: 8px;
    }
  }
  .model-bottom {
    text-align: right;
    margin-top: 18px;
  }
  .del-tips {
    width: 420px;
  }
}

/deep/ {
  .cm-s-idea {
    height: 60vh;
  }
}

/deep/ {
  .el-radio-button__inner {
    width: 110px;
  }
  .el-input__inner {
    border-radius: 0;
    height: 34px;
  }
  .el-textarea__inner {
    border-radius: 0;
    height: 100px;
    color: #515151;

    font-family: H_Medium;
  }
  .el-textarea__inner::placeholder {
    font-family: H_Regular;
    font-weight: normal;
  }
}
</style>
