<template>
  <iot-dialog
    title="告警设备范围"
    top="7vh"
    maxHeight="100vh"
    :visible.sync="visible"
    :width="width"
    :appendBody="true"
    :footer="false"
    @close="handleClose"
  >
    <template #body>
      <div class="content">
        <div class="item">
          <h5>产品<span>*</span></h5>
          <el-select
            v-model="product"
            placeholder="请选择产品"
            filterable
            @change="selectChange"
            :disabled="alreadySource.length !== 0"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="device flex">
          <div class="device-data not">
            <h4>未选择设备：{{ waitCount }}</h4>
            <div class="table">
              <div class="form-item">
                <el-input
                  v-model="notSearchVal"
                  placeholder="请输入DeviceName"
                  clearable
                  @keyup.enter.native="fn_handle__query(true)"
                  @clear="fn_handle__query(true)"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="fn_handle__query(true)"
                  ></i>
                </el-input>
              </div>
              <div class="device-table-content">
                <iot-table
                  :columns="notColumns"
                  :data="notSource"
                  :loading="notLoading"
                  @selection-change="(data) => selectionChange(data, true)"
                >
                  <template #empty>
                    <div class="empty" v-if="isEmpty">
                      该产品暂无设备，请先去<span @click="routeDevice"
                        >添加设备</span
                      >
                    </div>
                  </template>
                </iot-table>
                <div class="pagination flex">
                  <iot-pagination
                    :pagination="notPagination"
                    layout="total, prev, pager, next,  jumper"
                    @current-change="(data) => handleCurrentChange(data, true)"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="action flex">
            <p class="bind" @click="submitBind(true)">
              <span>绑定</span>
              <img src="~@/assets/images/index/arrow-icon.png" alt="" />
            </p>
            <p class="unbound" @click="submitBind(false)">
              <img src="~@/assets/images/index/arrow-icon.png" alt="" />
              <span>解绑</span>
            </p>
          </div>
          <div class="device-data already">
            <h4>已选择设备：{{ doneCount }}</h4>
            <div class="table">
              <div class="form-item">
                <el-input
                  v-model="alreadySearchVal"
                  placeholder="请输入DeviceName"
                  clearable
                  @keyup.enter.native="fn_handle__query(false)"
                  @clear="fn_handle__query(false)"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="fn_handle__query(false)"
                  ></i>
                </el-input>
              </div>
              <div class="device-table-content">
                <iot-table
                  :columns="notColumns"
                  :data="alreadySource"
                  :loading="alreadyLoading"
                  @selection-change="(data) => selectionChange(data, false)"
                ></iot-table>
                <div class="pagination flex">
                  <iot-pagination
                    :pagination="alreadyPagination"
                    layout="total, prev, pager, next,  jumper"
                    @current-change="(data) => handleCurrentChange(data, false)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="mask flex">
          <span>请先选择产品</span>
        </div> -->
      </div>
    </template>
  </iot-dialog>
</template>

<script>
import iotDialog from "@/components/iot-dialog";
import iotTable from "@/components/iot-table";
import iotPagination from "@/components/iot-pagination";
import { gethasEventDeviceSubProduct } from "@/api/device.js";
import {
  getrelatonWaitList,
  getrelatonBindList,
  bindDevice,
  removeDevice,
  getEventList,
} from "@/api/alarmCenter";
export default {
  data() {
    return {
      isclearson: false,
      visible: false,
      product: "",
      productInfo: {
        productKey: "",
      },
      // uuid:'',
      isdisabled: false,
      eventList: [],
      deviceNameWhole: {},
      objs: "",
      c: {},
      options: [
        {
          value: "1",
          label: "测试",
        },
      ],
      notSearchVal: "",
      notColumns: [
        {
          type: "selection",
        },
        {
          prop: "deviceName",
          label: "DeviceName",
          width: 220,
        },
        {
          prop: "productKey",
          label: "ProductKey",
          width: 180,
        },
        {
          prop: "nodeTypeName",
          label: "节点类型",
        },
      ],
      notSource: [],
      notLoading: false,
      notPagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      notSelectList: [],
      alreadySearchVal: "",
      alreadySource: [],
      alreadyLoading: false,
      alreadyPagination: {
        current: 1,
        size: 10,
        total: 0,
      },
      alreadySelectList: [],

      deviceId: "", //设备id
      isEmpty: false, //左侧table empty 特殊处理
      waitCount: 0,
      doneCount: 0,
      width: `${(1330 / 1920) * 100}vw`, // postcss 计算方法
    };
  },
  components: { iotDialog, iotTable, iotPagination },
  props: {
    hostProductKey: {
      type: String,
    },
    hostDeviceName: {
      type: String,
    },
    uuID: {
      type: String,
    },
    ruleID: {
      type: String,
    },
    rowProductKey: {
      type: String,
      default: "",
    },
    type: {
      type: Number,
    },
    isclear: {
      type: Boolean,
    },
  },
  methods: {
    open() {
      this.isclearson = this.isclear;
      this.visible = true;
      this.getProductList();
      this.getProductKey(0);
      this.getProductKey(1);
      if (this.type == 2) {
        //点击编辑时直接调用事件接口
        this.getProductEvent();
      }
    },
    //获取产品下拉框数据
    getProductList() {
      gethasEventDeviceSubProduct().then((res) => {
        this.options = res.data;
        if (this.rowProductKey) {
          //点击编辑时获取到与当前行相同的productKey作比较，一样的则将product等于它
          const selectedOption = this.options.find(
            (item) => item.productKey === this.rowProductKey || ""
          );
          if (selectedOption) {
            this.product = selectedOption.name;
          }
        } else if (this.type == 1) {
          //当新建成功时需要再次新建时需要将product清空
          this.product = "";
        } else return;
      });
    },
    //选择产品
    selectChange(data) {
      this.isclearson = false;
      this.notPagination.current = 1;
      this.alreadyPagination.current = 1;
      this.notSelectList = [];
      this.alreadySelectList = [];
      let object = this.options.filter((item) => item.id == data)[0];
      this.objs = object.productKey;
      this.rowProductKey = ""; //当点击编辑进入后再次选择产品时将产品key重新为空
      this.getProductEvent(object);
      // 未绑定的
      this.productInfo = object;
      this.getProductKey(0, true, true);
      // 已绑定的
      this.getProductKey(1, true, true);
    },
    // 获取产品事件接口
    getProductEvent(obj) {
      getEventList({
        productKey: this.rowProductKey ? this.rowProductKey : obj.productKey,
      }).then((res) => {
        if (res.code == 200) {
          this.eventList = res.data;
          if (this.eventList.length == []) {
            this.$newNotify.error({
              message: "该产品无主动事件上报，无法创建告警规则，请切换其他产品",
            });
          }
        }
      });
    },

    // 搜索
    fn_handle__query(flag) {
      if (!this.product) {
        this.$newNotify.warning({
          message: "请先选择产品",
        });
        return;
      }

      if (flag) {
        // 搜索待关联
        this.notPagination.current = 1;
        this.getProductKey(0);
      } else {
        // 搜索已关联
        this.alreadyPagination.current = 1;
        this.getProductKey(1);
      }
    },
    //获取设备列表
    getProductKey(flag) {
      if (flag == 0) {
        let params = {
          deviceName: this.notSearchVal,
          productKey: this.productInfo.productKey,
          ...this.notPagination,
        };

        if (this.rowProductKey) {
          //编辑时，直接获取到选中的数据
          params.productKey = this.rowProductKey;
        } else if (this.isclearson) {
          //当用户新建规则选择好设备时却未提交而是点击取消时，将params.productkey清空，避免用户再点击新建时出现上次的数据
          params.productKey = "";
        }
        getrelatonWaitList(params).then((res) => {
          if (200 == res.code) {
            let data = res.data;
            this.notSource = data.records || [];
            this.waitCount = data.total;
            this.notPagination.total = data.total || 0;
            this.handleData();
          }
        });
      } else {
        let params = {
          ruleId: this.ruleID,
          deviceName: this.alreadySearchVal,
          productKey: this.productInfo.productKey,
          ...this.alreadyPagination,
          requestUuid: this.uuID, //已选择的设备需要传递requestUuid参数
        };
        if (this.rowProductKey) {
          //编辑时，直接获取到选中的数据
          params.productKey = this.rowProductKey;
        }
        getrelatonBindList(params).then((res) => {
          if (200 == res.code) {
            let data = res.data;
            this.alreadySource = data.records;
            this.doneCount = data.total;
            this.alreadyPagination.total = data.total || 0;
            this.handleData();
          }
        });
      }
    },

    handleClear() {},
    selectionChange(data, flag) {
      if (flag) {
        // 未选择数组
        this.notSelectList = {
          alarmRuleDeviceBindList: data.map((item) => {
            const newItem = {
              requestUuid: this.uuID,
              deviceId: item.deviceId,
              deviceName: item.deviceName,
              productKey: item.productKey,
            };
            if (this.type == 2) {
              newItem.ruleId = this.ruleID;
            }
            return newItem;
          }),
        };
      } else {
        // 已选择数组
        this.alreadySelectList = {
          alarmRuleDeviceBindList: data.map((item) => {
            return {
              id: item.id,
              requestUuid: item.uuID,
              deviceId: item.deviceId,
              deviceName: item.deviceName,
              productKey: item.productKey,
              ruleId: item.ruleId,
            };
          }),
        };
      }
    },
    handleCurrentChange(data, flag) {
      if (flag) {
        this.notPagination.current = data;
        this.getProductKey(0);
      } else {
        // 已绑定
        this.alreadyPagination.current = data;
        this.getProductKey(1);
      }
    },
    //绑定、解绑
    submitBind(flag) {
      if (!this.product) {
        this.$newNotify.warning({
          message: "请先选择产品",
        });
        return;
      }
      if (flag) {
        //绑定
        if (
          this.notSelectList.length == 0 ||
          this.notSource.length == 0 ||
          this.notSelectList.alarmRuleDeviceBindList.length == 0
        ) {
          this.$newNotify.warning({
            message: "请选择未选择设备",
          });
          return;
        }
        let data = this.notSelectList;
        bindDevice(data).then((res) => {
          if (res.code == 200) {
            this.notSelectList = [];
            this.notSelectList.length = 0;
            this.alreadySelectList = [];
            this.$newNotify.success({
              message: res.message,
            });
            this.notPagination.current = 1;
            this.alreadyPagination.current = 1;
            // 未绑定的
            this.getProductKey(0, false, true);
            // 已绑定的
            this.getProductKey(1, false, true);
          }
        });
      } else {
        // 解绑
        if (
          this.alreadySelectList.length == 0 ||
          this.alreadySource.length == 0 ||
          this.alreadySelectList.alarmRuleDeviceBindList.length == 0
        ) {
          this.$newNotify.warning({
            message: "请选择已选择设备",
          });
          return;
        }
        let data = this.alreadySelectList;
        removeDevice(data).then((res) => {
          if (res.code == 200) {
            this.notSelectList = [];
            this.alreadySelectList = [];
            this.$newNotify.success({
              message: res.message,
            });
            this.notPagination.current = 1;
            this.alreadyPagination.current = 1;
            // 未绑定的
            this.getProductKey(0, false, true);
            // 已绑定的
            this.getProductKey(1, false, true);
          }
        });
      }
    },
    // 处理选中的设备数据传递给父组件进行渲染
    handleData() {
      if (this.doneCount > 10) {
        this.deviceNameWhole = {
          deviceNames: this.alreadySource,
          deviceCount: this.doneCount,
        };
      } else {
        this.deviceNameWhole = {
          deviceNames: this.alreadySource,
          deviceCount: this.alreadySource.length,
        };
      }
    },
    routeDevice() {
      this.$router.replace({
        path: "/device",
      });
    },
    handleClose() {
      this.notSearchVal = "";
      this.alreadySearchVal = "";
      this.notSource = [];
      this.alreadySource = [];
      this.notPagination.current = 1;
      this.alreadyPagination.current = 1;
      this.notPagination.total = 0;
      this.alreadyPagination.total = 0;
      (this.waitCount = 0),
        (this.doneCount = 0),
        this.$emit("deviceNameWhole", this.deviceNameWhole);
      this.$emit("geteventList", {
        eventList: this.eventList,
        productKey: this.rowProductKey ? this.rowProductKey : this.objs,
      });
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding-bottom: 12px;
  position: relative;
  h5 {
    color: #666666;
    font-size: 14px;
    line-height: 16px;
    font-weight: normal;
    padding: 8px 0;
    span {
      color: #ff0000;
    }
  }
  .item {
    padding-bottom: 26px;
  }

  .device {
    align-items: center;

    .device-data {
      width: 586px;
      h4 {
        padding-left: 14px;
        position: relative;
        color: #262626;
        font-size: 18px;
        font-weight: 500;
      }
      h4::before {
        content: "";
        width: 4px;
        height: 14px;
        background: #1890ff;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .table {
        margin-top: 18px;
        // height: 570px;
        background: #ffffff;
        border: 1px solid #ececec;
        padding: 18px 18px 5px 18px;
        .form-item {
          padding-bottom: 18px;
        }
      }
      .pagination {
        // margin-top: 80px;
        padding-top: 90px;
        justify-content: flex-end;
      }
    }
    .action {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
      p {
        width: 72px;
        height: 32px;
        text-align: center;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.3s;
        span {
          color: #515151;
          font-size: 14px;
          line-height: 32px;
        }
        img {
          width: 12px;
          height: 9px;
        }
      }
      .bind {
        background: linear-gradient(
          270deg,
          #eeeeee 0%,
          #dadddf 75%,
          #c9c9c9 100%
        );
        background-size: 200%;
        background-position: 100% 0;
        margin-bottom: 14px;
        span {
          padding-right: 8px;
        }
      }
      .bind:hover {
        // background: linear-gradient(270deg, #e7e7e7 0%, #c9c9c9 100%);
        background-position: 0;
      }
      .unbound {
        background: linear-gradient(
          90deg,
          #eeeeee 0%,
          #dadddf 75%,
          #c9c9c9 100%
        );
        background-size: 200%;

        span {
          padding-left: 8px;
        }
        img {
          transform: rotate(180deg);
        }
      }
      .unbound:hover {
        // background: linear-gradient(90deg, #e7e7e7 0%, #c9c9c9 100%);
        background-position: 100% 0;
      }
    }
  }
  .mask {
    position: absolute;
    width: 100%;
    height: calc(100% - 92px);
    position: absolute;
    top: 92px;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    align-items: center;
    justify-content: center;
    z-index: 99;
    span {
      color: #ffffff;
      text-align: center;
      font-family: H_Medium;
      font-size: 18px;
    }
  }
  .item {
    /deep/ {
      .el-input__inner {
        width: 586px;
        border-radius: 0;
      }
    }
  }

  .device {
    /deep/ .el-select {
      .el-input__inner::placeholder {
        color: #515151;
      }
    }
    /deep/ {
      .el-table__header {
        tr {
          height: 42px !important;
        }
        .el-table__cell {
          padding: 0;
        }
      }
      .el-table__row {
        height: 42px !important;
        .el-table__cell {
          padding: 0;
        }
      }
    }
  }
}

/deep/ .empty {
  padding-top: 68px;
  color: #888888;
  font-size: 14px;
  span {
    color: #018aff;
    cursor: pointer;
  }
}
.device-table-content {
  .iot-table {
    height: 378px;
  }
}
</style>
