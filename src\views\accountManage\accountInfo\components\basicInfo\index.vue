<template>
  <div class="basic-info">
    <div class="info-top"></div>
    <div class="info-content">
      <div class="row-items flex">
        <div class="item">
          <span>登录账号</span>
          <span>{{ userInfo.account }}</span>
        </div>
        <div class="item">
          <span>账号ID</span>
          <span>{{ userInfo.id }}</span>
        </div>
      </div>
      <div class="row-items flex">
        <div class="item">
          <span>手机号码</span>
          <span>{{ userInfo.phone }}</span>
        </div>
        <div class="item">
          <span>注册时间</span>
          <span>{{ userInfo.createTime }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { getUserInfo } from "@/api/user";

export default {
  name: "basicInfo",
  data() {
    return {
      userInfo: {
        id: "",
        createTime: "",
        account: "",
        phone: "",
      },
    };
  },
  mounted() {
    this.getUserInfo();
  },
  methods: {
    getUserInfo() {
      getUserInfo().then((res) => {
        if (res.code == 200) {
          this.userInfo = res.data;
        }
      });
    },
  },
};
</script>
<style lang="scss" scoped>
.basic-info {
  .info-top {
    margin-top: 18px;
  }
  .info-content {
    margin-top: 18px;
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 10px 32px;
    .row-items {
      .item {
        flex: 1;
        width: 50%;
        padding: 10px 0;
        font-size: 14px;
        font-weight: 400;
        span {
          height: 22px;
          line-height: 22px;
          &:first-child {
            color: #999999;
            width: 68px;
            text-align: right;
            display: inline-block;
          }
          &:last-child {
            color: #515151;
            margin-left: 48px;
          }
        }
      }
    }
  }
}
</style>
