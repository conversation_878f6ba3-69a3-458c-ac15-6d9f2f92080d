<template>
  <div class="cloudSub">
    <div class="form-top flex">
      <!--  -->
      <!-- 搜索栏 -->
      <form-search
        :options="deviceOptions"
        @search="fn_search_table_data"
        @clear="fn_clear_search_info"
        :inputHolder="inputHolder"
        :selectHolder="selectHolder"
      ></form-search>
    </div>
    <div class="cloudSub-table">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="status" slot-scope="scope">
          <el-switch
            v-model="scope.row.effectiveState"
            active-color="#13CE66"
            inactive-color="#D9DAE0"
            @change="(flag) => handleSwitchChange(flag, scope.row)"
          >
          </el-switch>
        </template>
        <template slot="event" slot-scope="scope">
          <p>{{ scope.row.event }}</p>
        </template>

        <template slot="action" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_edit(scope.row)" class="color2">
              选择事件
            </p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="table-bottom flex" v-if="tableData.length">
      <!-- 分页 -->
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <select-event ref="event" @ok="handleReset" />
  </div>
</template>

<script>
import FormSearch from "@/components/form-search";
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import selectEvent from "../event";
import { mqttSubscriberList, mqttEventState } from "@/api/cloud";
export default {
  name: "mqtt",
  data() {
    return {
      loading: false,
      queryForm: {},
      columns: [
        // { label: "Public Key", prop: "publicKey", width: "350px" },
        {
          label: "产品名称",
          prop: "productName",
        },
        { label: "产品Key", prop: "productKey" },
        {
          label: "生效状态",
          prop: "effectiveState",
          slotName: "status",
        },
        { label: "订阅事件", prop: "event", slotName: "event" },
        { label: "订阅Topic", prop: "topic" },
        { label: "操作", slotName: "action", width: "100px" },
      ],
      tableData: [],
      deviceOptions: [
        {
          id: "2",
          name: "全部状态",
        },
        {
          id: "1",
          name: "启用",
        },
        {
          id: "0",
          name: "停止",
        },
      ],
      inputHolder: "请输入产品名称",
      selectHolder: "请选择状态",
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
    };
  },
  computed: {},
  components: { IotTable, FormSearch, IotPagination, selectEvent },
  mounted() {
    this.getData();
  },
  methods: {
    getData() {
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        productName: this.queryForm.value || "",
        descs: true,
      };
      if (this.queryForm.id != 2) {
        params["effectiveState"] = this.queryForm.id;
      }
      this.loading = true;
      mqttSubscriberList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records.map((item) => {
              item.event = Object.values(item.eventMap).join(",");
              item.effectiveState = item.effectiveState ? true : false;
              return item;
            });
            this.pagination.total = res.data.total;
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    fn_edit(row) {
      this.$refs.event.open(row);
    },
    fn_search_table_data(data) {
      console.log(data);
      this.queryForm = data;
      this.getData();
    },
    fn_clear_search_info() {
      this.queryForm.id = "";
      this.queryForm.value = "";
      this.getData();
    },
    handleSizeChange(val) {
      this.pagination.current = 1;
      this.pagination.size = val;
      this.getData();
    },
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.getData();
    },
    handleSwitchChange(flag, data) {
      mqttEventState({
        id: data.id,
        effectiveState: flag ? 1 : 0,
      }).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
        } else {
          this.$newNotify.warning({
            message: res.message,
          });
        }
        // this.handleReset();
      });
    },
    handleReset() {
      this.pagination.current = 1;
      this.getData();
    },
  },
};
</script>

<style lang="scss" scoped>
.cloudSub {
  padding-top: 18px;
  padding-bottom: 32px;

  .cloudSub-table {
    padding-top: 18px;
  }
  .empty {
    text-align: center;
    line-height: 50px;
  }
  .table-edit {
    display: flex;
    align-items: center;
    p {
      cursor: pointer;
    }
    p:nth-child(2) {
      margin: 0px 12px;
      width: 1px;
      height: 13px;
      border: 1px solid #ededed;
    }
  }
  .form-top {
    justify-content: flex-end;
  }
  .table-bottom {
    justify-content: flex-end;
    padding: 14px 0;
  }
}

.gap {
  height: 14px;
}
</style>
