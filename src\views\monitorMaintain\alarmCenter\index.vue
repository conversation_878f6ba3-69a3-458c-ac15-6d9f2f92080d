<template>
  <div class="alarm-center">
    <div class="product_bg">
      <h3 class="bg_title">告警中心</h3>
      <span class="bg_desc"
        >平台提供监控告警能力，用户可以根据业务需求、业务指标设置告警规则，当监控指标满足条件触发后，支持告警列表、邮件、站内信息等方式通知用户，帮助用户快速处理告警事件</span
      >
    </div>
    <div class="alarm-top">
      <div class="alarm-tab">
        <el-tabs v-model="tabIndex" type="border-card">
          <el-tab-pane label="告警统计" name="0">
            <alarm-statistics  @changeIndex="changeIndex"></alarm-statistics>
          </el-tab-pane>
          <el-tab-pane label="告警列表" name="1">
            <alarm-list></alarm-list>
          </el-tab-pane>
          <el-tab-pane label="告警规则" name="2">
            <alarm-config></alarm-config>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>
<script>
import alarmList from "./components/alarmList";
import alarmConfig from "./components/alarmConfig";
import alarmStatistics from "./components/alarmStatistics";
export default {
  name: "alarmCenter",
  components: {
    alarmList,
    alarmConfig,
    alarmStatistics,
  },
  data() {
    return {
      tabIndex: "0",
    };
  },
  beforeRouteEnter(to, from, next) {
    next((vm) => {
      if (from.name == "alarmeventHanding") {
        vm.tabIndex = "1";
      } else {
        vm.tabIndex = "0";
      }
    });
  },
  created() {
    console.log(this.$route);
  },
  methods: {
    changeIndex(index){
      this.tabIndex = index
    }
  },
};
</script>
<style lang="scss" scoped>
.alarm-center {
  .product_bg {
    width: 100%;
    background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px 0px;
    background-size: 100%;
    padding: 35px 0px 50px 32px;
    .bg_title {
      font-weight: 500;
      font-size: 22px;
      line-height: 30px;
      color: #333333;
    }
    .bg_desc {
      font-weight: 400;
      font-size: 16px;
      line-height: 30px;
      color: #77797c;
    }
  }

  .alarm-top {
    padding: 0px 22px 0px 22px;
    margin-top: 32px;
    justify-content: space-between;
    .alarm-tab {
      .el-tabs--border-card {
        box-shadow: none;
        border: none;
      }
      /deep/ {
        .el-tabs__item {
          height: 48px;
          line-height: 48px;
          color: rgba(51, 51, 51, 1);
        }
        .el-tabs--border-card > .el-tabs__header {
          background-color: #edf1f7;
          border-bottom: none;
          margin: 0px 10px 0px 10px;
        }
        .el-tabs--border-card > .el-tabs__content {
          padding: 0px;
        }
        .el-tabs__item {
          padding: 0px 35px;
          height: 42px;
          line-height: 42px;
        }
        .el-tabs--border-card > .el-tabs__header .el-tabs__item {
          color: rgba(51, 51, 51, 1);
          font-family: HarmonyOS Sans SC;
          font-style: normal;
          font-weight: normal;
          font-size: 16px;
          letter-spacing: 1px;
        }
        .el-tabs--top.el-tabs--border-card
          > .el-tabs__header
          .el-tabs__item:nth-child(2) {
          padding-left: 35px;
        }
        .el-tabs--top.el-tabs--border-card
          > .el-tabs__header
          .el-tabs__item:last-child {
          padding-right: 35px;
        }
        .el-tabs--border-card
          > .el-tabs__header
          .el-tabs__item.is-active::after {
          content: "";
          position: absolute;
          left: 0;
          top: 0;
          width: 100%;
          height: 3px;
          background-color: #515151;
          z-index: 1;
        }
        /deep/ .is-active {
          color: rgba(51, 51, 51, 1);
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .alarm-center {
    .product_bg {
      width: 100%;
      background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px  0px;
      background-size: 100% 100%;
      height: 100px;
      padding:10px 32px 50px 32px;
      .bg_title {
        font-weight: 500;
        font-size: 16px;
        line-height: 30px;
        color: #333333;
      }
      .bg_desc {
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        color: #77797c;
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1060px) {
  .alarm-center {
    .product_bg {
      width: 100%;
      background: url("~@/assets/images/product/product_bg.jpg") no-repeat 0px  0px;
      background-size: 100% 100%;
      height: 100px;
      padding:10px 32px 0px 32px;
      .bg_title {
        font-weight: 500;
        font-size: 16px;
        line-height: 30px;
        color: #333333;
      }
      .bg_desc {
        font-weight: 400;
        font-size: 13px;
        line-height: 20px;
        color: #77797c;
      }
    }
  }
}
</style>
