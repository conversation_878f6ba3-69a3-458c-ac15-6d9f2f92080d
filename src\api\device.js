/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-18 10:35:17
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2023-04-24 15:21:35
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;

const baseUrl = `${baseServer}/tenant`;

const baseUrl_sub = `${baseServer}`;

const baseUrl_action = `${baseServer}/tenant`;

/**
 * @desc 设备列表
 * @params
 * @returns
 */
export const getDeviceList = (params) => {
    return request({
        url: `${baseUrl}/device/list`,
        method: "get",
        params,
    });
};

/**
 * @desc 新增设备
 * @params
 * @returns
 */
export const getDeviceSave = (data) => {
    return request({
        url: `${baseUrl}/device/save`,
        method: "post",
        data,
    });
};

/**
 * @desc 设备状态数
 * @params
 * @returns
 */
export const getDeviceStatusNum = (params) => {
    return request({
        url: `${baseUrl}/device/statusNum`,
        method: "get",
        params,
    });
};

/**
 * @desc 更新设备信息
 * @params
 * @returns
 */
export const getDeviceUpdate = (data) => {
    return request({
        url: `${baseUrl}/device/update`,
        method: "put",
        data,
    });
};

/**
 * @desc 设备详细信息
 * @params
 * @returns
 */
export const getDeviceInfo = (params) => {
    return request({
        url: `${baseUrl}/device/info`,
        method: "get",
        params,
    });
};


/**
 * @desc 设备标签信息
 * @params
 * @returns
 */
export const getDeviceTags = (params) => {
    return request({
        url: `${baseUrl}/device/tags/list`,
        method: "get",
        params,
    });
};


/**
 * @desc 设备标签检查
 * @params
 * @returns
 */
export const getDeviceTagsCheck = (params) => {
    return request({
        url: `${baseUrl}/device/tags/check`,
        method: "get",
        params,
    });
};


/**
 * @desc 设备标签保存
 * @params
 * @returns
 */
export const getDeviceTagsSave = (data) => {
    return request({
        url: `${baseUrl}/device/tags/save`,
        method: "post",
        data,
    });
};



/**
 * @desc 删除设备
 * @params
 * @returns
 */
export const getDeviceDelete = (params) => {
    return request({
        url: `${baseUrl}/device/remove`,
        method: "delete",
        params,
    });
};

/**
 * @desc 查询子设备列表
 * @params
 * @returns
 */
export const getDeviceSubList = (params) => {
    return request({
        url: `${baseUrl_sub}/device/sub/list`,
        method: "get",
        params,
    });
};

/**
 * @desc 关联子设备
 * @params
 * @returns
 */
export const getDeviceSubAdd = (data) => {
    return request({
        url: `${baseUrl_sub}/device/sub/add`,
        method: "post",
        data,
    });
};

/**
 * @desc 解除关联子设备
 * @params
 * @returns
 */
export const getDeviceSubRemove = (data) => {
    return request({
        url: `${baseUrl_sub}/device/sub/remove`,
        method: "post",
        data,
    });
};

/**
 * @desc 查询产品下未关联或已关联网关的设备
 * @params
 * @returns
 */
export const getDeviceSubDeviceByProductKey = (params) => {
    return request({
        url: `${baseUrl_sub}/device/sub/deviceByProductKey`,
        method: "get",
        params,
    });
};

/**
 * @desc 查询节点类型为网关子设备的产品
 * @params
 * @returns
 */
export const gethasEventDeviceSubProduct = (params) => {
    return request({
        url: `${baseUrl_sub}/device/hasEvent/sub/product`,
        method: "get",
        params,
    });
};

/**
 * @desc 查询有事件的子设备的产品
 * @params
 * @returns
 */
export const getDeviceSubProduct = (params) => {
    return request({
        url: `${baseUrl_sub}/device/sub/product`,
        method: "get",
        params,
    });
};

/**
 * @desc 查询事件调用
 * @params
 * @returns
 */
export const getEventsData = (data) => {
    return request({
        url: `${baseUrl}/data/events`,
        method: "post",
        data,
    });
};
/**
 * @desc 查询服务调用
 * @params
 * @returns
 */
export const getServicesData = (data) => {
    return request({
        url: `${baseUrl_action}/call/services`,
        method: "post",
        data,
    });
};

/**
 * @desc 设备运行状态
 * @params
 * @returns
 */
export const getDeviceRealData = (params) => {
    return request({
        url: `${baseUrl}/device/realData`,
        method: "get",
        params,
    });
};
/**
 * @desc 查询服务调用
 * @params
 * @returns
 */
export const getPropertiesData = (data) => {
    return request({
        url: `${baseUrl}/data/properties`,
        method: "post",
        data,
    });
};
