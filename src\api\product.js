/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-10 11:08:06
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:13:46
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";

const baseUrl = `${BASE_SERVER}/tenant`;

/**
 * @desc 产品列表
 * @params
 * @returns
 */
export const getProductList = (params) => {
  return request({
    url: `${baseUrl}/product/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 产品列表(模糊查询)
 * @params
 * @returns
 */
export const getProductLike = (params) => {
  return request({
    url: `${baseUrl}/product/like`,
    method: "get",
    params,
  });
};

/**
 * @desc 新增产品
 * @params
 * @returns
 */
export const getProductSave = (data) => {
  return request({
    url: `${baseUrl}/product/save`,
    method: "post",
    data,
  });
};

/**
 * @desc 修改产品
 * @params
 * @returns
 */
export const getProductUpdate = (data) => {
  return request({
    url: `${baseUrl}/product/update`,
    method: "post",
    data,
  });
};

/**
 * @desc 产品详情
 * @params
 * @returns
 */
export const getProductDetail = (params) => {
  return request({
    url: `${baseUrl}/product/detail`,
    method: "get",
    params,
  });
};

/**
 * @desc 产品删除
 * @params
 * @returns
 */
export const getProductRemove = (params) => {
  return request({
    url: `${baseUrl}/product/remove`,
    method: "delete",
    params,
  });
};

/**
 * @desc 产品配置
 * @params
 * @returns
 */
export const setProductConfig = (data) => {
  return request({
    url: `${baseUrl}/product/config?productKey=${data.productKey}`,
    method: "post",
    data,
  });
};

/**
 * @desc 功能定义  新增属性
 * @params params
 * @returns
 */

export const productAbilityAdd = (data, params) => {
  return request({
    url: `${baseUrl}/product/thingModel/add`,
    method: "post",
    data,
    params,
  });
};
/**
 * @desc 功能定义  新增属性
 * @params params
 * @returns
 */

export const productAbilityEdit = (data, params) => {
  return request({
    url: `${baseUrl}/product/thingModel/update`,
    method: "post",
    data,
    params,
  });
};
/**
 * @desc 功能定义  新增属性
 * @params params
 * @returns
 */

export const productAbilityDetail = (params) => {
  return request({
    url: `${baseUrl}/product/thingModel/detail`,
    method: "get",
    params,
  });
};

/**
 * @desc 所有项目名称查询
 * @params params
 * @returns
 */
export const getUniqueCode = (params) => {
  return request({
    url: `${baseUrl}/product/thingModel/getUniqueCode`,
    method: "get",
    params,
  });
};

/**
 * @desc 导出物模型
 * @params params
 * @returns
 */
export const exportObjectModel = (params) => {
  return request({
    url: `${baseUrl}/product/thingModel/export`,
    method: "get",
    params,
  });
};

/**
 * @desc 导入物模型
 * @params params
 * @returns
 */
export const importObjectModel = (params) => {
  return request({
    url: `${baseUrl}/product/thingModel/import`,
    method: "post",
    params,
  });
};
export const importPartUrl = `${baseUrl}/product/thingModel/import`;

/**
 * @desc 产品列表下拉信息
 * @params params
 * @returns
 */
export const getProductSelect = (params) => {
  return request({
    url: `${baseUrl}/product/dropDown`,
    method: "get",
    params,
  });
};
