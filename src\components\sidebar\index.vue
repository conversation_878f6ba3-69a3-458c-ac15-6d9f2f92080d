<template>
  <div class="sidebar">
    <!-- <p @click="miniChange">切换</p> -->
    <sidebar-item
      v-for="(item, index) in sidebarList"
      :menu="item"
      :index="index"
      :key="item.name"
      :isCollapse="isCollapse"
      @open="openOhange"
      @active="activeChange"
    />
  </div>
</template>

<script>
import sidebarItem from "../sidebar-item";
export default {
  data() {
    return {
      sidebarList: [],
      isCollapse: false,
    };
  },
  components: { sidebarItem },
  watch: {
    $route() {
      this.loop(this.sidebarList, (menu) => {
        const name = this.$route.meta.crumb[1].url.split("/")[1];
        const view = this.$route.meta.crumb[0].url;
        if (menu.path === view) {
          menu.isOpen = false;
        } else {
          menu.isOpen = true;
        }
        if (menu.name === name) {
          menu.isActive = true;
        } else {
          menu.isActive = false;
          if (Array.isArray(menu.children) && menu.children.length > 0) {
            return true;
          } else {
            return false;
          }
        }
      });
    },
  },
  mounted() {
    let routes = this.$router.options.routes;
    let list = routes.filter((item) => item.name == "home")[0].children;
    this.sidebarList = this.reset(list);
  },
  methods: {
    miniChange() {
      this.isCollapse = !this.isCollapse;
    },
    openOhange(data) {
      //接收子项展开传递的下标数组
      let object = null;
      data.forEach((item) => {
        if (object) {
          object = object.children[item];
          this.sidebarList[item].isOpen = !this.sidebarList[item].isOpen;
        } else {
          object = this.sidebarList[item];
          this.sidebarList[item].isOpen = !this.sidebarList[item].isOpen;
        }
      });
    },
    activeChange(data) {
      this.loop(this.sidebarList, (menu) => {
        if (menu.name === data) {
          menu.isActive = true;
        } else {
          menu.isActive = false;
          if (Array.isArray(menu.children) && menu.children.length > 0) {
            return true;
          } else {
            return false;
          }
        }
      });
    },
    loop(list, callback) {
      if (Array.isArray(list) && list.length > 0) {
        list.forEach((item) => {
          if (callback) {
            let flag = callback(item);
            if (flag) this.loop(item.children, callback);
          }
        });
      }
    },
    reset(list) {
      if (Array.isArray(list) && list.length > 0) {
        return list.map((item) => {
          // item.isOpen = false; //是否展开
          if (item.path === this.$route.meta.crumb[1].url) {
            item.isActive = true; //是否选中
            item.isOpen = false; //是否展开
          } else {
            item.isActive = false; //是否选中
            item.isOpen = true; //是否展开
          }
          if (item.path === this.$route.meta.crumb[0].url) {
            item.isOpen = false; //是否展开
          } else {
            item.isOpen = true; //是否展开
          }

          if (item.children && Array.isArray(list) && list.length > 0) {
            item.children = this.reset(item.children);
          }
          return item;
        });
      } else return [];
    },
  },
};
</script>

<style lang="scss" scoped>
.sidebar {
  width: 280px;
  height: 100%;
  padding: 10px 0;
  background: #EEEFF1;
  flex-shrink: 0;
  // margin-top: 8px;
  // margin-top: 10px;
}
</style>
