<template>
	<iot-dialog
		:visible.sync="visible"
		:title="title"
		:width="dialogWidth"
		@callbackSure="fn_sure"
	>
		<template #body>
			<iot-form>
				<el-form
					class="link-form"
					ref="linkForm"
					:rules="rules"
					@validate="fn_validate"
					:model="linkForm"
					label-width="80px"
					:label-position="'top'"
				>
					<el-form-item label="规则名称" prop="name">
						<el-input v-model="linkForm.name"></el-input>
					</el-form-item>
					<div class="el-form-tips" v-if="nameTrue">
						支持中文、英文、数字、下划线（_）和短划线（-），
						长度限制为1~32个字符，中文算两个字符
					</div>
					<el-form-item label="规则描述" prop="description">
						<el-input
							:maxlength="200"
							type="textarea"
							v-model="linkForm.description"
						></el-input>
					</el-form-item>
					<div class="el-form-tips" v-if="descTrue">
						最多不超过200个字符
					</div>
				</el-form>
			</iot-form>
		</template>
	</iot-dialog>
</template>

<script>
import IotDialog from '@/components/iot-dialog'
import IotForm from '@/components/iot-form'
import { getRuleSceneUpdate } from '@/api/sceneLink'
import { reg_fifteen, reg_seven } from '@/util/util'
export default {
	data() {
		return {
			visible: false,
			title: '编辑场景',
			dialogWidth: '580px',
			linkForm: {
				name: '',
				description: '',
			},
			rules: {
				name: [
					{
						required: true,
						trigger: 'blur',
						message:
							'支持中文、英文、数字、下划线（_）和短划线（-），长度限制为1~32个字符，中文算两个字符',
					},
					{ validator: this.nameValidate, trigger: 'blur' },
				],
				description: [
					{
						required: false,
						// message: '最多不超过200个字符',
						trigger: 'blur',
						validator: this.checkLength,
					},
				],
			},
			nameTrue: true,
			descTrue: true,
		}
	},
	props: {
		ruleID: {
			type: [String, Number],
		},
	},
	components: { IotDialog, IotForm },
	methods: {
		open(info) {
			this.linkForm.name = info.name || ''
			this.linkForm.description = info.description || ''
			this.visible = true
		},
		// 表单验证触发
		fn_validate(name, value) {
			if (name === 'description') {
				this.descTrue = value
			}
			if (name === 'name') {
				this.nameTrue = value
			}
		},
		nameValidate(rule, value, callback) {
			if (value !== '') {
				if (!reg_fifteen(value)) {
					callback(
						'支持中文、英文、数字、下划线（_）和短划线（-），长度限制为1~32个字符，中文算两个字符'
					)
				} else {
					callback()
				}
				callback()
			} else {
				callback('须选择所属项目')
			}
		},
		checkLength(rule, value, callback) {
			if (!reg_seven(value, 201)) {
				return callback(new Error('最多不超过200个字符'))
			} else {
				callback()
			}
		},
		fn_sure() {
			this.$refs.linkForm.validate((valid) => {
				if (valid) {
					let params = { ...this.linkForm, id: this.ruleID }

					getRuleSceneUpdate(params)
						.then((res) => {
							if (res.code == 200) {
								this.$newNotify.success({
									message: res.message,
								})
								this.$emit('updateInfo', this.linkForm)
								// 更新 详情名字
								let data = {
									title: this.linkForm.name,
								}
								this.$store.dispatch('setLayoutInfo', data)
								this.visible = false
							} else {
								this.$newNotify.error({
									message: res.message,
								})
							}
						})
						.finally(() => {
							this.visible = false
						})
				}
			})
		},
	},
}
</script>

<style lang="scss" scoped>
.link-form {
	:deep(.el-form-item) {
		margin-bottom: 17px;
	}
	.el-form-tips {
		margin-top: -17px;
	}
	.upload-text {
		width: 160px;
		background: #ebf6ff;
		color: #0088fe;
		font-size: 14px;
		font-weight: 400;
		padding: 11px 0;
		user-select: none;
	}
	.el-upload__tip {
		color: #999999;
		font-size: 12px;
	}
}
</style>
