/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-19 11:33:04
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:34:06
 */

import {
  MUTATIONS_DEVICE__WEBSOCKET,
  MUTATIONS_DEVICE__STATUSDATA,
} from "../mutations_type";
import { getDeviceRealData } from "@/api/device";
import Socket from "@/socket/";
import { BASE_SERVER } from "../../conf/env";
import decode from "@/util/aes.js";
const baseUrl = "/api";
// const url__ws = `${baseUrl}/central-iot-stomp/ws`;
const url__ws = `${baseUrl}${BASE_SERVER}/ws`;
export default {
  state: () => {
    return {
      xtSocket: null,
      statusData: {},
    };
  },
  mutations: {
    [MUTATIONS_DEVICE__WEBSOCKET](state, val = null) {
      state.xtSocket = val;
    },
    [MUTATIONS_DEVICE__STATUSDATA](state, val = null) {
      state.statusData = val;
    },
  },
  actions: {
    // 建立连接
    async fn_init_socket({ commit, state, rootState }) {
      if (state.xtSocket) {
        return;
      }
      let userInfo = rootState.user.userInfo;
      let username = userInfo.user_name;
      let nestAuth = userInfo.access_token;
      let ws = `${url__ws}?username=${username}&Nest-Auth=${nestAuth}`;
      // 建立WS实例同时初始化IOT设备数据
      let xtSocket = new Socket({
        ws,
        headers: {
          username,
          "Nest-Auth": nestAuth,
        },
      });
      commit(MUTATIONS_DEVICE__WEBSOCKET, xtSocket);
      // 连接WS
      await xtSocket.asyncConnect();
    },
    // 事件订阅
    fn_runstatus__subscribe({ commit, state, rootState }) {
      let userInfo = rootState.user.userInfo;
      let aesTenantId = userInfo.tenant_id || localStorage.getItem("tenant_id");
      let tenantId = decode.decryptoByAES(aesTenantId);
      let xtSocket = state.xtSocket;
      let ws_url__sub = `/iot/${tenantId}/property`;
      xtSocket.subscribe({
        destination: ws_url__sub,
        callback: function (data) {
          try {
            data.isSocket = true;
            commit(MUTATIONS_DEVICE__STATUSDATA, data);
          } catch (error) {
            console.log(error);
          }
        },
      });
    },
    // 运行状态数据
    http_getDeviceRealData({ commit }, data) {
      getDeviceRealData(data).then((res) => {
        if (res.code == 200) {
          const result = res.data.props;
          commit(MUTATIONS_DEVICE__STATUSDATA, result);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
  },
};
