<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-21 17:58:20
-->
<template>
  <div class="project">
    <div class="project-top">
      <div class="top-left">
        <iot-button text="创建项目" @search="fn_open" />
      </div>
      <div class="top-right">
        <!-- 搜索栏 -->
        <form-search
          defaultId="1"
          :options="selectOptions"
          @search="fn_search_table_data"
          @clear="fn_clear_search_info"
          :inputHolder="inputHolder"
        />
      </div>
    </div>

    <div class="project-table">
      <!-- 表格 -->
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="deviceAmount" slot-scope="scope">
          <p>
            {{ scope.row.deviceAmount === -1 ? 0 : scope.row.deviceAmount }}
          </p>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_edit(scope.row)" class="color2">
              编辑
            </p>
            <p></p>
            <p @click="fn_del(scope.row.id)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>

    <div class="project-bottom" v-if="tableData.length">
      <!-- 分页 -->
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      :showLoading="true"
      @callbackSure="fn_sure"
      @close="fn_close"
    >
      <template #body>
        <iot-form v-if="type == 1">
          <template #default>
            <el-form
              class="projectForm"
              ref="projectForm"
              :label-position="'top'"
              :model="projectForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <el-form-item label="项目名称" prop="name">
                <el-input type="text" v-model="projectForm.name"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持中文、英文、数字、下划线的组合，最多不超过32个字符
              </div>
              <el-form-item label="项目描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="projectForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </el-form>
          </template>
        </iot-form>

        <div v-if="type == 2">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除项目后，该项目下所有产品数据都将被删除且不能恢复，请确认
                    是否删除该项目？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
    <!-- <p @click="openDialog">项目</p>
    <add ref="add" />-->
  </div>
</template>

<script>
import FormSearch from "@/components/form-search";
import IotForm from "@/components/iot-form";
import IotButton from "@/components/iot-button";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotTable from "@/components/iot-table";

import { reg_one, reg_seven } from "@/util/util.js";
import {
  addProject,
  pageProject,
  deleteProject,
  updateProject,
  listProject,
} from "@/api/equipment";

// import add from '../components/add'
export default {
  name: "Project",
  components: {
    FormSearch,
    IotForm,
    IotButton,
    IotPagination,
    IotDialog,
    IotTable,
    // add
  },
  data() {
    return {
      selectOptions: [{ id: "1", name: "项目名称" }],
      tableData: [],
      columns: [
        { label: "项目名称", prop: "name", width: "350" },
        { label: "项目标识", prop: "code" },
        { label: "产品数量", prop: "productAmount" },
        {
          label: "设备数量",
          prop: "deviceAmount",
          slotName: "deviceAmount",
        },
        { label: "创建时间", prop: "createTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      inputHolder: "请输入项目名称",
      searchParams: {
        name: "",
      },
      projectList: [],
      delIds: "",
      dialogWidth: "580px",
      visible: false,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      projectForm: {
        name: "",
        description: "",
        id: "",
      },
      rules: {
        name: [
          {
            required: true,
            // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        description: [
          {
            required: false,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      nameTrue: true,
      descTrue: true,
      title: "",
      type: 1, //1 创建 修改 2 删除
      loading: true,
    };
  },
  created() {
    // this.fn_get_table_data();
  },
  mounted() {
    // 文案测试
    /* this.DDConfig.ddShowMenu({
      url: window.location.href,
      title: 'tenant',
      content: '测试文案测试文案测试文案测试文案测试文案测试文案测试文案'
    }) */
  },
  activated() {
    let data = {
      ...this.searchParams,
      current: this.pagination.current,
      size: this.pagination.size,
    };
    console.log(this.searchParams);
    this.fn_get_table_data(data);
  },
  methods: {
    checkName(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入项目名称"));
      } else if (!reg_one(value)) {
        return callback(
          new Error("支持中文、英文、数字、下划线的组合，最多不超过32个字符")
        );
      } else {
        callback();
      }
    },
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
    fn_notNull(val) {
      return val !== 0 && !val;
    },
    fn_get_project_list() {
      listProject().then((res) => {
        if (res.code == 200) {
          this.projectList = res.data;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 查询
    fn_get_table_data(params = {}) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      if (params.name) {
        others.name = params.name;
      }

      pageProject(others)
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 新增
    fn_get_add_project_info(data) {
      let loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
        target: document.querySelector(".iot-dialog-content"),
      });
      addProject(data)
        .then((res) => {
          if (res.code === 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.pagination.current = 1;
            this.fn_get_table_data({
              size: this.pagination.size,
              current: 1,
            });
            this.visible = false;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    // 修改
    fn_update_project_info(data) {
      let loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
        target: document.querySelector(".iot-dialog-content"),
      });
      updateProject(data)
        .then((res) => {
          if (res.code === 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.fn_get_table_data({
              size: this.pagination.size,
              current: this.pagination.current,
            });
            this.visible = false;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          loading.close();
        });
    },
    // 搜索
    fn_search_table_data(params) {
      let data = {
        name: params.value,
      };
      this.searchParams.name = params.value;
      this.fn_get_table_data(data);
    },
    fn_clear_search_info() {
      this.searchParams.name = "";
      this.fn_get_table_data();
    },
    fn_sure() {
      if (this.type === 2) {
        let data = {
          ids: this.delIds,
        };
        let loading = this.$loading({
          lock: true,
          text: "loading...",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.9)",
          target: document.querySelector(".iot-dialog-content"),
        });
        deleteProject(data)
          .then((res) => {
            if (res.code === 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.visible = false;
              this.fn_get_table_data();
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          })
          .finally(() => {
            loading.close();
          });
      } else if (this.type === 1) {
        if (this.projectForm.id) {
          this.$refs["projectForm"].validate((valid) => {
            if (valid) {
              this.fn_update_project_info(this.projectForm);
              this.fn_get_table_data();
            } else {
              return false;
            }
          });
        } else {
          this.$refs["projectForm"].validate((valid) => {
            if (valid) {
              this.fn_get_add_project_info(this.projectForm);
            } else {
              return false;
            }
          });
        }
      }
    },
    fn_close() {
      if (this.type == 1) {
        console.log("9865");
        this.nameTrue = true;
        this.$refs["projectForm"].resetFields();
      }
    },
    fn_cancle() {
      this.visible = false;
    },
    openDialog() {
      this.$refs.add.open();
    },
    // 修改
    fn_edit(row) {
      console.log(row);
      this.title = "修改项目";
      this.dialogWidth = "580px";
      this.type = 1;
      this.visible = true;
      this.projectForm = {
        name: row.name,
        description: row.description,
        id: row.id,
      };
    },
    // 删除
    fn_del(id) {
      console.log(id);
      this.delIds = id;
      this.title = "确定删除该项目？";
      this.dialogWidth = "550px";
      this.type = 2;
      this.visible = true;
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
      if (name === "description") {
        this.descTrue = value;
      }
    },
    // 创建项目弹窗
    fn_open() {
      this.title = "创建项目";
      this.type = 1;
      this.dialogWidth = "580px";
      this.visible = true;
      this.projectForm = {
        name: "",
        description: "",
      };
    },
    // 确认请求
    // http_create_project() {
    // this.$refs['projectForm'].validate(valid => {
    //   if (valid) {
    //     this.visible = false
    //   } else {
    //     return false
    //   }
    // })
    // },
    // 删除请求
    // http_del_project() {
    //   this.$refs['projectForm'].validate(valid => {
    //     if (valid) {
    //       this.visible = false
    //     } else {
    //       return false
    //     }
    //   })
    // },
    // 当前页总条数
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.pagination.size = val;
      let params = {
        size: this.pagination.size,
        current: 1,
        ...this.searchParams,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.pagination.current = val;
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        ...this.searchParams,
      };
      this.fn_get_table_data(params);
    },
    fn_number_type_format(row, column, cellValue) {
      if (cellValue === -1) {
        return 0;
      }
    },
  },
  watch: {
    visible(val) {
      if (!val && this.type == 1) {
        this.$refs["projectForm"] && this.$refs["projectForm"].resetFields();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.project {
  .project-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 18px 0px 18px 0px;
    // .top-left {
    // }
    .top-right {
      align-items: center;
    }
  }
  .project-table {
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
  .project-bottom {
    text-align: right;
    margin-top: 14px;
  }
  .del-tips {
    width: 420px;
  }
  /deep/ .el-textarea__inner {
    font-family: H_Regular !important;
  }
  .el-form-tips {
    margin-top: -22px;
  }
}
</style>
