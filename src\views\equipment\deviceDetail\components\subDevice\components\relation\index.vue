<template>
  <iot-dialog
    title="关联子设备"
    top="10vh"
    maxHeight="auto"
    :visible.sync="visible"
    :width="width"
    :appendBody="true"
    :footer="false"
    @close="handleClose"
  >
    <template #body>
      <div class="content">
        <div class="item">
          <h5>产品<span>*</span></h5>
          <el-select
            v-model="product"
            placeholder="请选择产品"
            filterable
            @change="selectChange"
          >
            <el-option
              v-for="item in options"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </div>
        <div class="device flex">
          <div class="device-data not">
            <h4>待关联设备：{{ waitCount }}</h4>
            <div class="table">
              <div class="form-item">
                <el-input
                  v-model="notSearchVal"
                  placeholder="请输入DeviceName"
                  clearable
                  @keyup.enter.native="fn_handle__query(true)"
                  @clear="fn_handle__query(true)"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="fn_handle__query(true)"
                  ></i>
                </el-input>
              </div>
              <div class="device-table-content">
                <iot-table
                  :columns="notColumns"
                  :data="notSource"
                  :loading="notLoading"
                  @selection-change="(data) => selectionChange(data, true)"
                >
                  <template #empty>
                    <div class="empty" v-if="isEmpty">
                      该产品暂无设备，请先去<span @click="routeDevice"
                        >添加设备</span
                      >
                    </div>
                  </template>
                </iot-table>
                <div class="pagination flex">
                  <iot-pagination
                    :pagination="notPagination"
                    layout="total, prev, pager, next,  jumper"
                    @current-change="(data) => handleCurrentChange(data, true)"
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="action flex">
            <p class="bind" @click="submitBind(true)">
              <span>绑定</span>
              <img src="~@/assets/images/index/arrow-icon.png" alt="" />
            </p>
            <p class="unbound" @click="submitBind(false)">
              <img src="~@/assets/images/index/arrow-icon.png" alt="" />
              <span>解绑</span>
            </p>
          </div>
          <div class="device-data already">
            <h4>已关联设备：{{ doneCount }}</h4>
            <div class="table">
              <div class="form-item">
                <el-input
                  v-model="alreadySearchVal"
                  placeholder="请输入DeviceName"
                  clearable
                  @keyup.enter.native="fn_handle__query(false)"
                  @clear="fn_handle__query(false)"
                >
                  <i
                    slot="suffix"
                    class="el-input__icon el-icon-search"
                    @click="fn_handle__query(false)"
                  ></i>
                </el-input>
              </div>
              <div class="device-table-content">
                <iot-table
                  :columns="notColumns"
                  :data="alreadySource"
                  :loading="alreadyLoading"
                  @selection-change="(data) => selectionChange(data, false)"
                ></iot-table>
                <div class="pagination flex">
                  <iot-pagination
                    :pagination="alreadyPagination"
                    layout="total, prev, pager, next,  jumper"
                    @current-change="(data) => handleCurrentChange(data, false)"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- <div class="mask flex">
          <span>请先选择产品</span>
        </div> -->
      </div>
    </template>
  </iot-dialog>
</template>

<script>
import iotDialog from "@/components/iot-dialog";
import iotTable from "@/components/iot-table";
import iotPagination from "@/components/iot-pagination";
import {
  getDeviceSubAdd,
  getDeviceSubRemove,
  getDeviceSubDeviceByProductKey,
  getDeviceSubProduct,
} from "@/api/device.js";
export default {
  data() {
    return {
      visible: false,
      product: "",
      c: {},
      options: [
        {
          value: "1",
          label: "测试",
        },
      ],
      notSearchVal: "",
      notColumns: [
        {
          type: "selection",
        },
        {
          prop: "deviceName",
          label: "DeviceName",
        },
        {
          prop: "productKey",
          label: "ProductKey",
          width: 180,
        },
        {
          prop: "nodeType",
          label: "节点类型",
        },
      ],
      notSource: [],
      notLoading: false,
      notPagination: {
        current: 1,
        size: 7,
        total: 0,
      },
      notSelectList: [],
      alreadySearchVal: "",
      alreadySource: [],
      alreadyLoading: false,
      alreadyPagination: {
        current: 1,
        size: 7,
        total: 0,
      },
      alreadySelectList: [],

      deviceId: "", //设备id
      isEmpty: false, //左侧table empty 特殊处理
      waitCount: 0,
      doneCount: 0,
      width: `${(1330 / 1920) * 100}vw`, // postcss 计算方法
    };
  },
  components: { iotDialog, iotTable, iotPagination },
  props: {
    hostProductKey: {
      type: String,
    },
    hostDeviceName: {
      type: String,
    },
  },
  methods: {
    open(id) {
      this.deviceId = id;
      this.visible = true;
      this.getProductList();
    },
    getProductList() {
      getDeviceSubProduct().then((res) => {
        this.options = res.data;
      });
    },
    selectChange(data) {
      this.notPagination.current = 1;
      this.alreadyPagination.current = 1;
      let object = this.options.filter((item) => item.id == data)[0];
      // 未绑定的
      this.productInfo = object;
      this.getProductKey(0, true, true);
      // 已绑定的
      this.getProductKey(1, true, true);
    },
    fn_handle__query(flag) {
      if (!this.product) {
        this.$newNotify.warning({
          message: "请先选择产品",
        });
        return;
      }

      if (flag) {
        // 搜索待关联
        this.notPagination.current = 1;
        this.getProductKey(0);
      } else {
        // 搜索已关联
        this.alreadyPagination.current = 1;
        this.getProductKey(1);
      }
    },
    getProductKey(flag, isTips = false, isTotal = false) {
      let params = {};
      if (flag) {
        // 已绑定
        this.alreadySearchVal = isTotal ? "" : this.alreadySearchVal;
        params = {
          flag,
          productId: this.productInfo.id,
          productKey: this.productInfo.productKey,
          current: this.alreadyPagination.current,
          size: this.alreadyPagination.size,
          deviceName: isTotal ? "" : this.alreadySearchVal,
          hostProductKey: this.hostProductKey,
          hostDeviceName: this.hostDeviceName,
        };
      } else {
        // 未绑定
        this.notSearchVal = isTotal ? "" : this.notSearchVal;
        params = {
          flag,
          productId: this.productInfo.id,
          productKey: this.productInfo.productKey,
          current: this.notPagination.current,
          size: this.notPagination.size,
          deviceName: isTotal ? "" : this.notSearchVal,
          hostProductKey: this.hostProductKey,
          hostDeviceName: this.hostDeviceName,
        };
      }
      getDeviceSubDeviceByProductKey(params).then((res) => {
        if (res.code == 200) {
          let data = res.data;
          if (flag) {
            // 已绑定
            this.alreadySource = data.records || [];
            this.doneCount = isTotal ? data.total : this.doneCount;
            this.alreadyPagination.total = data.total || 0;
          } else {
            // 未绑定
            if (res.code == 4603) {
              this.isEmpty = true;
            } else {
              this.isEmpty = false;
            }
            this.notSource = data.records || [];
            this.waitCount = isTotal ? data.total : this.waitCount;
            this.notPagination.total = data.total || 0;
          }
        } else {
          if (flag) {
            // 已绑定
            this.alreadySource = [];
            this.doneCount = 0;
            this.alreadyPagination.total = 0;
          } else {
            this.notSource = [];
            this.waitCount = 0;
            this.notPagination.total = 0;
          }
          if (isTips) {
            this.$newNotify.warning({
              message: res.message,
            });
          }
        }
      });
    },
    handleClear() {},
    selectionChange(data, flag) {
      if (flag) {
        // 未关联数组
        this.notSelectList = data.map((item) => item.id);
      } else {
        // 已关联数组
        this.alreadySelectList = data.map((item) => item.id);
      }
    },
    handleCurrentChange(data, flag) {
      if (flag) {
        // 未绑定
        this.notPagination.current = data;
        this.getProductKey(0);
      } else {
        // 已绑定
        this.alreadyPagination.current = data;
        this.getProductKey(1);
      }
    },
    submitBind(flag) {
      if (!this.product) {
        this.$newNotify.warning({
          message: "请先选择产品",
        });
        return;
      }
      if (flag) {
        //绑定
        if (this.notSelectList.length == 0) {
          this.$newNotify.warning({
            message: "请选择未关联设备",
          });
          return;
        }
        getDeviceSubAdd({
          deviceId: this.deviceId,
          subDeviceIds: this.notSelectList.join(","),
        }).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.notPagination.current = 1;
            this.alreadyPagination.current = 1;
            // 未绑定的
            this.getProductKey(0, false, true);
            // 已绑定的
            this.getProductKey(1, false, true);
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        });
      } else {
        // 解绑
        if (this.alreadySelectList.length == 0) {
          this.$newNotify.warning({
            message: "请选择已关联设备",
          });
          return;
        }
        getDeviceSubRemove({
          deviceId: this.deviceId,
          subDeviceIds: this.alreadySelectList.join(","),
        }).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.notPagination.current = 1;
            this.alreadyPagination.current = 1;
            // 未绑定的
            this.getProductKey(0, false, true);
            // 已绑定的
            this.getProductKey(1, false, true);
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        });
      }
    },
    routeDevice() {
      this.$router.replace({
        path: "/device",
      });
    },
    handleClose() {
      this.product = "";
      this.productInfo = {};
      this.notSearchVal = "";
      this.alreadySearchVal = "";
      this.notSource = [];
      this.alreadySource = [];
      this.notPagination.current = 1;
      this.alreadyPagination.current = 1;
      this.notPagination.total = 0;
      this.alreadyPagination.total = 0;
      this.$emit("close");
    },
  },
};
</script>

<style lang="scss" scoped>
.content {
  padding-bottom: 12px;
  position: relative;
  h5 {
    color: #666666;
    font-size: 14px;
    line-height: 16px;
    font-weight: normal;
    padding: 8px 0;
    span {
      color: #ff0000;
    }
  }
  .item {
    padding-bottom: 26px;
  }

  .device {
    align-items: center;

    .device-data {
      width: 586px;
      h4 {
        padding-left: 14px;
        position: relative;
        color: #262626;
        font-size: 18px;
        font-weight: 500;
      }
      h4::before {
        content: "";
        width: 4px;
        height: 14px;
        background: #1890ff;
        position: absolute;
        left: 0;
        top: 50%;
        transform: translateY(-50%);
      }
      .table {
        margin-top: 18px;
        height: 520px;
        background: #ffffff;
        border: 1px solid #ececec;
        padding: 18px;
        .form-item {
          padding-bottom: 18px;
        }
      }
      .pagination {
        padding-top: 14px;
        justify-content: flex-end;
      }
    }
    .action {
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 0 12px;
      p {
        width: 72px;
        height: 32px;
        text-align: center;
        border-radius: 3px;
        cursor: pointer;
        transition: all 0.3s;
        span {
          color: #515151;
          font-size: 14px;
          line-height: 32px;
        }
        img {
          width: 12px;
          height: 9px;
        }
      }
      .bind {
        background: linear-gradient(
          270deg,
          #eeeeee 0%,
          #dadddf 75%,
          #c9c9c9 100%
        );
        background-size: 200%;
        background-position: 100% 0;
        margin-bottom: 14px;
        span {
          padding-right: 8px;
        }
      }
      .bind:hover {
        // background: linear-gradient(270deg, #e7e7e7 0%, #c9c9c9 100%);
        background-position: 0;
      }
      .unbound {
        background: linear-gradient(
          90deg,
          #eeeeee 0%,
          #dadddf 75%,
          #c9c9c9 100%
        );
        background-size: 200%;

        span {
          padding-left: 8px;
        }
        img {
          transform: rotate(180deg);
        }
      }
      .unbound:hover {
        // background: linear-gradient(90deg, #e7e7e7 0%, #c9c9c9 100%);
        background-position: 100% 0;
      }
    }
  }
  .mask {
    position: absolute;
    width: 100%;
    height: calc(100% - 92px);
    position: absolute;
    top: 92px;
    left: 0;
    background: rgba(0, 0, 0, 0.7);
    align-items: center;
    justify-content: center;
    z-index: 99;
    span {
      color: #ffffff;
      text-align: center;
      font-family: H_Medium;
      font-size: 18px;
    }
  }
  .item {
    /deep/ {
      .el-input__inner {
        width: 586px;
        border-radius: 0;
      }
    }
  }

  .device {
    /deep/ .el-select {
      .el-input__inner::placeholder {
        color: #515151;
      }
    }
    /deep/ {
      .el-table__header {
        tr {
          height: 42px !important;
        }
        .el-table__cell {
          padding: 0;
        }
      }
      .el-table__row {
        height: 42px !important;
        .el-table__cell {
          padding: 0;
        }
      }
    }
  }
}

/deep/ .empty {
  padding-top: 68px;
  color: #888888;
  font-size: 14px;
  span {
    color: #018aff;
    cursor: pointer;
  }
}
.device-table-content {
  .iot-table {
    height: 378px;
  }
}
</style>
