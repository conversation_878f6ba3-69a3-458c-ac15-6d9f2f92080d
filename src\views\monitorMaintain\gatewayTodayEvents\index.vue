<template>
  <div class="config-list">
    <div class="config-top flex">
      <div class="top-left"></div>
      <div class="top-right flex">
        <el-select v-model="alarmOptions" @change="clear_optionsData($event)">
          <el-option
            v-for="item in searchOptonsList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div>
          <el-input
            v-if="alarmOptions == 0"
            class="alarm-input"
            v-model="keyword"
            @keyup.enter.native="handleSearch"
            @clear="handleClear"
            :disabled="alarmOptions == 0"
            placeholder="输入关键词搜索"
          >
          </el-input>
          <el-input
            v-if="alarmOptions == 1"
            class="alarm-input"
            v-model="keyword"
            @keyup.enter.native="handleSearch"
            @clear="handleClear"
            placeholder="输入关键词搜索"
          >
          </el-input>
          <el-input
            v-if="alarmOptions == 2"
            class="alarm-input"
            v-model="keyword"
            @keyup.enter.native="handleSearch"
            @clear="handleClear"
            placeholder="输入关键词搜索"
          >
          </el-input>
          <el-select
            v-model="keyword"
            placeholder="请选择事件等级"
            v-if="alarmOptions == 4"
          >
            <el-option
              v-for="item in alarmLevelList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="keyword"
            placeholder="请选择事件类型"
            v-if="alarmOptions == 3"
          >
            <el-option
              v-for="item in alarmClassList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="keyword"
            placeholder="请选择事件状态"
            v-if="alarmOptions == 5"
          >
            <el-option
              v-for="item in alarmStatusList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <el-date-picker
          v-model="timeTaking"
          type="datetimerange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          prefix-icon="riqi"
          class="custom-datepicker"
        >
        </el-date-picker>
        <iot-button
          text="查询"
          style="margin-left: 12px"
          @search="handleSearch"
        ></iot-button>
        <iot-button
          text="重置"
          type="white"
          style="margin: 0px 12px"
          @search="handleClear"
        ></iot-button>
      </div>
    </div>
    <div class="config-content">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="eventType" slot-scope="scope">
          <span>{{
            scope.row.alarmType == 1
              ? "设备事件"
              : scope.row.alarmType == 2
              ? "业务事件"
              : scope.row.alarmType == 3
              ? "系统事件"
              : scope.row.alarmType == 4
              ? "其他事件"
              : "--"
          }}</span>
        </template>
        <template slot="level" slot-scope="scope">
          <span>{{
            scope.row.alarmLevel == 1
              ? "紧急"
              : scope.row.alarmLevel == 2
              ? "重要"
              : scope.row.alarmLevel == 3
              ? "次重要"
              : scope.row.alarmLevel == 4
              ? "提示"
              : "--"
          }}</span>
        </template>
        <template slot="status" slot-scope="scope">
          <div class="table-status">
            <div class="status flex" v-if="scope.row.eventStatus == 0">
              <div class="red"></div>
              <div>待处理</div>
            </div>
            <div class="status flex" v-if="scope.row.eventStatus == 1">
              <div class="green"></div>
              <div>已关闭</div>
            </div>
          </div>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p class="color2" @click="open_Detail(scope.row)">详情</p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="config-bottom" v-if="tableData.length != 0">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>
  
  <script>
import IotTable from "@/components/iot-table";
import IotButton from "@/components/iot-button";
import IotPagination from "@/components/iot-pagination";
import { getEventsDatail } from "@/api/monitorMaintain";
export default {
  components: {
    IotTable,
    IotPagination,
    IotButton,
  },
  watch: {
    timeTaking(newVal) {
      //处理时间选择器中获取到的时间
      if (Array.isArray(newVal)) {
        const [start, end] = newVal.map((time) => {
          const d = new Date(time);
          const year = d.getFullYear();
          const month = (d.getMonth() + 1).toString().padStart(2, "0");
          const day = d.getDate().toString().padStart(2, "0");
          const hours = d.getHours().toString().padStart(2, "0");
          const minutes = d.getMinutes().toString().padStart(2, "0");
          const seconds = d.getSeconds().toString().padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        });
        this.validBeginTime = start;
        this.validEndTime = end;
      }
    },
  },
  data() {
    return {
      productKey: "P377jx5fX6WCIlMC",
      alarmOptions: "0",
      alarmRuleName: "",
      alarmLevel: "", //事件级别
      alarmClass: "",
      alarmStatus: "",
      alarmDeviceName: "",
      timeTaking: [],
      validBeginTime: "",
      validEndTime: "",
      tableData: [],
      keyword: "",
      loading: true,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      searchOptonsList: [
        { name: "全部", value: "0" },
        { name: "设备名称/DviceName", value: "2" },
        { name: "事件类型", value: "3" },
        { name: "事件等级", value: "4" },
        { name: "事件状态", value: "5" },
      ],
      alarmLevelList: [
        { name: "紧急", value: "1" },
        { name: "重要", value: "2" },
        { name: "次重要", value: "3" },
        { name: "提示", value: "4" },
      ],
      alarmClassList: [
        { name: "设备事件", value: "1" },
        { name: "业务事件", value: "2" },
        { name: "系统事件", value: "3" },
        { name: "其他事件", value: "4" },
      ],
      alarmStatusList: [
        { name: "待处理", value: "0" },
        { name: "已关闭", value: "1" },
      ],
      columns: [
        { label: "DeviceName", prop: "deviceName" },
        { label: "设备名称", prop: "aliasName" },
        { label: "所属产品", prop: "productName" },
        { label: "事件类型", prop: "alarmType", slotName: "eventType" },
        { label: "内容", prop: "message", width: "200" },
        { label: "事件等级", prop: "alarmLevel", slotName: "level" },
        { label: "状态", prop: "status", slotName: "status" },
        { label: "发生时间", prop: "createTime", width: "200" },
        { label: "关闭时间", prop: "closeTime", width: "200" },
        { label: "操作", slotName: "operation" },
      ],
    };
  },
  mounted() {
    this.fn_get_table_data();
  },
  methods: {
    //获取表格数据
    fn_get_table_data() {
      let params = {
        productKey: this.productKey,
        queryType: this.alarmOptions,
        keyword: this.keyword,
        beginTime: this.validBeginTime,
        endTime: this.validEndTime,
        ...this.pagination,
      };
      getEventsDatail(params).then((res) => {
        if (res.code == 200) {
          this.tableData = res.data.records;
          this.pagination.total = res.data.total;
          this.loading = false;
        }
      });
    },
    open_Detail(row) {
      console.log(row);
      let data = {
        id: row.id,
        title: row.message,
        status: row.eventStatus,
        // key: row.nodeTypeKey,
      };
      this.$store.dispatch("setLayoutInfo", data);
      this.$router.push({
        path: "todayEventHanding",
        query: {
          //封装成对象传参会导致无法保存数据，需优化
          alarmLevel: row.alarmLevel,
          alarmType: row.alarmType,
          aliasName: row.aliasName,
          closeTime: row.closeTime,
          content: row.content,
          createTime: row.createTime,
          createUser: row.createUser,
          createDept: row.createDept,
          deviceName: row.deviceName,
          eventIdentifier: row.eventIdentifier,
          eventStatus: row.eventStatus,
          id: row.id,
          isDeleted: row.isDeleted,
          message: row.message,
          productKey: row.productKey,
          status: row.status,
          tenantId: row.tenantId,
          updateTime: row.updateTime,
          updateUser: row.updateUser,
          processingDetails: row.processingDetails,
          processingPlan: row.processingPlan,
          processingSource: row.processingSource,
          productName: row.productName,
          projectName: row.projectName,
        },
      });
    },
    //查询
    handleSearch() {
      let params = {
        productKey: "P377jx5fX6WCIlMC",
        queryType: this.alarmOptions,
        keyword: this.keyword,
        beginTime: this.validBeginTime,
        endTime: this.validEndTime,
        ...this.pagination,
      };
      this.fn_get_table_data(params);
    },
    //重置
    handleClear() {
      this.productKey = "P377jx5fX6WCIlMC";
      this.alarmOptions = "0";
      this.keyword = "";
      this.timeTaking = [];
      this.fn_get_table_data();
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.current = 1;
      this.pagination.size = val;
      let params = {
        queryType: this.alarmOptions,
        keyword: this.keyword,
        beginTime: this.validBeginTime,
        endTime: this.validEndTime,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;
      let params = {
        queryType: this.alarmOptions,
        keyword: this.keyword,
        beginTime: this.validBeginTime,
        endTime: this.validEndTime,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    //清除查询关键字
    clear_optionsData() {
      this.keyword = "";
    },
  },
};
</script>
  
  <style lang="scss" scoped>
.config-list {
  padding: 0px 32px 0 32px;
  margin: 0px 10px 0px 10px;
  .config-top {
    margin-right: -12px;
    margin-top: 18px;
    justify-content: space-between;
    .top-right {
      .el-input {
        width: 188px !important;
      }
      .el-range-editor {
        border-radius: 0;
        /deep/ .el-input__inner {
          padding: 0;
        }
        /deep/ .el-input__icon {
          height: auto;
        }
        /deep/ .el-range-separator {
          height: 32px;
        }
      }
      .el-select {
        width: 188px;
        border-radius: 0;
        margin-right: 14px;
        .el-select-dropdown__item {
          height: 38px;
          line-height: 38px;
        }
      }
      /deep/ .el-input__inner {
        border-radius: 0;
      }
      .alarm-input {
        width: 240px;
        margin-right: 14px;
      }
      /deep/ {
        .custom-datepicker .el-input__icon {
          /* 调整图标位置的样式 */
          // margin-right: 20px;
          margin-bottom: 0px;
        }
      }
    }
  }
  .config-content {
    margin-top: 18px;
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
    .table-status {
      .status {
        .red {
          background: #ff4d4f;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .green {
          background: rgba(196, 196, 196, 1);
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .yellow {
          background: #e6a23c;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
      }
    }
  }
  .config-bottom {
    margin-top: 16px;
    text-align: right;
    // padding: 14px 0;
  }
}
</style>