// 检测基本类型
export const checkDefault = function (
  type,
  item,
  rules = {},
  checkTime = false
) {
  if (type == "int") {
    if (!/^-?[0-9]\d*$/.test(item)) {
      console.warn("格式错误，请输入 int 格式参数");
      return { flag: true, text: "格式错误，请输入 int 格式参数" };
    }
  } else if (type == "float") {
    if (!/^-?\d{1,12}([.]\d{0,7})?$/.test(item)) {
      console.warn("格式错误，请输入 float 格式参数");
      return { flag: true, text: "格式错误，请输入 float 格式参数" };
    }
  } else if (type == "double") {
    if (!/^-?\d{1,12}([.]\d{0,16})?$/.test(item)) {
      console.warn("格式错误，请输入 double 格式参数");
      return { flag: true, text: "格式错误，请输入 double 格式参数" };
    }
  } else if (type == "text") {
    if (rules.length !== "" && rules.length < item.length) {
      console.warn("格式错误，请输入 text 格式参数");
      return { flag: true };
    }
    if (item.length > 1000) return { flag: true };
  } else if (type == "enum" || type == "bool") {
    // 数组下的枚举 布尔  取值  存在即合理
    if (!rules[item]) return { flag: true };
  } else if (type == "time" && checkTime) {
    // 时间类型  10位时间戳
    if (!/^\d{10,10}$/.test(item))
      return { flag: true, text: "请输入十位时间戳" };
  }
  if (type == "int" || type == "float" || type == "double") {
    if (rules.max !== "" && rules.min !== "") {
      if (Number(rules.min) > item || Number(rules.max) < item)
        return {
          flag: true,
          text: `请输入正确的取值范围(${rules.min}~${rules.max})`,
        };
    }
    if (rules.min !== "" && Number(rules.min) > item)
      return { flag: true, text: `取值不得小于${rules.min}` };
    if (rules.max !== "" && Number(rules.max) < item)
      return { flag: true, text: `取值不得超过${rules.max}` };
  }
  return { flag: false, text: "" };
};

// 检测数组项  (基本类型的特殊情况)
export const checkValue = function (type, item, egRules) {
  console.log(item);
  if (type == "int") {
    return !/^-?[0-9]\d*$/.test(item);
  } else if (type == "float") {
    return !/^-?\d{1,12}([.]\d{0,7})?$/.test(item);
  } else if (type == "double") {
    return !/^-?\d{1,12}([.]\d{0,16})?$/.test(item);
  } else if (type == "text") {
    return item.length > 1000 ? true : false;
  } else if (type == "object") {
    let keys;
    let json;
    if (typeof item === "object") {
      json = item;
    } else {
      try {
        json = JSON.parse(item);
      } catch (err) {
        console.warn("json格式解析失败  ：未填写完整");
        return false;
      }
    }

    // 判断是否为对象
    if (Object.prototype.toString.call(json) !== "[object Object]") {
      console.warn("数据格式错误  ：不是对象字符串");
      return true;
    }
    keys = Object.keys(json);
    if (keys.length == 0) {
      // 不允许对象为空
      console.warn("数据格式错误  ：对象内为空");
      return true;
    } else if (keys.length != egRules.length) {
      // 对象有属性  但与 模型 对象属性长度不一致  ：未填写完整
      console.warn("对象存在属性，但与物模型对象属性长度不一致  ：未填写完整");
      return true;
    } else {
      // 具体判断属性值
      for (let i = 0; i < keys.length; i++) {
        let index = egRules.findIndex((item) => item.id == keys[i]);
        if (index !== -1) {
          // 找到对应项
          let result = checkDefault(
            egRules[index].data.type,
            json[keys[i]],
            egRules[index].data.rules,
            true
          );
          if (result.flag) return true;
        } else {
          return true;
        }
      }
    }
  }
  return false;
};

// 检测类型
export const judgeType = function (type, item) {
  if (type === "int" || type === "float" || type === "double") {
    if (typeof item !== "number") {
      // int float double
      return true;
    }
  } else if (type === "text") {
    if (typeof item !== "string") {
      // text类型
      return true;
    }
  } else if (type === "object") {
    if (typeof item !== "object") {
      return true;
    }
  }
  return false;
};

// 检测输入的是否为数组json字符串
export const arrayValidate = function (value, rules, callback, errTips) {
  if (!value) return;
  let list; //接收处理后 的json
  let size = rules.size; // 数组长度
  let { type, rules: egRules } = rules.item; //数组项类型及 规则
  let errText = errTips || "格式错误，请输入数组格式参数";
  let typeFlag = false,
    valueFlag = false; // 子项数组是否满足规则
  try {
    list = JSON.parse(value);
  } catch (err) {
    // json 解析失败 报错 退出函数
    console.warn("JSON解析错误：该字符串未满足JOSN格式");
    callback(errText);
    return false;
  }
  // 检测解析后是否为数组
  if (!Array.isArray(list)) {
    console.warn("该字符串满足JOSN格式但不是数组");
    callback(errText);
    return;
  }
  // 检测数组长度是否满足
  if (list.length > size || list.length <= 0) {
    console.warn("该字符串满足JOSN格式但数组长度不符");
    callback(`请输入正确的长度范围（1~${size}）`);
  }
  // 检测数组子项
  // 用for循环  方便中断循环
  for (let i = 0; i < list.length; i++) {
    let item = list[i];
    // 检查类型
    typeFlag = judgeType(type, item);
    if (typeFlag) {
      console.warn("该字符串满足JSON数组字符串，但不是物模型定义的子项类型");
      callback(errText);
      break;
    }
    // 类型值判断
    valueFlag = checkValue(type, item, egRules);
    if (valueFlag) {
      console.warn(
        "该字符串满足JSON数组字符串，但子项类型值未满足物模型定义的数据结构"
      );
      callback(errText);
      break;
    }
  }
};

// 根据模型解析成表格数据
export const formatModel = function (list) {
  if (!Array.isArray(list) || list.length == 0) return list;
  let result = list.map((item) => {
    if (item.data.type === "enum" || item.data.type === "bool") {
      // 枚举 或 bool
      let childList = item.data.type === "enum" ? "enumList" : "boolList";
      item.data[childList] = [];
      for (let i in item.data.rules) {
        item.data[childList].push({
          key: i,
          value: i,
          label: `${item.data.rules[i]} - ${i}`,
        });
      }
    } else if (item.data.type === "object") {
      item.data.objectList = this.formatModel(item.data.rules);
    }
    item.value = "";
    return item;
  });
  return result;
};
