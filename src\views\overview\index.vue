<!--
 * @Author: your name
 * @Date: 2021-11-03 13:39:56
 * @LastEditTime: 2021-11-08 21:10:08
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \tenant-web\src\views\overview\index.vue
-->
<template>
	<div>概览</div>
</template>

<script>
import { getChildList } from '@/api/system'
export default {
	data() {
		return {}
	},
	mounted() {
		getChildList().then((res) => {
			if (res.code == 200) {
                console.log('getChildList request done');
			}
		})
	},
}
</script>

<style lang="scss" scoped></style>
