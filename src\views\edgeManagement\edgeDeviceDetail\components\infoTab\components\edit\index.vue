<template>
	<iot-dialog
		:visible.sync="visible"
		:title="title"
		width="718px"
		@callbackSure="fn_sure"
		@close="handleClose"
	>
		<template #body>
			<!-- 编辑设备 -->
			<iot-form>
				<template #default>
					<el-form
						ref="produceForm"
						class="produceForm"
						:rules="rules"
						:label-position="'top'"
						@validate="fn_validate"
						:model="produceForm"
						label-width="80px"
					>
						<el-form-item label="产品名称" prop="productName">
							<el-input
								:disabled="true"
								v-model="produceForm.productName"
							></el-input>
						</el-form-item>
						<el-form-item label="DeviceName" prop="deviceName">
							<el-input
								:disabled="true"
								v-model="produceForm.deviceName"
							></el-input>
						</el-form-item>

						<el-form-item label="节点类型" prop="nodeType">
							<el-input
								:disabled="true"
								v-model="produceForm.nodeType"
							></el-input>
						</el-form-item>

						<el-form-item label="当前状态" prop="status">
							<el-select
								v-model="produceForm.status"
								:disabled="true"
								placeholder
							>
								<el-option
									v-for="item in statusList"
									:key="item.value"
									:label="item.label"
									:value="item.value"
								></el-option>
							</el-select>
						</el-form-item>

						<el-form-item label="备注名称" prop="aliasName">
							<el-input
								v-model="produceForm.aliasName"
							></el-input>
						</el-form-item>
						<div class="el-form-tips" v-if="aliasTrue">
							不支持特殊字符，字数不能超过32个字符
						</div>

						<el-form-item label="设备描述" prop="description">
							<el-input
								type="textarea"
								v-model="produceForm.description"
							></el-input>
						</el-form-item>
						<div v-if="descTrue" class="el-form-tips">
							最多不超过200个字符
						</div>
					</el-form>
				</template>
			</iot-form>
		</template>
	</iot-dialog>
</template>

<script>
import IotForm from '@/components/iot-form'
import IotDialog from '@/components/iot-dialog'
import { getDeviceUpdate } from '@/api/device.js'
import { reg_seven } from '@/util/util.js'
export default {
	data() {
		return {
			visible: false,
			title: '修改设备',
			model: {}, //父页面数据
			produceForm: {
				productName: '',
				deviceName: '',
				nodeType: '',
				status: '',
				aliasName: '',
				description: '',
			},
			rules: {
				aliasName: [
					{ required: false, validator: this.checkAliasName },
				],
				description: [
					{
						required: false,
						trigger: 'blur',
						validator: this.checkLength,
					},
				],
			},
			aliasTrue: true,
			descTrue: true,
			statusList: [
				{
					value: 4,
					label: '未激活',
				},
				{
					value: 5,
					label: '在线',
				},
				{
					value: 6,
					label: '离线',
				},
			],
		}
	},
	components: { IotForm, IotDialog },

	methods: {
		checkAliasName(rule, value, callback) {
			if (value != '') {
				if (!reg_seven(value, 32)) {
					return callback(new Error('最多不超过32个字符'))
				} else {
					callback()
				}
			} else {
				callback()
			}
		},
		checkLength(rule, value, callback) {
			if (!reg_seven(value, 201)) {
				return callback(new Error('最多不超过200个字符'))
			} else {
				callback()
			}
		},
		// 表单验证触发
		fn_validate(name, value) {
			if (name === 'description') {
				this.descTrue = value
			}
			if (name === 'aliasName') {
				this.aliasTrue = value
			}
		},
		fn_sure() {
			this.$refs.produceForm.validate((vaild) => {
				if (vaild) {
					let params = {
						id: this.model.id,
						description: this.produceForm.description,
						aliasName: this.produceForm.aliasName,
					}
					getDeviceUpdate(params).then((res) => {
						if (res.code == 200) {
							this.$newNotify.success({
								message: res.message,
							})
							this.$emit('reload')
							this.visible = false
						} else {
							this.$newNotify.warning({
								message: res.message,
							})
						}
					})
				}
			})
		},
		handleClose() {
			this.$refs.produceForm.resetFields()
		},
		open(record) {
			this.model = record
			this.produceForm = JSON.parse(JSON.stringify(this.model))
			this.visible = true
		},
	},
}
</script>

<style lang="scss" scoped>
/deep/ {
	.el-form-tips {
		margin-top: -16px !important;
	}
}
</style>
