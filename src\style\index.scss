@import "./font.scss";
* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
  scrollbar-width: thin;
}
ul,
li {
  list-style: none;
}

.flex {
  display: flex;
}

h3,
h4,
h5,
h6 {
  font-family: H_Medium;
}
input::placeholder {
  color: #bfbfbf;
}

.gailan {
  font-family: "tenant";
}
.gailan::before {
  content: "\e646";
}

.shebeiguanli {
  font-family: "tenant";
}
.shebeiguanli::before {
  content: "\e647";
}
.guizeyinqing {
  font-family: "tenant";
}
.guizeyinqing::before {
  content: "\e648";
}
.jiankongyunwei {
  font-family: "tenant";
}
.jiankongyunwei::before {
  content: "\e649";
}
.zhanghaoguanli {
  font-family: "tenant";
}
.zhanghaoguanli::before {
  content: "\e64d";
}

.listicon {
  font-family: "tenant";
}
.listicon::before {
  content: "\e64b";
}

.cardicon {
  font-family: "tenant";
}
.cardicon::before {
  content: "\e64a";
}

.color1 {
  color: #ff4d4f;
}
.color2 {
  color: #018aff;
}

.bb_1 {
  border-bottom: 1px solid #efefef;
}

/*滚动条的设置*/
::-webkit-scrollbar-thumb {
  background-color: #7d7d7d;
  background-clip: padding-box;
  min-height: 28px;
  -webkit-border-radius: 2em;
  -moz-border-radius: 2em;
  border-radius: 2em;
}
/*滚动条移上去的背景*/

::-webkit-scrollbar-thumb:hover {
  cursor: pointer;
}

body::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  /**/
}
body::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}
body::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}
body::-webkit-scrollbar-thumb:hover {
  background: #333;
}
body::-webkit-scrollbar-corner {
  background: #179a16;
}
div::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  /**/
}
div::-webkit-scrollbar-track,
div::-moz-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}
div::-webkit-scrollbar-thumb,
div::-moz-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}
div::-webkit-scrollbar-thumb:hover,
div::-moz-scrollbar-thumb:hover {
  background: #333;
}
div::-webkit-scrollbar-corner,
div::-moz-scrollbar-corner {
  background: #179a16;
}
/*-----------*/
.el-notification {
  border-radius: 0;
}
.el-notification__content {
  margin: 0px 0 0 0 !important;
  font-size: 15px;
  font-weight: 400;
}

.notify-error {
  background: url(~@/assets/images/index/fail.svg) no-repeat;
  background-size: 100%;
}

.notify-success {
  background: url(~@/assets/images/index/success.svg) no-repeat;
  background-size: 100%;
}
.notify-warning {
  background: url(~@/assets/images/index/warning.svg) no-repeat;
  background-size: 100%;
}

.fix {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.fix2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-break: break-all;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

input:-webkit-autofill,
textarea:-webkit-autofill,
select:-webkit-autofill {
  background-color: rgba($color: eef7fb, $alpha: 0.5);
  background-image: none;
  transition: background-color 50000s ease-in-out 0s;
}

/* 下拉框 自定义 空 */
.empty-select {
  height: 34px;
  line-height: 34px;
  text-align: center;
  color: #888888;
  font-size: 14px;
}
