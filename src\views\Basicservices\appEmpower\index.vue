<template>
  <div class="main"></div>
</template>

<script>
export default {
  mounted() {
    this.connectorDataCanvas();
  },
  methods: {
    connectorDataCanvas() {
      var myChart = this.$echarts.init(document.querySelector(".main"));
      var option = {
        series: [
          {
            type: "gauge",
            splitNumber: null,
            radius: "80%",
            axisLine: {
              lineStyle: {
                width: 18,
                color: [[1, "rgba(228, 228, 228)"]],
              },
            },
            axisTick: {
              show: false,
            },
            detail: {
              valueAnimation: false,
              fontSize: 0,
              offsetCenter: [0, "80%"],
              color: "white",
              
            },
            pointer: {
            show: false, // 隐藏指针
          },
            data: [0],
          },
          {
            type: "gauge",
            axisLine: {
              lineStyle: {
                width: 15,
                color: [
                  [0.3, "#1875f0"],
                  [0.6, "#4ed065"],
                  [0.9, "#f18f1c"],
                  [1, "#f13a30"],
                ],
              },
            },
            axisTick: {
              show: true,
            },
            splitLine: {
              length: 15,
              lineStyle: {
                width: 2,
              },
            },
            axisLabel: {
              distance: -60,
              fontSize: 20,
              formatter: function (value) {
                if (value === 0 || value === 100) {
                  return value;
                } else {
                  return "";
                }
              },
              color: "black",
            },
            anchor: {
              show: true,
              showAbove: true,
              size: 25,
              itemStyle: {
                borderWidth: 10,
              },
            },
            title: {
              show: false,
            },
            detail: {
              valueAnimation: true,
              fontSize: 30,
              offsetCenter: [0, "80%"],
              color: "black",
            },
            data: [80],
          },
        ],
      };
      myChart.setOption(option);
      // window.addEventListener("resize", function () {
      //   myChart.resize();
      // });
    },
  },
};
</script>

<style lang="scss" scoped>
.main {
  width: 100%;
  height: 700px;
}
</style>