<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: --
 * @Date: 2023-8-26 16:54
 * @LastEditors: --
 * @LastEditTime: 2023-8-26 16:54
-->

<template>
  <div id="main">
    <div class="content-top">
      <div
        class="box"
        style="background: linear-gradient(to bottom, #ffe8cd, #fffaf2)"
      >
        <div class="num">
          <img src="~@/assets/images/alarm/todayAlarm.png" alt="" />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ this.topData.todayAlarm || "0" }}</span>
            </p>
            <p>今日告警
              <b class="todayDatail" @click="open_todayDatail()">详情</b>
            </p>
          </div>
        </div>
      </div>
      <div
        class="box"
        style="background: linear-gradient(to bottom, #ffe8cd, #fffaf2)"
      >
        <div class="num">
          <img src="~@/assets/images/alarm/todayAlarm.png" alt="" />
        </div>
        <div class="name">
          <div>
            <p>
              <span>{{ this.topData.themonthAlarm || "0" }}</span>
            </p>
            <p>本月告警</p>
          </div>
        </div>
      </div>

      <div
        class="specialboxs"
        style="background: linear-gradient(to bottom, #aae2f4, #e6fafe)"
      >
        <div class="num2">
          <img src="~@/assets/images/alarm/alarmRuleBule.png" alt="" />
        </div>
        <div class="name2">
          <div>
            <p>
              <span>{{ this.topData.totalCount || "0" }}</span>
            </p>
            <p>告警规则</p>
          </div>
        </div>
        <div class="name2">
          <div>
            <p>
              <span>{{ this.topData.enableCount || "0" }}</span>
            </p>
            <p>正常</p>
          </div>
        </div>
        <div class="name2">
          <div>
            <p>
              <span>{{ this.topData.disableCount || "0" }}</span>
            </p>
            <p>禁用</p>
          </div>
        </div>
      </div>
    </div>
    <div class="alarmstatic">
      <div class="alarmstatic_box">
        <div class="aliarmTitle">告警级别统计</div>
        <div class="alarmEcharts"></div>
      </div>
      <div class="alarmstatic_box2">
        <div class="aliarmTitle">告警处理状态</div>
        <div class="alarmEcharts2"></div>
      </div>
      <div class="alarmstatic_box3">
        <div class="aliarmTitle">
          <el-radio-group
            size="mini"
            v-model="params.alarmTime"
            @input="change()"
          >
            <el-radio-button label="hour">今日</el-radio-button>
            <el-radio-button label="day">最近7天</el-radio-button>
            <el-radio-button label="month">最近12个月</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </div>

    <div class="alarmtrend">
      <div class="alarmtrend-title">
        <div>告警趋势分析</div>
        <div class="alarmtrend-edit color2">
          <el-radio-group
            v-model="params.alarmtrend"
            size="mini"
            @input="changeTwo()"
          >
            <el-radio-button label="hour">今日</el-radio-button>
            <el-radio-button label="day">最近7天</el-radio-button>
            <el-radio-button label="month">最近12个月</el-radio-button>
          </el-radio-group>
        </div>
      </div>
      <div class="alarmtrend_Echarts"></div>
    </div>
  </div>
</template>

<script>
import {
  getAlarmStatic,
  alarmStatic,
  alarmtrend,
  alarmoverview,
} from "@/api/alarmCenter";
export default {
  name: "alarmStatistics",
  data() {
    return {
      params: {
        alarmTime: "hour",
        alarmtrend: "hour",
      },
      topData: {
        disableCount: "",
        enableCount: "",
        totalCount: "",
        todayAlarm: "",
        themonthAlarm: "",
      },
      level: [],
      processed: [],
      lineChartMap: {},
    };
  },
  mounted() {
    this.$bus.$on("get_alarm_static", this.get_alarm_static); //子组件发生改变时触发该方法
    this.get_alarm_static();
    this.getstada();
    this.getTrend();
    this.getAlarmStatic_alarm_overview();
  },
  beforeDestroy() {
    this.$bus.$on("get_alarm_static"); //页面销毁时解绑此方法
  },
  methods: {
    //告警规则统计
    get_alarm_static() {
      getAlarmStatic().then((res) => {
        this.topData.totalCount = res.data.totalCount;
        this.topData.enableCount = res.data.enableCount;
        this.topData.disableCount = res.data.disableCount;
      });
    },

    getAlarmStatic_alarm_overview() {
      alarmoverview().then((res) => {
        const { data } = res;
        this.topData.todayAlarm = data.dayTotal;
        this.topData.themonthAlarm = data.monthTotal;
      });
    },

    //获取图表数据接口
    getstada() {
      alarmStatic({ type: this.params.alarmTime }).then((res) => {
        this.level = res.data.level;
        this.processed = res.data.processed;
        this.get_alarm_levelStatic();
        this.get_alarm_statuStatic();
      });
    },

    // 获取告警趋势图表接口
    getTrend() {
      alarmtrend({ type: this.params.alarmtrend }).then((res) => {
        this.lineChartMap = res.data.lineChartMap;
        this.get_alarmtrend();
      });
    },

    get_alarm_levelStatic() {
      var myChart = this.$echarts.init(document.querySelector(".alarmEcharts"));
      // 指定图表的配置项和数据

      var option = {
        tooltip: {
          trigger: "item",
        },
        color: [
          "#FFAE58",
          "rgba(24, 117, 240, 1)",
          "rgba(119, 205, 243, 1)",
          "#6EDA81",
        ],
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "告警级别统计",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: true,
            label: {
              color: "black",
              show: true,
              position: "outside",
              alignTo: "none",
              formatter: "{b} {d}%",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 16,
              },
            },
            labelLine: {
              show: true,
            },
            data: this.level,
          },
        ],
      };
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    get_alarm_statuStatic() {
      var myChart = this.$echarts.init(
        document.querySelector(".alarmEcharts2")
      );
      // 指定图表的配置项和数据

      var option = {
        tooltip: {
          trigger: "item",
        },
        color: ["#cfcfcf", "#FFE27A"], //已处理、未处理
        legend: {
          top: "5%",
          left: "center",
          show: false,
        },
        series: [
          {
            name: "告警处理状态",
            type: "pie",
            radius: ["50%", "70%"],
            avoidLabelOverlap: true,
            label: {
              color: "black",
              show: true,
              formatter: "{b} {d}%",
            },
            emphasis: {
              label: {
                show: true,
                fontSize: 16,
              },
            },
            labelLine: {
              show: true,
            },
            data: this.processed,
          },
        ],
      };

      // 使用刚指定的配置项和数据显示图表。

      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },
    get_alarmtrend() {
      var myChart = this.$echarts.init(
        document.querySelector(".alarmtrend_Echarts")
      );
      var option = {
        tooltip: {
          trigger: "axis", // 设置触发类型为坐标轴轴线触发
          backgroundColor:"rgba(0, 0, 0, 0.5)",
          axisPointer:{
            type:"cross",
            label:{
              backgroundColor:"rgba(255, 82, 82, 1)"
            },
          },
          textStyle: {
            color: 'rgba(255, 255, 255, 1)'
          }
        },
        legend: {
          bottom: 0,
          data: ["设备事件", "系统事件", "业务事件", "其他事件"],
        },

        grid: {
          left: "3%",
          right: "4%",
          bottom: "10%", // 调整网格底部距离为 10%
          containLabel: true,
        },
        xAxis: [
          {
            type: "category",
            boundaryGap: false,
            data: this.lineChartMap.timeList,
            axisLabel: {
            interval: 2, // 设置刻度标签的间隔，即每隔两个刻度显示一个刻度标签
          },
          splitLine: {
            show: true,
            lineStyle: {
              type: "dashed", // 设置为虚线
            },
          },
          },
        ],
        yAxis: [
          {
            type: "value",
            axisLine: {
              show: true, // 显示坐标轴线
              },
              axisTick: {
                show: true,
              },
              splitLine: {
                show: false, // 隐藏分隔线
              },
          },
        ],
        series: [
          {
            name: "设备事件",
            type: "line",
            stack: "A",
            areaStyle: {
              color: "rgba(0, 194, 80, 0.05)",
            },
            smooth: true,
            // emphasis: {
            //   focus: "series",
            // },
            label: {
              show: true,
              position: "top",
            },
            itemStyle: {
              color: "#00C250",
            },
            data: this.lineChartMap.deviceList,
          },
          {
            name: "系统事件",
            type: "line",
            stack: "B",
            smooth: true,
            areaStyle: {
              color: "rgba(63, 125, 238, 0.05)",
            },
            // emphasis: {
            //   focus: "series",
            // },
            label: {
              show: true,
              // position: "top",
            },
            itemStyle: {
              color: "#3F7DEE",
            },
            data: this.lineChartMap.systemList,
          },
          {
            name: "业务事件",
            type: "line",
            stack: "C",
            smooth: true,
            areaStyle: {
              color: "rgba(255, 85, 0, 0.05)",
            },
            // emphasis: {
            //   focus: "series",
            // },
            label: {
              show: true,
              // position: "top",
            },
            itemStyle: {
              color: "#FF5500",
            },
            data: this.lineChartMap.serviceList,
          },
          {
            name: "其他事件",
            type: "line",
            smooth: true,
            stack: "D",
            areaStyle: {
              color: "rgba(255, 214, 0, 0.05)",
            },
            label: {
              show: true,
            },
            // emphasis: {
            //   focus: "series",
            // },
            itemStyle: {
              color: "#FFD600",
            },
            data: this.lineChartMap.otherList,
          },
        ],
      };
      // 使用刚指定的配置项和数据显示图表。
      myChart.setOption(option);
      window.addEventListener("resize", function () {
        myChart.resize();
      });
    },

    open_todayDatail() {
      let index = "1"
      this.$emit('changeIndex', index);
    },
    change() {
      this.getstada();
    },
    changeTwo() {
      this.getTrend();
    },
  },
};
</script>

<style lang="scss" scoped>
#main {
  margin-top: 32px;
  .content-top {
    box-sizing: border-box;
    width: 98.8%;
    min-width: 1085px;
    display: grid;
    margin: 0px 10px 0px 10px;
    grid-template-columns: repeat(3, 1fr); // 将 .content-top 分为3列
    .box {
      width: 386px;
      height: 102px;
      display: flex;
      padding: 0px 80px 0px 50px;
      box-shadow: 0px 8px 18px 0px rgba(0, 0, 0, 0.03);
      border-radius: 2px;
      margin-bottom: 10px;
      .num {
        display: flex;
        justify-content: left;
        align-items: center;
        img {
          width: 100%;
          vertical-align: middle;
          text-align: center;
        }
        @media (max-width: 1148px) {
          img {
            width: 75%;
          }
        }
        @media (max-width: 768px) {
          img {
            width: 60%;
          }
        }
      }
      .name {
        // font-family: YSBT;
        width: 169px;
        text-align: right;
        display: flex;
        justify-content: right;
        align-items: center;
        p {
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 16.41px;
          color: #515151;
          font-family: HarmonyOS Sans SC;
          margin-top: 5px;
          .todayDatail{
            color: #3e8fff;
            font-weight: normal;
          }
          .todayDatail:hover {
              cursor: pointer;
            }
        }
        span {
          font-size: 28px;
          font-weight: 700;
          line-height: 32.82px;
          color: rgba(51, 51, 51, 1);
        }
      }
    }
    .specialboxs {
      width: 780px;
      height: 102px;
      display: flex;
      padding: 0px 80px 0px 50px;
      box-shadow: 0px 8px 18px 0px rgba(0, 0, 0, 0.03);
      border-radius: 2px;
      margin-bottom: 10px;
      .num2 {
        display: flex;
        justify-content: left;
        align-items: center;
        img {
          width: 100%;
          vertical-align: middle;
          text-align: center;
        }
        @media (max-width: 1148px) {
          img {
            width: 75%;
          }
        }
        @media (max-width: 768px) {
          img {
            width: 60%;
          }
        }
      }
      .name2 {
        width: 200px;
        text-align: right;
        display: flex;
        justify-content: right;
        align-items: center;
        p {
          text-align: center;
          font-size: 14px;
          font-weight: 400;
          line-height: 16.41px;
          color: #515151;
          font-family: HarmonyOS Sans SC;
          margin-top: 9px;
        }
        span {
          font-size: 28px;
          font-weight: 700;
          line-height: 28.13px;
        }
      }
    }
  }
  .alarmstatic {
    margin-top: 22px;
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 28px 32px;
    outline: 1px solid #eeeff1;
    margin: 22px 10px 15px 10px;
    display: flex;
    justify-content: space-between;
    .alarmstatic_box {
      .aliarmTitle {
        font-weight: 500;
        font-size: 16px;
        line-height: 16px;
      }

      .alarmEcharts {
        width: 500px;
        height: 300px;
        position: relative;
        // background-color: antiquewhite;
      }
      .alarmEcharts::after {
        content: "";
        position: absolute;
        left: 480px;
        right: -120px;
        bottom: 130px;
        border-bottom: 2px solid #eeeff1;
        transform: scaleX(0.5) rotate(90deg);
      }
    }
    .alarmstatic_box2 {
      .aliarmTitle {
        font-weight: 500;
        font-size: 16px;
        line-height: 16px;
        /deep/ {
          .el-radio-button__inner {
            width: 100%;
            padding: 10px 30px 10px 30px;
          }
        }
      }
      .alarmEcharts2 {
        width: 500px;
        height: 300px;
      }
    }
    .alarmstatic_box3 {
      .aliarmTitle {
        /deep/ {
          .el-radio-button__inner {
            width: 100%;
            padding: 10px 30px 10px 30px;
          }
        }
      }
    }
  }
  .alarmtrend {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 28px 32px;
    outline: 1px solid #eeeff1;
    margin: 32px 10px 15px 10px;
    .alarmtrend-title {
      display: flex;
      justify-content: space-between;

      .alarmtrend-edit {
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: H_Regular;

        img {
          width: 12px;
          height: 12px;
          margin-right: 6px;
        }

        .el-radio-group {
          // margin-right: 24px;

          .el-radio-button {
            // width: 96px;
            text-align: center;
          }
        }
        /deep/ {
          .el-radio-button__inner {
            width: 100%;
            padding: 10px 30px 10px 30px;
          }
        }
      }
    }
    .alarmtrend_Echarts {
      width: 79vw;
      height: 450px;
    }
  }
}

@media (max-width: 768px) {
  #main {
    margin-top: 32px;
    .alarmstatic {
      .alarmstatic_box {
        width: 50%;
        .aliarmTitle {
          margin-bottom: 10px;
        }
        .alarmEcharts {
          width: 100%;
          height: 160px;
        }
        .alarmEcharts::after {
          content: "";
          position: absolute;
          left: 260px;
          right: -30px;
          bottom: 80px;
          border-bottom: 2px solid #eeeff1;
          transform: scaleX(0.5) rotate(90deg);
        }
      }
      .alarmstatic_box2 {
        width: 50%;
        .alarmEcharts2 {
          width: 100%;
          height: 160px;
        }
      }
      .alarmstatic_box3 {
        display: none;
      }
    }
    .alarmtrend {
      .alarmtrend_Echarts {
        // width: 79vw;
        height: 300px;
      }
    }
  }
}

@media (min-width: 769px) and (max-width: 1060px) {
  .alarmstatic {
    .alarmstatic_box {
      .aliarmTitle {
        margin-bottom: 10px;
      }
    }
  }
}
</style>
