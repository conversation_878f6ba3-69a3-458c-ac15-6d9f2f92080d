<template>
  <div class="model">
    <h4>安全验证</h4>
    <div class="form">
      <el-form ref="form" :model="form" :rules="rules">
        <el-form-item label="" prop="phone">
          <div class="form-item">
            <!-- <p>手机号码</p> -->
            <el-input
              v-model="form.phone"
              disabled
              :placeholder="`校验手机号码  ${placeholder}`"
            ></el-input>
          </div>
        </el-form-item>
        <el-form-item prop="captcha">
          <div class="form-item form-item-verify-code">
            <!-- <p><span>*</span>验证码</p> -->
            <el-input
              v-model="form.captcha"
              maxlength="6"
              tabindex="-1"
              placeholder="验证码"
            ></el-input>
            <div class="verify-code">
              <span v-if="!isSend" v-throttle="500" @click="getCode">{{
                countDownTips
              }}</span>
              <span v-else style="color: #bfbfbf">{{
                countDown + "s后重新获取"
              }}</span>
            </div>
          </div>
        </el-form-item>
      </el-form>
      <div class="handle-next" @click="handleConfirm">
        <span>确认</span>
      </div>
      <div class="handle-login">
        <span @click="handleLogin">已有账号，立即登录</span>
      </div>
    </div>
  </div>
</template>

<script>
import { sendMessage, checkCaptcha } from "@/api/user";
export default {
  data() {
    const verifyCaptcha = (rule, value, callback) => {
      if (this.sendError) {
        // 出现异常  退出验证
        callback(new Error(this.sendErrorMessage));
        return;
      }
      if (value === "") {
        callback(new Error("请输入6位验证码"));
      } else {
        let reg = /^\d+$|^\d+[.]?\d+$/;
        if (reg.test(value)) {
          if (!this.phone) {
            callback(new Error("手机号有误，请刷新页面后重新输入"));
            return;
          }

          checkCaptcha({
            captcha: this.form.captcha,
            phone: this.phone,
          })
            .then((res) => {
              if (res.code != 200) {
                callback(new Error(res.message));
              } else {
                callback();
              }
            })
            .finally(() => {
              callback();
            });
        } else {
          callback(new Error("只能输入数字"));
        }
      }
    };
    return {
      placeholder: "",
      countDownOpen: false,
      countDown: 119,
      sendError: false, //发送验证码是否出现异常
      sendErrorMessage: "", //异常文字
      countDownTips: "发送验证码",
      isSend: false,
      form: {
        captcha: "",
      },
      rules: {
        captcha: [
          {
            required: true,
            trigger: "blur",
            validator: verifyCaptcha,
          },
          {
            min: 6,
            max: 6,
            trigger: "blur",
            message: "请输入6位验证码",
          },
          // { pattern: /^\d+$|^\d+[.]?\d+$/, message: "只能输入数字" },
        ],
      },
    };
  },
  props: {
    phone: {
      type: String,
    },
  },
  watch: {
    phone() {
      let pat = /(\d{3})\d*(\d{4})/;
      this.placeholder = this.phone.replace(pat, "$1****$2");
    },
  },
  methods: {
    checkValue(callback) {
      checkCaptcha({
        phone: this.phone,
        captcha: this.form.captcha,
      }).then((res) => {
        if (res.code == 200) {
          if (callback) callback();
        } else {
          this.$message.warning(res.message);
        }
      });
    },
    getCode() {
      if (this.countDownOpen) return;
      if (!this.phone) {
        this.$message.warning("手机号不存在，请刷新页面或联系管理员");
        return false;
      }
      this.countDownTips = "正在发送中";
      this.countDownOpen = true;
      sendMessage({
        phone: this.phone,
        messageType: "resetPassword",
      })
        .then((res) => {
          this.$refs.form.clearValidate(["captcha"]);
          if (res.code == 200) {
            this.isSend = true;
            this.sendError = false; //未出现异常
            this.$message.success(res.message);
            let timer = setInterval(() => {
              this.countDown--;
              if (this.countDown <= 0) {
                this.countDownOpen = false;
                this.isSend = false;
                this.countDown = 59;
                clearInterval(timer);
              }
            }, 1000);
          } else {
            this.countDownOpen = false;
            this.sendError = true;
            this.sendErrorMessage = res.message;
            this.$refs.form.validateField("captcha");
          }
        })
        .finally(() => {
          this.countDownTips = "发送验证码";
        });
    },
    handleConfirm() {
      this.sendError = false; //重置 短信发送错误  防止进入短信发送错误从而阻塞后续验证
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$emit("next", { step: 2, captcha: this.form.captcha });
        } else {
          return false;
        }
      });
    },
    handleLogin() {
      this.$emit("route");
    },
  },
};
</script>

<style lang="scss" scoped>
.model {
  width: 1020px;
  height: 460px;
  margin: 0 auto;
  background: #ffffff;
  box-shadow: 0px 48px 48px rgba(0, 0, 0, 0.05);
  border-radius: 3px;
  h4 {
    height: 98px;
    line-height: 98px;
    color: #333333;
    font-weight: 500;
    font-size: 28px;
    text-align: center;
    border-bottom: 1px solid #f5f5f5;
  }
  .form {
    width: 330px;
    margin: 0 auto;
    padding-top: 48px;

    .form-item {
      align-items: center;
      p {
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        padding-bottom: 8px;
        span {
          color: #f53e3e;
        }
      }
    }
    /deep/ {
      .el-form-item {
        margin-bottom: 34px;
      }
      .el-input {
        input {
          border-radius: 0;
          height: 42px;
          font-size: 14px;
          border: 1px solid #e4e7ec;
        }
        input:focus,
        input:hover {
          border: 1px solid #018aff;
        }
        input::placeholder {
          color: #bfbfbf;
        }
      }
    }
    .form-item-verify-code {
      position: relative;
      /deep/ .el-input__inner {
        padding-right: 130px;
      }
      .verify-code {
        position: absolute;
        right: 15px;
        // top: 54px;
        top: 0px;
        height: 42px;
        line-height: 42px;
        text-align: right;
        width: 130px;
        color: #0088fe;
        cursor: pointer;
        span {
          font-size: 14px;
        }
      }
    }
    .handle-next {
      height: 42px;
      text-align: center;
      background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
      color: #ffffff;
      font-size: 16px;
      line-height: 42px;
      margin-top: 48px;
      cursor: pointer;
    }
    .handle-login {
      text-align: right;
      padding-top: 24px;
      span {
        color: #0088fe;
        cursor: pointer;
        font-size: 14px;
        line-height: 16px;
      }
    }
  }
}
</style>
