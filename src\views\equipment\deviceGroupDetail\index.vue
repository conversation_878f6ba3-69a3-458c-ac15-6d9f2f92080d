<template>
  <div id="main">
    <div class="info-content">
      <div class="info-title">
        <div>基础信息</div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>分组ID</span>
          <span>{{ params.groupId || "————" }}</span>
        </div>
        <div class="item">
          <span>设备总数</span>
          <span>{{ params.deviceNumber || "————" }}</span>
        </div>
        <div class="item">
          <span>添加时间</span>
          <span>{{ params.createTime || "————" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>在线设备数</span>
          <span>{{ "————" }}</span>
        </div>
        <div class="item">
          <span>激活设备数</span>
          <span>{{ "————" }}</span>
        </div>
        <div class="item">
          <span>分组描述</span>
          <span>{{ params.introduce || "————" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>标签</span>
          <span>{{  "————" }}</span>
        </div>
      </div>
    </div>
    <div class="table">
      <div class="tableNav">
        <form-search
          :selectHolder="selectHolder"
          :inputHolders="inputHolders"
        ></form-search>
        <!-- <Iot-button text="- 批量移除设备" type="grey" @search="deletDevice" style="margin-left: 10px;"></Iot-button> -->
        <Iot-button
          text="+ 添加设备"
          style="margin-left: 10px"
          @search="addDevice"
        ></Iot-button>
      </div>
      <div class="deviceDetail-table">
        <iot-table
          :columns="columns"
          :data="tableData"
          :loading="loading"
          @selection-change="fn_select_more_data"
          @selection-del="fn_del_more_data"
          @del-callbackSure="fn_del_sure"
        >
          <template slot="status" slot-scope="scope">
            <div class="table-status">
              <div class="status flex" v-if="scope.row.status == 6">
                <div class="red"></div>
                <div>离线</div>
              </div>
              <div class="status flex" v-if="scope.row.status == 5">
                <div class="green"></div>
                <div>在线</div>
              </div>
              <div class="status flex" v-if="scope.row.status == 4">
                <div class="yellow"></div>
                <div>未激活</div>
              </div>
            </div>
          </template>
          <template slot="operation">
            <div class="flex table-edit">
              <p class="color2">查看详情</p>
              <p class="table-line"></p>
              <p class="color2">移除分组</p>
            </div>
          </template>
        </iot-table>
      </div>
    </div>
    <deviceTable
      :title="title"
      :visible.sync="visible"
      :width="dialogWidth"
      :id="this.$route.query.id"
      @customEvent="getDetail"
    ></deviceTable>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button";
import FormSearch from "@/components/form-search";
import IotTable from "@/components/iot-table";
import deviceTable from "./component/deviceTable.vue";

import { deviceGroupDetail, Delete_DeviceGroup } from "@/api/equipment";
export default {
  name: "deviceDetail",
  components: {
    IotButton,
    FormSearch,
    IotTable,
    deviceTable,
  },
  data() {
    return {
      columns: [
        {
          type: "selection",
          selectionText: false,
          title: "确定删除该设备？",
          text: "批量删除设备后，设备数据不可恢复，请确认是否删除？",
          visible: false,
          isShowdelete: true,
        },
        { label: "设备名称", prop: "deviceName" },
        { label: "DeviceName", prop: "deviceKey" },
        { label: "所属产品/ProductKey", prop: "productKey" },
        { label: "设备状态", slotName: "status", prop: "status" },
        { label: "操作", slotName: "operation" },
      ],
      //分组传入id
      params: {
        id: null,
        createTime:'',
        deviceNumber:'',
        groupId:'',
        index:'',
        introduce:'',
        name:''
      },
      selectHolder: "请选择所属产品/ProductKey",
      inputHolders: ["请输入设备名称/DeviceName", "请输入产品key"],

      tableData: [],
      loading: true,

      delIds: [], // 多选删除所需id

      visible: false, //弹出框是否显示
      title: "添加设备", //弹出框标题
      dialogWidth: "1118px", //弹出框宽度
    };
  },
  created() {
    Object.assign(this.params,{...this.$route.query});
  },

  methods: {
    // 获取详情列表信息
    getDetail() {
      deviceGroupDetail(this.params)
        .then((res) => {
          const { data } = res;
          this.tableData = data.records;
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 多选数据
    fn_select_more_data(data) {
      this.delIds = data.map((item) => {
        return item.deviceId;
      });
      if (this.delIds.length !== 0) {
        this.columns[0].selectionText = true;
      } else {
        this.columns[0].selectionText = false;
      }
    },
    // 多选删除按钮
    fn_del_more_data() {
      if (this.delIds.length !== 0) {
        this.columns[0].visible = true;
      } else {
        return;
      }
    },
    // 多选删除确定按钮
    fn_del_sure() {
      let data = {
        id: this.params.id,
        deviceId: this.delIds.join(","),
      };
      this.fn_del_table_data(data);
    },
    // 删除数据接口
    fn_del_table_data(data) {
      Delete_DeviceGroup(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
          this.getDetail();
          this.columns[0].visible = false;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    //添加设备
    addDevice() {
      this.visible = true;
    },
  },
  mounted() {
    this.getDetail();
  },
};
</script>

<style lang="scss" scoped>
#main {
  padding: 0px 32px 0px 32px;
  background-color: white;
  .info-content {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 28px 32px;
    outline: 1px solid #eeeff1;
    position: relative;
    .temperature {
      position: absolute;
      top: 170px;
      left: 18px;
      z-index: 99;
      p:nth-child(1) {
        color: rgba(81, 81, 81, 1);
        font-family: HarmonyOS Sans SC;
        font-size: 16px;
        font-weight: 400;
        line-height: 19px;
        letter-spacing: 0em;
      }
      p:nth-child(2) {
        color: rgba(51, 51, 51, 1);
        font-family: HarmonyOS Sans SC;
        font-size: 24px;
        font-weight: 500;
        line-height: 28px;
        letter-spacing: 0em;
      }
    }
    .empty {
      width: 100%;
      display: flex;
      justify-content: space-around;
    }
    .info-row {
      .item {
        flex: 1;
        padding-top: 20px;
        width: 50%;
        display: flex;
        height: 100%;
        font-size: 14px;
        font-weight: 400;

        span {
          height: 16px;
          line-height: 16px;
          display: block;

          &:first-child {
            color: #999999;
            width: 112px;
            text-align: right;
            flex-shrink: 0;
          }

          &:last-child {
            // width: 100%;
            flex: 1;
            color: #515151;
            margin-left: 48px;
          }
        }

        .item-span {
          padding-right: 22px;
          width: calc(100% - 100px - 22px);
          color: #515151;
          margin-left: 48px;
          word-wrap: break-word;
        }
      }
    }

    .systemdetection {
      display: flex;
      justify-content: space-between;
      .systemdetection_bottom1 {
        width: 370px;
        height: 320px;
        position: relative;
      }

      .systemdetection_bottom1::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom2 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_bottom2::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom3 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_bottom3::after {
        content: "";
        position: absolute;
        left: 300px;
        right: -90px;
        bottom: 170px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5);
        transform: rotate(90deg);
      }
      .systemdetection_bottom4 {
        height: 320px;
        width: 370px;
        position: relative;
      }

      .systemdetection_box {
        .systemdetection_top {
          width: 100%;
          display: flex;
          justify-content: space-between;
          padding: 5px 10px 10px 20px;
          div {
            font-size: 28px;
            font-weight: 500;
            color: rgba(51, 51, 51, 1);
            line-height: 32.82px;
            font-family: HarmonyOS Sans SC;
          }
          span {
            font-weight: 400;
            font-size: 16px;
            color: #515151;
            font-family: HarmonyOS Sans SC;
          }

          p {
            font-weight: 500;
            font-size: 28px;
          }
        }
      }
    }

    .info-title {
      display: flex;
      justify-content: space-between;

      .info-edit {
        cursor: pointer;
        display: flex;
        align-items: center;
        font-size: 14px;
        font-family: H_Regular;

        .el-radio-group {
          .el-radio-button {
            // width: 80px;
            text-align: center;
          }
        }
        /deep/ {
          .el-radio-button__inner {
            width: 96px;
          }
        }
      }
    }

    .eventCanvas {
      display: flex;
      justify-content: space-between;
      .test {
        width: 600px;
        height: 300px;
        position: relative;
      }
      .test::after {
        content: "";
        position: absolute;
        left: 400px;
        right: -160px;
        bottom: 160px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5) rotate(90deg);
      }
      .test2 {
        height: 300px;
        width: 450px;
        position: relative;
      }
      .test2::after {
        content: "";
        position: absolute;
        left: 280px;
        right: -170px;
        bottom: 160px;
        border-bottom: 1px solid #ededed;
        transform: scaleY(0.5) rotate(90deg);
      }
      .test3 {
        height: 300px;
        width: 450px;
        position: relative;
      }
      .identifying {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
        font-weight: 400;
        color: #515151;
        div:nth-child(1) {
          width: 10px;
          height: 5px;
          background-color: #00c250;
          margin: 0px 10px;
        }
        div:nth-child(3) {
          width: 10px;
          height: 5px;
          // background-color: #3F7DEE;
          margin: 0px 10px;
        }
      }

      p {
        flex-shrink: 0;
      }
    }

    .flow {
      .upAndDown {
        display: flex;

        div {
          width: 50%;
          font-size: 16px;

          span {
            font-size: 28px;
            color: #333333;
          }
        }

        div:first-child {
          text-align: end;
          padding-right: 50px;
        }
      }
      .flowLook {
        width: 100%;
        height: 400px;
      }
    }
    .identifying2 {
      // width: 90%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
      font-weight: 400;
      color: #515151;
      div:nth-child(1) {
        width: 10px;
        height: 5px;
        background-color: #00c250;
        margin: 0px 10px;
      }
      div:nth-child(3) {
        width: 10px;
        height: 5px;
        background-color: #3f7dee;
        margin: 0px 10px;
      }
    }
    .connectorEcharts {
      height: 400px;
      width: 100%;
    }
  }
  .table {
    width: 100%;
    height: 300px;
    // background-color: aqua;
    .tableNav {
      width: 100%;
      //   height: 54px;
      display: flex;
      justify-content: end;
      // padding: 12px;
      margin: 12px 0px;
    }
    .deviceDetail-table {
      .table-edit {
        display: flex;
        align-items: center;
        p {
          cursor: pointer;
        }
        .table-line {
          margin: 0px 12px;
          width: 1px;
          height: 13px;
          border: 1px solid #ededed;
        }
      }
      .table-status {
        .status {
          .red {
            background: #ff4d4f;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
          .green {
            background: #00c250;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
          .yellow {
            background: #e6a23c;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
        }
      }
    }
  }
}
</style>