<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-02-21 14:46:08
-->
<template>
  <div class="model">
    <div class="model-top">
      <div class="top-left">
        <el-radio-group v-model="type" size="medium" @change="handleTabChange">
          <el-radio-button :label="1">运行状态</el-radio-button>
          <el-radio-button :label="2">事件管理</el-radio-button>
          <el-radio-button :label="3">服务调用</el-radio-button>
        </el-radio-group>
      </div>
      <div class="top-right">
        <!-- 搜索栏 -->
        <form-search
          v-if="type == 1"
          :options="selectOptions"
          @search="fn_search_table_data"
          @clear="fn_clear_search_info"
          :oneMaxLength="50"
          :twoMaxLength="32"
          selectHolder="请选择"
          :inputHolders="inputHolders"
        >
          <template #other>
            <div class="card-toggle">
              <el-radio-group v-model="typeList">
                <el-radio-button label="1">
                  <font class="cardicon"></font>
                </el-radio-button>
                <el-radio-button label="2">
                  <font class="listicon"></font>
                </el-radio-button>
              </el-radio-group>
            </div>
          </template>
        </form-search>
        <div class="event-select flex" v-if="type == 2 || type == 3">
          <el-select v-model="eventType" v-if="type == 2">
            <el-option
              v-for="item in eventTypeOption"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
          <el-select v-model="time">
            <el-option
              v-for="item in timeOption"
              :key="item.value"
              :value="item.value"
              :label="item.label"
            ></el-option>
          </el-select>
          <el-date-picker
            v-if="time == 4"
            v-model="dateRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="pickerChange"
            align="right"
          ></el-date-picker>
        </div>
      </div>
    </div>

    <template v-if="type == 1">
      <status
        ref="status"
        :statusTable="statusTable"
        :productKey="productKey"
        :deviceName="deviceName"
        :tenant_id="tenant_id"
        :typeList="typeList"
      />
    </template>

    <template v-if="type == 2">
      <event
        ref="event"
        :productKey="productKey"
        :tenant_id="tenant_id"
        :deviceName="deviceName"
        :eventType="eventType"
        :time="time"
      />
    </template>

    <template v-if="type == 3">
      <service
        ref="service"
        :productKey="productKey"
        :tenant_id="tenant_id"
        :deviceName="deviceName"
        :time="time"
      />
    </template>
  </div>
</template>

<script>
import FormSearch from "@/components/form-search";

import Service from "./components/service.vue";
import Event from "./components/event.vue";
import Status from "./components/status.vue";
// import { getDeviceInfo } from "@/api/device";

import { productAbilityDetail } from "@/api/product.js";

export default {
  name: "model",
  components: {
    FormSearch,
    Service,
    Event,
    Status,
  },
  data() {
    return {
      type: "1",
      typeList: "1",
      selectOptions: [
        { id: "1", name: "属性标识符" },
        { id: "2", name: "属性名称" },
      ],
      inputHolders: ["请输入属性标识符", "请输入属性名称"],
      deviceForm: {}, //设备信息
      eventType: 1,
      eventTypeOption: [
        {
          value: 1,
          label: "全部类型",
        },
        {
          value: "info",
          label: "信息",
        },
        {
          value: "error",
          label: "故障",
        },
        {
          value: "alarm",
          label: "告警",
        },
      ],
      time: 1,
      timeOption: [
        {
          value: 1,
          label: "1小时",
        },
        {
          value: 2,
          label: "24小时",
        },
        {
          value: 3,
          label: "7天",
        },
        {
          value: 4,
          label: "自定义",
        },
      ],
      dateRange: [],
      pickerOptions: {
        disabledDate(time) {
          let curDate = new Date().getTime();
          let three = 6 * 30 * 24 * 3600 * 1000;
          let threeMonths = curDate - three;
          return time.getTime() > Date.now() || time.getTime() < threeMonths;
        },
      },
      statusTable: [],
      // statusTableCopy: [] //备份数组
    };
  },
  props: {
    id: {
      type: String,
    },
    productKey: {
      type: String,
    },
    deviceName: {
      type: String,
    },
    tenant_id: {
      type: String,
    },
  },
  mounted() {
    // this.getData();
    if (this.productKey) {
      this.http_get_detail();
    }
  },
  watch: {
    productKey() {
      this.http_get_detail();
    },
  },
  methods: {
    handleTabChange() {
      this.time = 1;
      this.dateRange = [];
    },
    http_get_detail() {
      let data = {
        // tenantId: this.tenant_id,
        // productKey: "IynRagPFXfssy7rC",
        productKey: this.productKey,
      };
      productAbilityDetail(data).then((res) => {
        let jsonData = "{}";
        if (res.code == 200 && typeof res.data == "string") {
          jsonData = res.data;
        }
        this.statusTable = JSON.parse(jsonData).properties || [];
      });
    },
    fn_search_table_data(data) {
      this.$refs.status.fn_search(data);
      // this.statusTable = this.statusTable.filter(item =>
      //   item.name.includes(data.value)
      // )
    },
    fn_clear_search_info() {
      this.$refs.status.fn_clear();
      // this.statusTable = this.statusTableCopy
    },
    pickerChange(data) {
      if (this.type == 2) {
        this.$refs.event.rangeDate = data;
      } else if (this.type == 3) {
        this.$refs.service.rangeDate = data;
      }
    },
    // getData() {
    //   let params = {
    //     id: this.id,
    //   };
    //   getDeviceInfo(params).then((res) => {
    //     if (res.code == 200) {
    //       this.deviceForm = res.data;
    //       this.deviceName = res.data.deviceName;
    //       this.productKey = res.data.productKey;
    //       this.http_get_detail()
    //     } else {
    //       this.$newNotify.error({
    //         message: res.message,
    //       });
    //     }
    //   });
    // },
  },
};
</script>

<style lang="scss" scoped>
.model {
  .model-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 18px 0px 18px 0px;
    // .top-left {
    // }
    .top-right {
      align-items: center;
      .event-select {
        align-items: center;
        .el-select {
          margin-right: 14px;
        }
        /deep/ .el-date-editor {
          width: calc(100% - 14px -240px);
          height: 34px;
          border-radius: 0;
          line-height: 26px;
          .el-range__icon,
          .el-input__icon {
            line-height: 26px !important;
            font-weight: 400;
          }

          .el-range-separator {
            line-height: 26px;
            font-family: H_Regular;
          }
        }
      }
    }
  }
}

/deep/ {
  .el-radio-button__inner {
    width: 110px;
  }
  .el-input__inner {
    border-radius: 0;
    height: 34px;
  }
  .el-textarea__inner {
    border-radius: 0;
    height: 100px;
    color: #515151;

    font-family: H_Medium;
  }
  .el-textarea__inner::placeholder {
    font-family: H_Regular;
    font-weight: normal;
  }
}

/deep/ {
  .card-toggle {
    margin-left: 15px;
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #fff;
      color: #409eff;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #fff;
      color: #409eff;
    }

    .el-radio-button__inner {
      width: 34px;
      height: 34px;
      line-height: 34px;
      padding: 0px;
    }
    .cardicon,
    .listicon {
      font-size: 28px;
    }
  }
}
</style>
