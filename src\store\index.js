/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-11 10:00:31
 * @LastEditors: hs
 * @LastEditTime: 2021-12-21 16:11:12
 */
import Vue from "vue";
import Vuex from "vuex";

import user from "./modules/user";
import device from "./modules/device";
import layout from "./modules/layout"
import online from "./modules/online"

import getters from "./getters";
Vue.use(Vuex);

export default new Vuex.Store({
  state: {},
  mutations: {},
  actions: {},
  modules: {
    user,
    device,
    layout,
    online
  },
  getters,
});
