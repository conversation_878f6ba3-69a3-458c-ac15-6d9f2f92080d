<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: hs
 * @LastEditTime: 2021-11-09 15:24:15
-->
<template>
  <div class="layout">
    <div class="content-404 flex">
      <div class="content-to">
        <p class="big-font">Oops!</p>
        <p class="sorry">sorry，您访问的页面不存在~</p>
        <div class="seconds">
          <span>将于</span> <span class="color">{{ count }} </span> <span> 秒后自动跳转登录页</span>
        </div>
        <iot-button :text="'前往登录页'" @search="back"></iot-button>
      </div>
      <img src="~@/assets/images/empty/404.png" alt="" />
    </div>
    <copyright class="copyright" />
  </div>
</template>

<script>
import copyright from "@/components/copyright";
import IotButton from "@/components/iot-button";
export default {
  components: {
    copyright,
    IotButton
  },
  data() {
    return {
      count: 5
    }
  },
  mounted() {
    this.countDown()
  },
  methods: {
    back() {
      this.$router.push({ path: "/login" })
    },
    countDown() {
      let timer = setInterval(() => {
        this.count --;
        if(this.count <= 0) {
          this.$router.push({
            path: '/login'
          })
          clearInterval(timer)
        }
      }, 1000)
    }
  },
  
};
</script>

<style lang="scss" scoped>
.layout {
  height: 100vh;
  background: #f8fcfd;
  .content-404 {
    justify-content: center;
    .content-to {
      margin-top: 250px;
      margin-right: 120px;
      .big-font {
        font-family: HarmonyOS Sans SC;
        margin-top: 40px;
        font-size: 40px;
        font-weight: bold;
        color: #333333;
      }
      .sorry {
        color: #515151;
        font-size: 18px;
        line-height: 21px;
        font-weight: 400;
        margin-top: 24px;
      }
      .seconds {
        color: #999999;
        font-size: 14px;
        line-height: 16px;
        margin-top: 10px;
        margin-bottom: 32px;
        .color {
          color: #018aff;
        }
      }
      
    }
    img {
      width: 600px;
      margin-top: 250px;
    }
  }
  .copyright {
    position: absolute;
    left: 38%;
    bottom: 0;
  }
}
</style>
