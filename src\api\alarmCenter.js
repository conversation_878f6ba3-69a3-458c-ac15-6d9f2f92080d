/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-21 14:15:23
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:11:11
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;



/**
 * @desc 告警统计
 * @returns
 */
export const getAlarmStatic = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/config/statistics`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则列表
 * @returns
 */
export const getRuleList = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/config/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 更改告警规则状态
 * @returns
 */
export const changeRuleStatus = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/config/status/update`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警列表
 * @returns
 */
export const getAlarmList = (params) => {
  return request({
    url: `${baseServer}/alarm/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警设备范围列表-未选择设备
 * @returns
 */
export const getrelatonWaitList = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/device/wait/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警设备范围列表-已选择设备
 * @returns
 */
export const getrelatonBindList = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/device/bind/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警设备范围列表-主界面获取已选择设备列表数据
 * @returns
 */
export const getrelatonBindListMain = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/device/bind/list/main`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警设备范围列表-绑定设备
 * @returns
 */
export const getuuID = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/device/identifierId`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警设备范围列表-绑定设备
 * @returns
 */
export const bindDevice = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/device/bind`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警设备范围列表-解绑设备
 * @returns
 */
export const removeDevice = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/device/unbind`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警规则-新建规则-获取事件
 * @returns
 */
export const getEventList = (params) => {
  return request({
    url: `${baseServer}/tenant/product/thingModel/productEvent/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则-新建规则-取消规则的数据清除
 * @returns
 */
export const removeData = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/device/data/check`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警规则-新建规则
 * @returns
 */
export const creatRule = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/config/create`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警规则-检验规则名称是否重复
 * @returns
 */
export const checkRuleName = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/config/checkName`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则-点击编辑获取规则详情
 * @returns
 */
export const getRuleDetail = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/config/detail`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则-编辑规则
 * @returns
 */
export const editRule = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/config/update`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警规则-编辑规则
 * @returns
 */
export const removeRule = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/config/delete`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警统计
 * @returns
 */
export const alarmStatic = (params) => {
  return request({
    url: `${baseServer}/tenant/device/alarm/monitor/statistics`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警趋势
 * @returns
 */
export const alarmtrend = (params) => {
  return request({
    url: `${baseServer}/tenant/device/alarm/monitor/lineChart`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警概览
 * @returns
 */
export const alarmoverview = (params) => {
  return request({
    url: `${baseServer}/tenant/device/alarm/monitor/overview`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警列表
 * @returns
 */
export const alarmTableList = (params) => {
  return request({
    url: `${baseServer}/tenant/device/alarm/monitor/eventQuery`,
    method: "get",
    params,
  });
};












/**
 * @desc 获取告警状态,等级
 * @returns
 */
export const getPullDownList = (params) => {
  return request({
    url: `${baseServer}/alarm/pullDownList`,
    method: "get",
    params,
  });
};

/**
 * @desc 处理告警
 * @returns
 */
export const postHandle = (data) => {
  return request({
    url: `${baseServer}/alarm/handle`,
    method: "post",
    data,
  });
};

/**
 * @desc 告警规则启动或停止
 * @returns
 */
export const getRunOrStop = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/runOrStop`,
    method: "get",
    params,
  });
};

/**
 * @desc 忽略告警
 * @returns
 */
export const getIgnore = (params) => {
  return request({
    url: `${baseServer}/alarm/ignore`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则列表
 * @returns
 */
export const getAlarmRuleList = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/list`,
    method: "get",
    params,
  });
};

/**
 * @desc 告警规则添加获取场景联动规则列表
 * @returns
 */
export const getSceneRuleList = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/sceneRuleList`,
    method: "get",
    params,
  });
};

/**
 * @desc 创建告警规则
 * @returns
 */
export const postAlarmRuleCreate = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/create`,
    method: "post",
    data,
  });
};

/**
 * @desc 修改告警规则
 * @returns
 */
export const postAlarmRuleUpdate = (data) => {
  return request({
    url: `${baseServer}/alarm/rule/update`,
    method: "put",
    data,
  });
};

/**
 * @desc 删除告警规则
 * @returns
 */
export const deleteAlarmRule = (params) => {
  return request({
    url: `${baseServer}/alarm/rule/delete`,
    method: "delete",
    params,
  });
};
