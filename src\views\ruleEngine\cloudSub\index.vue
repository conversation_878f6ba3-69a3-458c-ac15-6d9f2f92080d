<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:06:54
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-15 16:15:18
-->
<template>
	<div class="detail">
		<div class="detail-top">
			<el-tabs type="border-card">
				<el-tab-pane label="HTTP推送" name="0">
					<http-tab />
				</el-tab-pane>
				<el-tab-pane label="MQTT订阅" name="1">
					<mqtt-tab />
				</el-tab-pane>
			</el-tabs>
		</div>
	</div>
</template>

<script>
import HttpTab from './components/http'
import MqttTab from './components/mqtt'
export default {
	name: 'ProductDetail',
	components: {
		HttpTab,
		MqttTab,
	},
	data() {
		return {}
	},
	computed: {},
	created() {},
	methods: {},
	beforeDestroy() {},
}
</script>

<style lang="scss" scoped>
.detail {
	padding: 22px 22px 0px 22px;
	padding-bottom: 20px;
	.detail-top {
		margin-top: 20px;
	}
}

.el-tabs--border-card {
	box-shadow: none;
	border: none;
}
/deep/ {
	.el-tabs__item {
		height: 48px;
		line-height: 48px;
		color: rgba(51, 51, 51, 1);
	}
	.el-tabs--border-card > .el-tabs__header {
		background-color: #edf1f7;
		border-bottom: none;
	}
	.el-tabs--border-card > .el-tabs__content {
		padding: 0px;
	}
	.el-tabs__item {
		padding: 0px 35px;
		height: 42px;
		line-height: 42px;
	}
	.el-tabs--border-card > .el-tabs__header .el-tabs__item {
		color: rgba(51, 51, 51, 1);
		font-family: HarmonyOS Sans SC;
		font-style: normal;
		font-weight: normal;
		font-size: 16px;
		letter-spacing: 1px;
	}
	.el-tabs--top.el-tabs--border-card
		> .el-tabs__header
		.el-tabs__item:nth-child(2) {
		padding-left: 35px;
	}
	.el-tabs--top.el-tabs--border-card
		> .el-tabs__header
		.el-tabs__item:last-child {
		padding-right: 35px;
	}
	.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active::after {
		content: '';
		position: absolute;
		left: 0;
		top: 0;
		width: 100%;
		height: 3px;
		background-color: #515151;
		z-index: 1;
	}
	/deep/ .is-active {
		color: rgba(51, 51, 51, 1);
	}
}
</style>
