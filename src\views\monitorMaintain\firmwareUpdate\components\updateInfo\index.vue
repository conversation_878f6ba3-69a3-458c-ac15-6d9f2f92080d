<template>
  <div class="update-info">
    <div class="info-info">
      <div class="info-title flex">
        <div class="left">
          <p>固件信息</p>
        </div>
        <div class="right">
          <img src="~@/assets/images/index/edit.svg" />
          <span @click="handleEditfirmware" class="color2">编辑</span>
        </div>
      </div>
      <div class="info-detail">
        <div class="item-rows">
          <div class="item">
            <span>固件名称</span>
            <span>{{ firmwareJobDetail.name }}</span>
          </div>
          <div class="item">
            <span>固件版本号</span>
            <span>{{ firmwareJobDetail.version }}</span>
          </div>
        </div>
        <div class="item-rows">
          <div class="item">
            <span>所属产品</span>
            <span>{{ firmwareJobDetail.productName }}</span>
          </div>
          <div class="item">
            <span>签名算法</span>
            <span>{{ firmwareJobDetail.signMethod }}</span>
          </div>
        </div>
        <div class="item-rows">
          <div class="item">
            <span>添加时间</span>
            <span>{{ firmwareJobDetail.createTime }}</span>
          </div>
          <div class="item">
            <span>固件签名</span>
            <span>{{ firmwareJobDetail.firmwareSign }}</span>
          </div>
        </div>
        <div class="item-rows">
          <div class="item" style="width: 100%">
            <span>固件描述</span>
            <span>{{ firmwareJobDetail.description }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="info-census">
      <div class="census-title">
        <div class="title">
          <span>固件升级设备统计</span>
        </div>
        <div class="census-content flex">
          <div class="content-item">
            <div class="item-title flex">
              <p></p>
              <span>固件升级设备总数</span>
            </div>
            <div class="num">
              <span>{{ firmwareJobStatic.total || 0 }}</span>
            </div>
          </div>
          <div class="content-item">
            <div class="item-title flex">
              <p></p>
              <span>升级成功数</span>
            </div>
            <div class="num">
              <span>{{ firmwareJobStatic.success || 0 }}</span>
            </div>
          </div>
          <div class="content-item">
            <div class="item-title flex">
              <p></p>
              <span>正在升级数</span>
            </div>
            <div class="num">
              <span>{{ firmwareJobStatic.upgrading || 0 }}</span>
            </div>
          </div>
          <div class="content-item">
            <div class="item-title flex">
              <p></p>
              <span>升级失败数</span>
            </div>
            <div class="num">
              <span>{{ firmwareJobStatic.fail || 0 }}</span>
            </div>
          </div>
          <div class="content-item">
            <div class="item-title flex">
              <p></p>
              <span>升级取消数</span>
            </div>
            <div class="num">
              <span>{{ firmwareJobStatic.canceled || 0 }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="info-content">
      <div class="content-select flex">
        <div class="left">
          <div class="content-title">
            <span>任务管理</span>
          </div>
        </div>
        <div class="right">
          <form-search
            :isSelect="false"
            @search="handleSearch"
            @clear="fn_clear_search_info"
            :inputHolder="inputHolder"
          />
        </div>
      </div>
      <div class="content-table">
        <iot-table :columns="columns" :data="tableData" :loading="loading">
          <template slot="operation" slot-scope="{ row }">
            <div class="table-edit flex">
              <p class="color2" @click="handleViewDetail(row)">查看详情</p>
              <p class="table-line"></p>
              <p
                class="color2"
                @click="handleCancelTask(row)"
                v-if="row.jobStatus != '已取消' && row.jobStatus != '升级完毕'"
              >
                取消任务
              </p>
              <p v-else style="color: #bfbfbf">取消任务</p>
            </div>
          </template>
        </iot-table>
      </div>
      <div class="content-bottom" v-if="tableData.length > 0">
        <iot-pagination
          :pagination="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      @callbackSure="fn_sure"
      @close="fn_close"
    >
      <template #body>
        <iot-form>
          <template #default>
            <el-form
              class="info-form"
              ref="firmwareJobDetailForm"
              :label-position="'top'"
              :model="firmwareJobDetailForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <el-form-item label="固件名称" prop="name">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.name"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧），
                必须以中文、英文或数字开头，长度不超过32个字符
              </div>
              <el-form-item label="所属产品" prop="productName">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.productName"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="固件版本号" prop="version">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.version"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="签名算法" prop="signMethod">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.signMethod"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="添加时间" prop="createTime">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.createTime"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="固件签名" prop="firmwareSign">
                <el-input
                  type="text"
                  v-model="firmwareJobDetailForm.firmwareSign"
                  disabled
                ></el-input>
              </el-form-item>
              <el-form-item label="固件描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="firmwareJobDetailForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </el-form>
          </template>
        </iot-form>
      </template>
    </iot-dialog>
  </div>
</template>
<script>
import FormSearch from "@/components/form-search";
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";
import { reg_seven, reg_sixteen } from "@/util/util.js";
import { cloneDeep } from "lodash";

import {
  getFirmwareDetail,
  getFirmwareJobUpgradeStatistic,
  getFirmwareJobList,
  putFirmwareJobCancel,
  putFirmwareUpdate,
} from "@/api/firmwareUpdate";
export default {
  name: "updateInfo",
  components: {
    IotTable,
    IotPagination,
    IotDialog,
    IotForm,
    FormSearch,
  },
  data() {
    return {
      columns: [
        { label: "任务ID", prop: "id" },
        { label: "任务类型", prop: "jobType" },
        { label: "任务状态", prop: "jobStatus" },
        { label: "添加时间", prop: "createTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      tableData: [],
      loading: false,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 4,
      },
      visible: false,
      title: "编辑固件信息",
      dialogWidth: "742px",
      infoForm: {
        id: 0,
        name: "",
        description: "",
      },
      rules: {
        name: [
          {
            required: true,
            trigger: "blur",
            // message: '须选择所属项目',
            validator: this.checkName,
          },
        ],
        description: [
          {
            required: false,
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      nameTrue: true,
      descTrue: true,
      firmwareJobDetail: {},
      firmwareJobDetailForm: {},
      firmwareJobStatic: {},
      firmwareJobList: {},
      firmwareId: "",
      inputHolder: "输入任务ID",
      jobId: "",
    };
  },
  created() {
    this.firmwareId = this.$route.query.id;
  },
  mounted() {
    this.fn_getFirmwareDetail();
    this.getFirmwareJobUpgradeStatistic();
    this.fn_get_table_data();
  },
  methods: {
    fn_getFirmwareDetail() {
      getFirmwareDetail(this.firmwareId).then((res) => {
        if (res.code == 200) {
          //路由信息
          let data = {
            id: res.data.id,
            title: res.data.name,
          };
          this.$store.dispatch("setLayoutInfo", data);

          this.firmwareJobDetail = res.data;
          this.firmwareJobDetailForm = cloneDeep(res.data);
        }
      });
    },
    getFirmwareJobUpgradeStatistic() {
      getFirmwareJobUpgradeStatistic(this.firmwareId).then((res) => {
        if (res.code == 200) {
          this.firmwareJobStatic = res.data;
        }
      });
    },
    getFirmwareJobList() {
      // getFirmwareJobList(this.firmwareId).then((res) => {
      // 	if (res.code == 200) {
      // 		this.firmwareJobList = res.data
      // 	}
      // })
    },

    fn_get_table_data(params = { firmwareId: this.firmwareId }) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      console.log(others);
      getFirmwareJobList(others)
        .then((res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
            }, 300);
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;

            this.fn_clear_form();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },

    fn_clear_form() {
      this.infoForm = {
        name: "",
        description: "",
      };
    },
    handleEditfirmware() {
      this.visible = true;
    },
    // 查看详情
    handleViewDetail(row) {
      this.$router.push({
        path: "/firmwareUpdateDetail",
        query: {
          firmwareId: this.firmwareId,
          id: row.id,
        },
      });
    },
    // 取消任务
    handleCancelTask(row) {
      putFirmwareJobCancel(row.id).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
        } else {
          this.$newNotify.warning({
            message: res.message,
          });
        }
        this.fn_get_table_data();
      });
      console.log(row);
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.current = 1;
      this.pagination.size = val;
      this.fn_get_table_data({
        jobId: this.jobId,
        firmwareId: this.firmwareId,
        size: this.pagination.size,
        current: this.pagination.current,
      });
    },
    // 当前页
    handleCurrentChange(val) {
      console.log(this.pagination);
      this.pagination.current = val;
      this.fn_get_table_data({
        jobId: this.jobId,
        firmwareId: this.firmwareId,
        size: this.pagination.size,
        current: this.pagination.current,
      });
    },
    fn_notNull(val) {
      return val !== 0 && !val;
    },
    // 弹窗的确定按钮
    fn_sure() {
      this.$refs["firmwareJobDetailForm"].validate((valid) => {
        if (valid) {
          console.log("this.firmwareJobDetailForm", this.firmwareJobDetailForm);
          let infoForm = {
            id: this.firmwareId,
            name: this.firmwareJobDetailForm.name,
            description: this.firmwareJobDetailForm.description,
          };
          putFirmwareUpdate(infoForm).then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.pagination.current = 1;
              this.fn_get_table_data({
                firmwareId: this.firmwareId,
                size: this.pagination.size,
                current: 1,
              });
              this.visible = false;
              this.fn_getFirmwareDetail();
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
        }
      });
    },
    fn_close() {
      this.nameTrue = true;
      this.descTrue = true;
    },

    handleSearch(from) {
      this.jobId = from.value;
      this.fn_get_table_data({
        firmwareId: this.firmwareId,
        jobId: from.value,
      });
    },
    fn_clear_search_info() {
      this.jobId = "";
      this.fn_get_table_data({
        firmwareId: this.firmwareId,
      });
    },

    checkName(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入固件名称"));
      } else if (!reg_sixteen(value)) {
        return callback(
          new Error(
            "支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符"
          )
        );
      } else {
        callback();
      }
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
      if (name === "description") {
        this.descTrue = value;
      }
    },
    // 长度检验
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
  },
};
</script>
<style lang="scss" scoped>
@mixin fontStyle400 {
  font-size: 14px;
  font-weight: 400;
}
@mixin fontStyle500 {
  font-size: 16px;
  font-weight: 500;
}
@mixin BoxShadow {
  box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
}
$blue: #018aff;
$green: #00c250;
$purple: #8f01ff;
$red: #ff4d4f;
$yellow: #e6a23c;
.update-info {
  .info-info {
    margin-top: 18px;
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    padding: 20px 32px;
    .info-title {
      justify-content: space-between;
      @include fontStyle500;
      .right {
        img {
          margin-right: 6px;
        }
        span {
          cursor: pointer;
          font-size: 14px;
        }
      }
    }
    .info-detail {
      margin-top: 18px;
      // @include BoxShadow;
      // border-radius: 2px;
      // padding: 17px 0;
      .item-rows {
        margin: 0 auto;
        display: flex;
        flex: 1;
        .item {
          padding: 11px 0;
          width: 50%;
          display: inline-flex;
          span {
            height: 16px;
            line-height: 16px;
            @include fontStyle400;
            &:first-child {
              flex-shrink: 0;
              color: #999999;
              width: 80px;
              text-align: right;
            }
            &:last-child {
              color: #515151;
              margin-left: 48px;
            }
          }
          &:nth-child(1) {
            // margin-left: 6px;
            span {
              &:first-child {
                width: 60px;
              }
            }
          }
        }
      }
    }
  }
  .info-census {
    margin-top: 38px;
    .census-title {
      @include fontStyle500;
      background: #ffffff;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      padding: 20px 32px;
    }
    .census-content {
      margin-top: 18px;
      .content-item {
        flex: 1;
        .item-title {
          p {
            @include fontStyle400;
            height: 12px;
            margin: 4px 8px 0 0;
          }
          span {
            color: #515151;
            font-size: 14px;
          }
        }
        .num {
          margin: 14px 17px 0;
          span {
            font-size: 24px;
            font-weight: 500;
          }
        }
        &:nth-child(1) {
          .item-title {
            p {
              border: 2px solid $blue;
            }
          }
        }
        &:nth-child(2) {
          .item-title {
            p {
              border: 2px solid $green;
            }
          }
        }
        &:nth-child(3) {
          .item-title {
            p {
              border: 2px solid $purple;
            }
          }
        }
        &:nth-child(4) {
          .item-title {
            p {
              border: 2px solid $red;
            }
          }
        }
        &:nth-child(5) {
          .item-title {
            p {
              border: 2px solid $yellow;
            }
          }
        }
      }
    }
  }
  .info-content {
    margin-top: 32px;
    background: #ffffff;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    padding: 20px 32px;
    .content-title {
      justify-content: space-between;
    }
    .content-select {
      justify-content: space-between;
    }
    .content-table {
      margin-top: 14px;

      .table-edit {
        display: flex;
        align-items: center;
        p {
          cursor: pointer;
        }
        .table-line {
          margin: 0px 12px;
          width: 1px;
          height: 13px;
          border: 1px solid #ededed;
        }
      }
    }
    .content-bottom {
      margin-top: 14px;
      text-align: right;
    }
  }
  .info-form {
    /deep/ .el-form-item {
      margin-bottom: 17px;
    }
    .el-form-tips {
      margin-top: -17px;
    }
    .upload-text {
      width: 160px;
      background: #ebf6ff;
      color: #0088fe;
      font-size: 14px;
      font-weight: 400;
      padding: 11px 0;
      user-select: none;
    }
    .el-upload__tip {
      color: #999999;
      font-size: 12px;
    }
  }
}
</style>
