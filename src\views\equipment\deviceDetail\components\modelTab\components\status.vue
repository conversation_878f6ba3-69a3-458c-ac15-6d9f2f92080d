<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-25 11:23:44
-->
<template>
  <div class="model">
    <div class="model-table" v-show="typeList == 1">
      <!-- 表格 -->
      <iot-table :columns="columns" :data="newTableData">
        <!-- <template slot="classification" slot-scope="scope">
          <div class="flex">
            <span v-if="scope.row.classification == 0">属性</span>
            <span v-if="scope.row.classification == 1">事件</span>
            <span v-if="scope.row.classification == 2">服务</span>
          </div>
        </template>
        <template slot="type" slot-scope="scope">
          <div class="flex">
            <span v-if="scope.row.classification == 0">{{ scope.row.data.type }}</span>
            <span v-else>--</span>
          </div>
        </template>-->
        <template slot="newValue" slot-scope="scope">
          <el-tooltip
            class="item"
            effect="light"
            placement="top-start"
            popper-class="event-tooltip"
          >
            <!--  -->
            <template #content>
              <p :style="fn_formatter_style(scope.row)">
                {{ scope.row.value }}
              </p>
            </template>
            <div class="flex">
              <p class="newValue">{{ scope.row.value }}</p>
              <p v-copy="scope.row.value" class="newValue-copy color2">复制</p>
            </div>
          </el-tooltip>
        </template>
        <template slot="type" slot-scope="scope">
          <div class="flex">
            <span>{{ scope.row.data.type }}</span>
          </div>
        </template>
        <template slot="time" slot-scope="scope">
          <div class="flex">
            <span>{{ scope.row.time | dateFormate }}</span>
          </div>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_open(scope.row)" class="color2">
              查看数据
            </p>
          </div>
        </template>
      </iot-table>
    </div>

    <div class="model-card" v-show="typeList == 2">
      <!-- 卡片 -->
      <div class="card-list">
        <!-- :class="item == 2 ? 'active' : ''" -->

        <div class="card-item" v-for="item in newTableData" :key="item.id">
          <div class="item-top">
            <p>{{ item.name }}</p>
            <p @click="fn_open(item)">查看数据</p>
          </div>

          <div class="item-bottom">
            <p class="fix">
              {{ item.value ? item.value : "--" }}&nbsp;
              {{
                item.data.rules.unit && item.data.rules.unit != "无"
                  ? item.data.rules.unit
                  : ""
              }}
            </p>
            <p>{{ item.time | dateFormate }}</p>
          </div>
        </div>
      </div>

      <div class="table-empty" v-if="!newTableData.length">
        <img src="~@/assets/images/empty/empty.png" alt />
      </div>
    </div>
    <check-dialog
      ref="check"
      :deviceName="deviceName"
      :productKey="productKey"
    />
  </div>
</template>

<script>
import IotTable from "@/components/iot-table";
import checkDialog from "./check";
import { mapGetters } from "vuex";
import { fn_util__date_format } from "@/util/util.js";
export default {
  name: "Status",
  components: {
    IotTable,
    checkDialog,
  },
  props: {
    typeList: {
      type: String,
      default: "1",
    },
    statusTable: {
      type: Array,
      default: () => [],
    },
    productKey: {
      type: String,
    },
    deviceName: {
      type: String,
    },
  },
  computed: {
    ...mapGetters(["statusData"]),
    tableData() {
      let data = this.statusTable; //物模型数据
      // WebSocket
      if (
        this.statusTable.length &&
        this.statusData &&
        this.statusData.isSocket
      ) {
        data = this.tableDataCopy.map((item) => {
          if (
            this.statusData["identify"] == item.id &&
            this.deviceName == this.statusData["deviceName"]
          ) {
            return {
              ...item,
              ...this.statusData,
              time: this.statusData.deviceTime,
            };
          } else {
            return { ...item, value: item.value, time: item.time };
          }
        });
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.tableDataCopy = data;
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.newTableData = data;
        this.$store.commit("MUTATIONS_DEVICE__STATUSDATA", null);
        return data;
      }

      // http
      if (
        this.statusTable.length &&
        this.statusData &&
        !this.statusData.isSocket
      ) {
        data = this.statusTable.map((item) => {
          if (this.statusData[item.id]) {
            return { ...item, ...this.statusData[item.id] };
          } else {
            return { ...item, value: "-", time: "" };
          }
        });
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.tableDataCopy = data;
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.newTableData = data;
        return data;
      }
      console.log("触发");

      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.tableDataCopy = data;
      // eslint-disable-next-line vue/no-side-effects-in-computed-properties
      this.newTableData = data;
      // console.log(this.newTableData)
      return data;
    },
  },
  watch: {
    tableData() {},
  },
  mounted() {},
  data() {
    return {
      // tableData: [],
      newTableData: [],
      tableDataCopy: [],
      columns: [
        {
          label: "属性标识符",
          prop: "id",
        },
        { label: "属性名称", prop: "name" },
        { label: "数据类型", prop: "data", slotName: "type" },
        { label: "更新时间", prop: "time", slotName: "time" },
        {
          label: "最新值",
          prop: "value",
          width: "400",
          slotName: "newValue",
        },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      styleStr: "",
    };
  },
  filters: {
    dateFormate(val) {
      if (!val) return "- -";
      let value = val + "000";
      let { yy, MM, dd, hh, mm, ss } = fn_util__date_format(+value);
      return `${yy}-${MM}-${dd} ${hh}:${mm}:${ss}`;
    },
  },
  created() {},
  methods: {
    fn_search(data) {
      if (data.id == 2) {
        this.newTableData = this.tableDataCopy.filter((item) =>
          item.name.includes(data.value)
        );
      } else if (data.id == 1) {
        this.newTableData = this.tableDataCopy.filter((item) =>
          item.id.includes(data.value)
        );
      }
    },
    fn_clear() {
      this.newTableData = this.tableDataCopy;
    },
    fn_open(data) {
      // console.log(data)
      this.$refs.check.open(data.id);
    },
    fn_formatter_style(row) {
      if (row.value && row.value.length > 1006) {
        return "overflow: auto;height: 100%;max-height: 300px;";
      } else {
        return "";
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.model {
  .model-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin: 18px 0px 18px 0px;
    // .top-left {
    // }
    .top-right {
      align-items: center;
    }
  }
  .model-table {
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
    .newValue {
      width: 330px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      color: #515151;
      font-size: 14px;
    }
    .newValue-copy {
      cursor: pointer;
      margin-left: 8px;
    }
  }

  .model-card {
    // min-width: 1295px;
    overflow: hidden;
    padding-top: 2px;
    .card-list {
      display: flex;
      flex-wrap: wrap;
      // min-width: 1605px;
      width: calc(100% + 1.3%);
      .card-item {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 32%;
        height: 124px;
        background: #ffffff;
        border: 1px solid #e4e7ec;
        box-shadow: 0px 4px 8px 0px rgba(150, 150, 150, 0.19);
        padding: 28px;
        box-sizing: border-box;
        margin-right: 1.3%;
        margin-bottom: 18px;
        transition: all 0.3s;
        .item-top,
        .item-bottom {
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        .item-top {
          p:nth-child(1) {
            font-size: 14px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
            font-weight: 400;
            color: #515151;
          }
          p:nth-child(2) {
            font-size: 14px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
            font-weight: 400;
            color: #018aff;
            cursor: pointer;
          }
        }
        .item-bottom {
          p:nth-child(1) {
            font-size: 28px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Medium;
            font-weight: 500;
            color: #515151;
            width: 285px;
          }
          p:nth-child(2) {
            font-size: 14px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
            font-weight: 400;
            color: #999999;
          }
        }
      }
      .card-item:hover {
        border: 1px solid #0088fe;
        box-shadow: 0px 4px 8px 0px rgba(150, 150, 150, 0.19);
        margin-top: -2px;
        transition: all 0.3s;
      }
      // .card-item:nth-child(3n) {
      //   margin-right: 0px;
      // }
    }
  }
  .model-bottom {
    text-align: right;
    margin-top: 18px;
  }
  .del-tips {
    width: 420px;
  }
}

/deep/ {
  .cm-s-idea {
    height: 60vh;
  }
}

/deep/ {
  .el-radio-button__inner {
    width: 110px;
  }
  .el-input__inner {
    border-radius: 0;
    height: 34px;
  }
  .el-textarea__inner {
    border-radius: 0;
    height: 100px;
    color: #515151;

    font-family: H_Medium;
  }
  .el-textarea__inner::placeholder {
    font-family: H_Regular;
    font-weight: normal;
  }
}

/deep/ {
  .card-toggle {
    margin-left: 15px;
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background-color: #fff;
      color: #409eff;
    }
    .el-radio-button__orig-radio:checked + .el-radio-button__inner {
      background: #fff;
      color: #409eff;
    }

    .el-radio-button__inner {
      width: 34px;
      height: 34px;
      line-height: 34px;
      padding: 0px;
    }
  }
}

.table-empty {
  text-align: center;
  img {
    margin-top: 84px;
    margin-bottom: 28px;
  }
}
</style>
