<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-21 14:15:23
 * @LastEditors: hs
 * @LastEditTime: 2021-12-24 19:49:39
-->
<template>
  <div class="iot-input">
    <el-input
      :placeholder="placeholder"
      :value="value"
      :style="{ width: width }"
      @input="change"
    >
      <template slot="append">{{ unit }}</template>
    </el-input>
  </div>
</template>
<script>
export default {
  name: "Iot-input",
  props: {
    width: {
      type: String,
      // default: '240px'
    },
    placeholder: {
      type: String,
      default: "请输入...",
    },
    unit: {
      type: String,
      default: "",
    },
    value: {
      type: String,
      value: "",
    },
  },
  data() {
    return {};
  },
  methods: {
    change(val) {
      this.$emit("input", val);
    },
  },
};
</script>
<style lang="scss" scoped>
.iot-input {
  margin-right: 14px;
  margin-left: 14px;
  :deep(.el-input-group) {
    border: 1px solid #eeeff1;
    .el-input__inner {
      border: 1px solid #eeeff1;
      border: none;
    }
    .el-input-group__append {
      background: #ffffff;
      border: 1px solid #eeeff1;
      border: none;
      height: 34px;
      line-height: 34px;
      padding: 0 14px 0 0;
      font-size: 14px;
      font-weight: 400;
      color: #515151;
      border-radius: 0;
    }
  }
}
</style>
