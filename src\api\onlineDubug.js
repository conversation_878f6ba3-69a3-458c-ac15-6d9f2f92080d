/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-21 14:15:23
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2022-06-11 18:22:05
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const manage = BASE_SERVER;
const manage2 = BASE_SERVER;

/**
 * @desc 产品列表下拉信息
 * @params params{productKey}
 * @returns
 */
export const getProductSelect = (params) => {
    return request({
        url: `${manage}/tenant/product/ProductKeyDropDown`,
        method: "get",
        params,
    });
};

/**
 * @desc 设备列表下拉信息
 * @params params{productKey}
 * @returns
 */
export const getDeviceSelect = (params) => {
    return request({
        url: `${manage}/tenant/device/deviceDropDown`,
        method: "get",
        params,
    });
};

/**
 * @desc 刷新日志
 * @params params{productKey}
 * @returns
 */
export const getDebugList = (params) => {
    return request({
        url: `${manage2}/open/debug/list`,
        method: "get",
        params,
    });
};

/**
 * @desc 设备属性调试
 * @params params{productKey}
 * @returns
 */
export const getDeviceSet = (data) => {
    return request({
        url: `${manage2}/tenant/device/debug/set`,
        method: "post",
        data,
    });
};

/**
 * @desc 设备服务调用
 * @params params{productKey}
 * @returns
 */
export const getDeviceCall = (data) => {
    return request({
        url: `${manage2}/tenant/device/debug/call`,
        method: "post",
        data,
    });
};

/**
 * @desc 初始化设备状态
 * @params params{productKey}
 * @returns
 */
export const getDeviceiStatus = (params) => {
    return request({
        url: `${manage}/tenant/device/is/status`,
        method: "get",
        params,
    });
};
