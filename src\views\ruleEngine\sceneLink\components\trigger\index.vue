<template>
  <div>
    <p class="title">触发器{{ index + 1 }}</p>
    <div class="flex">
      <!-- 触发点 -->
      <el-form-item
        :prop="`${prop}.triggerMode`"
        :ref="`${prop}.triggerMode`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select v-model="attr.triggerMode" placeholder="请选择触发器类型">
          <el-option value="trigger" label="设备触发"></el-option>
        </el-select>
      </el-form-item>

      <template v-if="attr.triggerMode">
        <!-- 产品 -->
        <el-form-item
          :prop="`${prop}.productKey`"
          :ref="`${prop}.productKey`"
          :key="`${prop}.productKey`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
        >
          <el-select
            v-model="attr.productKey"
            placeholder="请选择产品"
            @change="productChange"
            filterable
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :value="item.productKey"
              :label="item.name"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item
          :prop="`${prop}.deviceName`"
          :ref="`${prop}.deviceName`"
          :key="`${prop}.deviceName`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
        >
          <el-select
            v-model="attr.deviceName"
            :disabled="!attr.productKey"
            placeholder="请选择设备"
            v-el-loadmore="getDeviceData"
            filterable
            clearable
            :filter-method="remoteDevice"
            @clear="remoteDevice('')"
          >
            <el-option
              v-if="deviceShowAll"
              value="_all"
              label="全部设备"
            ></el-option>
            <el-option
              v-for="item in deviceList"
              :key="`${item.id}+tigger`"
              :value="item.deviceName"
              :label="item.deviceName"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
      </template>
    </div>
    <div class="flex" v-if="attr.triggerMode">
      <!-- 触发方式 -->
      <el-form-item
        :prop="`${prop}.triggerShape`"
        :ref="`${prop}.triggerShape`"
        :key="`${prop}.triggerShape`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.triggerShape"
          :disabled="!attr.productKey"
          placeholder="请选择触发方式"
          @change="shapeChange"
        >
          <el-option value="property" label="属性触发"></el-option>
          <el-option value="event" label="事件触发"></el-option>
          <el-option value="deviceStatusChange" label="上下线触发"></el-option>
        </el-select>
      </el-form-item>

      <!-- 属性  默认 属性+事件     属性/事件/上下线 -->
      <el-form-item
        :prop="`${prop}.propertyName`"
        :ref="`${prop}.propertyName`"
        :key="`${prop}.propertyName`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.propertyName"
          :placeholder="proppertyPlaceholder"
          :disabled="!attr.productKey || !attr.triggerShape"
          @change="propChange"
        >
          <el-option
            v-if="attr.triggerShape != 'deviceStatusChange'"
            value="_all"
            :label="properptyLabel"
          ></el-option>
          <el-option
            v-for="item in propertyList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 具体事件 输出列表 -->
      <el-form-item
        v-if="
          attr.triggerShape == 'event' &&
          attr.propertyName &&
          attr.propertyName != '_all' &&
          attr.deviceName
        "
        :prop="`${prop}.eventCode`"
        :ref="`${prop}.eventCode`"
        :key="`${prop}.eventCode`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.eventCode"
          placeholder="请选择输出参数"
          @change="eventCodeChange"
        >
          <el-option
            v-for="item in eventList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 比较方式  根据属性变化获取下拉选项 -->
      <el-form-item
        v-if="
          (attr.triggerShape != 'deviceStatusChange' &&
            attr.triggerShape != 'event' &&
            attr.propertyName &&
            attr.propertyName != '_all') ||
          (attr.triggerShape == 'event' &&
            attr.deviceName &&
            attr.propertyName &&
            attr.propertyName != '_all')
        "
        :prop="`${prop}.compareType`"
        :ref="`${prop}.compareType`"
        :key="`${prop}.compareType`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select v-model="attr.compareType" placeholder="请选择比较模式">
          <el-option
            v-for="item in compareList"
            :key="item.symbol"
            :value="item.value"
            :label="item.symbol"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 具体值  设计为用户输入 -->
      <template
        v-if="
          attr.triggerShape != 'deviceStatusChange' &&
          attr.propertyName != '_all' &&
          attr.propertyName &&
          compareValueType != 'object'
        "
      >
        <el-form-item
          v-if="
            attr.triggerShape != 'event' ||
            (attr.triggerShape == 'event' && attr.deviceName)
          "
          :prop="`${prop}.compareValue`"
          :ref="`${prop}.compareValue`"
          :rules="[
            {
              validator:
                compareValueType != 'enum' &&
                compareValueType != 'bool' &&
                compareValueType != 'time'
                  ? compareValue
                  : selectValidate,
              trigger:
                compareValueType != 'enum' &&
                compareValueType != 'bool' &&
                compareValueType != 'time'
                  ? 'blur'
                  : 'change',
            },
          ]"
        >
          <el-input
            v-model="attr.compareValue"
            v-if="
              compareValueType != 'enum' &&
              compareValueType != 'bool' &&
              compareValueType != 'time'
            "
            :disabled="!attr.compareType"
            placeholder="请输入比较值"
          >
          </el-input>
          <el-date-picker
            v-else-if="compareValueType == 'time'"
            v-model="attr.compareValue"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="请选择时间"
          >
          </el-date-picker>
          <el-select
            v-else
            v-model="attr.compareValue"
            :disabled="!attr.compareType"
            placeholder="请选择比较值"
          >
            <el-option
              v-for="item in compareValueList"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
      <template
        v-if="
          attr.triggerShape != 'deviceStatusChange' &&
          attr.propertyName != '_all' &&
          attr.propertyName &&
          compareValueType == 'object'
        "
      >
        <div
          v-if="
            attr.triggerShape != 'event' ||
            (attr.triggerShape == 'event' && attr.deviceName)
          "
        >
          <custom-item
            v-for="(item, index) in attr.objectList"
            :key="`custom${item.id}`"
            :attr="item"
            :index="index"
            :prop="`${prop}.objectList.${index}`"
          ></custom-item>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
// import { getProductList } from "@/api/product";
import { productAbilityDetail } from "@/api/product";
import { getDeviceList } from "@/api/device";
import { compareData } from "@/util/util";
import {
  checkDefault,
  checkValue,
  arrayValidate,
  formatModel,
} from "../ruleEdit/rules.js";
import customItem from "../customItem";
export default {
  name: "",
  data() {
    return {
      // productList: [],
      deviceList: [],
      modelData: {}, // 模型数据
      propertyList: [], //属性设置下拉列表
      properptyLabel: "全部属性", // 属性默认项
      proppertyPlaceholder: "请选择属性", //属性项 提示语
      eventList: [], /// out 事件列表
      compareList: [], //比较模式数据
      propertyObject: {}, // 选择的属性  在  物模型中的定义   用于比较值的校验
      compareValueType: "",
      compareValueList: [], // 比较值 列表   （enum 或 bool 中使用）
      devicePageAll: 1,
      devicePage: 0, //设备分页页码
      deviceShowAll: true,
      productId: "",
    };
  },
  components: { customItem },
  props: {
    attr: {
      type: Object,
    },
    index: {
      type: [String, Number],
    },
    tenant_id: {
      type: String,
    },
    prop: {
      type: String,
    },
    productList: {
      type: Array,
    },
  },
  watch: {
    productList() {
      this.defaultSelectData();
    },
  },
  mounted() {},
  methods: {
    // 会显示
    defaultSelectData() {
      // 判断设备列表
      if (this.attr.productKey) {
        // 判断产品是否删除
        let product = this.productList.find(
          (item) => item.productKey == this.attr.productKey
        );
        if (!product) {
          // 如果产品不存在  清空后续
          this.reserItem([
            "productKey",
            "deviceName",
            "triggerShape",
            "propertyName",
            "eventCode",
            "compareType",
            "compareValue",
            "objectList",
            "compareValueType",
            "propMode",
          ]);
          this.$newNotify.warning({
            message: "触发器中所选产品不存在",
          });
          return;
        }
        let e = this.attr.productKey;
        this.productId = this.productList.filter(
          (item) => item.productKey == e
        )[0].id;
        // this.attr.deviceName    不传设备  无法查询设备是否被删除
        this.checkDevice(this.attr.deviceName);
        this.getDeviceData(); //产品id
        this.getModelData(e); // 产品key
      }
    },
    // 下拉选择框验证   输入框特殊处理
    selectValidate(rule, value, callback) {
      if (this.attr.triggerMode) {
        let key = rule.field.split(".")[2] || "";
        if (value == undefined || value === "") {
          callback(this.callbackTips(key, value));
        } else callback();
      } else {
        callback("请选择触发器类型");
      }
    },
    callbackTips(key) {
      let result = "";
      switch (key) {
        case "productKey":
          result = "请选择产品";
          break;
        case "deviceName":
          result = "请选择设备";
          break;
        case "triggerShape":
          result = "请选择触发方式";
          break;
        case "propertyName":
          result = this.proppertyPlaceholder;
          break;
        case "eventCode":
          result = "请选择输出参数";
          break;
        case "compareType":
          result = "请选择比较模式";
          break;
      }
      return result;
    },

    // 清空子项   deviceName  triggerShape propertyName eventCode compareType compareValue
    reserItem(list) {
      if (!Array.isArray(list) && list.length == 0) return;
      for (let i = 0; i < list.length; i++) {
        // 非表单项清空
        if (list[i] == "compareValueType") {
          this.compareValueType = "";
          continue;
        }
        if (list[i] == "objectList") {
          this.$emit("formSet", {
            key: `${this.prop}.objectList`,
            value: [],
          });
          continue;
        }
        if (list[i] == "propMode") {
          this.$emit("formSet", {
            key: `${this.prop}.propMode`,
            value: "",
          });
          continue;
        }
        if (list[i] == "deviceName") {
          this.devicePage = 0;
          this.devicePageAll = 1;
          this.deviceList = [];
        }
        if (list[i] == "propertyName") {
          this.eventList = [];
          this.propertyObject = {};
          this.compareList = [];
        }
        if (list[i] == "productKey") {
          this.productId = "";
        }
        if (
          this.attr[list[i]] != undefined &&
          this.$refs[`${this.prop}.${list[i]}`]
        ) {
          // 清空子项
          //  回显时 无法 使用form 的重置方法    因为回显时会吧form 的默认值修改
          // this.$refs[`${this.prop}.${list[i]}`].resetField();
          this.attr[list[i]] = "";
          this.$nextTick(() => {
            if (
              this.$refs[`${this.prop}.${list[i]}`] &&
              this.$refs[`${this.prop}.${list[i]}`].clearValidate
            ) {
              this.$refs[`${this.prop}.${list[i]}`].clearValidate(
                `${this.prop}.${list[i]}`
              );
            }
          });

          if (list[i] == "triggerShape") {
            this.propertyList = [];
            this.properptyLabel = "全部属性";
            this.proppertyPlaceholder = "请选择属性";
          }
        }
      }
    },
    // // 获取产品列表
    // getProductData() {
    //   getProductList().then((res) => {
    //     this.productList = res.data.records;
    //   });
    // },
    // 产品列表项变化
    productChange(e) {
      // 产品变化   需清空设备下拉列表 属性列表     并重新获取 产品物模型  产品设备列表
      this.reserItem([
        "deviceName",
        "triggerShape",
        "propertyName",
        "eventCode",
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.productId = this.productList.filter(
        (item) => item.productKey == e
      )[0].id;
      this.getDeviceData(); //产品id
      this.getModelData(e); // 产品key
    },
    // 设备搜索
    remoteDevice(val) {
      this.devicePage = 0;
      this.devicePageAll = 1;
      this.deviceList = [];
      console.log(val);
      if (typeof val != "string" || val == "") {
        this.deviceShowAll = true;
        this.getDeviceData();
      } else {
        this.deviceShowAll = false;

        this.getDeviceData(val);
      }
    },
    checkDevice(deviceName = "") {
      if (deviceName == "_all") return;
      getDeviceList({
        productId: this.productId,
        deviceName: deviceName != "_all" ? deviceName : "",
        current: this.devicePage,
        size: 10,
      }).then((res) => {
        if (res.code == 200) {
          let deviceInfo = res.data.records.find(
            (item) =>
              this.attr.deviceName == "_all" ||
              item.deviceName == this.attr.deviceName
          );
          if (!deviceInfo) {
            // 未找到   设备已被删除
            // 清空设备项    后续与设备项无关 无需清除
            this.reserItem(["deviceName"]);
            this.$newNotify.warning({
              message: "触发器中所选设备不存在",
            });
            return;
          }
        }
      });
    },
    // 获取设备列表
    getDeviceData(deviceName = "") {
      this.devicePage++;
      if (this.devicePage > this.devicePageAll) return;
      getDeviceList({
        productId: this.productId,
        deviceName: deviceName != "_all" ? deviceName : "",
        current: this.devicePage,
        size: 10,
      }).then((res) => {
        this.deviceList = this.deviceList.concat(res.data.records);
        this.devicePageAll = res.data.pages;
        this.$forceUpdate();
      });
    },
    // 获取产品物模型
    getModelData(productKey) {
      productAbilityDetail({
        // tenantId: this.tenant_id,
        productKey,
      }).then((res) => {
        if (res.code == 200 && typeof res.data == "string") {
          let model = JSON.parse(res.data);
          this.modelData = model;
          let propertyList = model.properties;
          let eventList = model.events.filter((item) => item.id !== "post");
          this.propertyList = propertyList.concat(eventList);
        } else {
          this.modelData = {
            properties: [],
            events: [],
            services: [],
          };
        }
        // 回显  ----------------------
        if (this.attr.triggerShape) {
          // 前置条件： 需要物模型数据
          // 选择的触发类型回显属性 / 事件 / 上下线   设置 propertyName 列表数据
          this.shapeFormat(this.attr.triggerShape);
          if (this.attr.propertyName) {
            // 处理后续之前 判断 属性是否存在于当前 属性列表
            // 具体属性 / 事件  / 上下线    根据选择的具体属性 回显 compareList 数据
            // 后续输入项规则 基于 propFormat 内的处理
            let property =
              this.propertyList.find(
                (item) => item.id == this.attr.propertyName
              ) || this.attr.propertyName == "_all";
            if (!property) {
              this.reserItem([
                "propertyName",
                "eventCode",
                "compareType",
                "compareValue",
                "objectList",
                "compareValueType",
                "propMode",
              ]);
              this.$newNotify.warning({
                message: "触发器中所选属性/事件不存在",
              });
              return;
            }
            this.propFormat(this.attr.propertyName);
          }
        }
      });
    },
    shapeChange(e) {
      // 需清空后续项
      this.reserItem([
        "propertyName",
        "eventCode",
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
      ]);
      this.shapeFormat(e);
    },
    shapeFormat(e) {
      if (e == "property") {
        // 属性
        this.propertyList = this.modelData.properties;
        this.properptyLabel = "全部属性";
        this.proppertyPlaceholder = "请选择属性";
      } else if (e == "event") {
        console.log(this.modelData);
        this.propertyList = this.modelData.events.filter(
          (item) => item.id !== "post"
        );
        this.properptyLabel = "全部事件";
        this.proppertyPlaceholder = "请选择事件";
      } else {
        this.properptyLabel = "";
        this.proppertyPlaceholder = "请选择属性";
        this.proppertyPlaceholder = "请选择上下线";
        this.propertyList = [
          { id: 0, name: "上下线" },
          { id: 1, name: "上线" },
          { id: 2, name: "下线" },
        ];
      }
    },
    // 属性下拉变化
    propChange(e) {
      // 需清空后续项
      this.reserItem([
        "eventCode",
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.propFormat(e);
    },
    propFormat(e) {
      if (e == "_all") {
        this.eventList = [];
        return;
      }
      if (e && this.attr.triggerShape == "property") {
        this.BasicPropChange(this.propertyList, e);
      } else if (e && this.attr.triggerShape == "event") {
        let result = this.propertyList.find((item) => item.id == e);
        this.eventList = result.out || [];
        // 回显   已选择事件 ------------------------
        if (this.attr.eventCode) {
          this.BasicPropChange(this.eventList, this.attr.eventCode);
        }
      }
    },
    // 事件  out 下参数变化
    eventCodeChange(e) {
      this.reserItem([
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.BasicPropChange(this.eventList, e);
    },
    // 属性变化   基本类型  (源数组，基准值)   处理比较类型数据及设置规则
    BasicPropChange(list, e) {
      if (e == "_all") return;
      let result = list.find((item) => item.id == e);
      this.propertyObject = result;
      this.compareValueType = result.data.type;
      this.$emit("formSet", {
        key: `${this.prop}.propMode`,
        value: this.compareValueType,
      });
      this.compareList = compareData.find(
        (item) => item.type == result.data.type
      ).condition;
      if (this.compareValueType == "bool" || this.compareValueType == "enum") {
        let list = [];
        for (let i in result.data.rules) {
          list.push({
            id: i,
            label: `${result.data.rules[i]} - ${i}`,
          });
        }
        this.compareValueList = list;
      } else if (this.compareValueType == "object") {
        // 对象
        let list = formatModel(result.data.rules);
        list = list.map((item) => {
          if (this.attr.objectList) {
            // 服务数据值回显
            let historyData = this.attr.objectList.find(
              (item2) => item2.id == item.id
            );
            if (historyData) {
              item.value = historyData.value;
            }
          }
          return item;
        });
        this.$emit("formSet", {
          key: `${this.prop}.objectList`,
          value: list,
        });
      }
      // 需要清空 compareType
      // 用form 表单的清空
    },
    // 输入值验证
    compareValue(rule, value, callback) {
      // this.propertyObject  存放当前输入项的规则
      if (value === "" || value == undefined) {
        callback("请输入比较值");
        return;
      }
      let { rules, type } = this.propertyObject.data;
      if (type != "array" && type != "object") {
        // 验证基本类型
        let result = checkDefault(type, value, rules, true);
        if (result.flag) {
          callback(result.text);
        } else {
          callback();
        }
      } else if (type == "object") {
        let flag = checkValue(type, value, rules);
        flag ? callback("对象格式输入有误") : callback();
      } else if (type == "array") {
        // 数组 或 对象
        arrayValidate(
          value,
          rules,
          callback,
          `输入格式有误，请输入array(${rules.item.type})格式`
        );
      }
      callback();
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #515151;
  font-size: 14px;
  padding-bottom: 8px;
}
:deep(.el-select) {
  margin-right: 14px;
}
:deep(.el-input__inner) {
  border-radius: 0;
  width: 224px;
}
:deep(.el-form-item) {
  height: 34px;
  margin-bottom: 26px;
  .el-form-item__content {
    line-height: 34px;
  }
}
:deep(.el-form-item__error) {
  white-space: nowrap;
}
</style>
