<template>
  <iot-dialog :visible.sync="visible"
              :title="title"
              width="718px"
              @callbackSure="fn_sure"
              @close="handleClose">
    <template #body>
      <!-- 编辑设备 -->
      <p>标签名称</p>

      <iot-form>
        <template #default>
          <el-form ref="produceForm"
                   class="produceForm"
                   :rules="rules"
                   :label-position="'top'"
                   @validate="fn_validate"
                   :model="item"
                   label-width="80px"
                   v-for="(item,index) in formData"
                   :key="`label${index}${Math.random * 20}`">
            <div class="label-item">
              <el-form-item prop="name">
                <el-tooltip class="item"
                            effect="light"
                            placement="top"
                            popper-class="register-tooltip">
                  <div slot="content"
                       class="tips-password">
                    <p class="flex">
                      <span :class="item.name.trim().length == 0 ? 'red':''">·不可为空 </span>
                    </p>
                    <p class="flex">
                      <span :class="item.checkName ? 'red':''"> ·不可超过32个字符,最多14位；</span>
                    </p>
                  </div>
                  <el-input :ref="`name${index}`"
                            v-model="item.name"
                            @change="changeIndex(index)"
                            placeholder="请输入标签名"></el-input>
                </el-tooltip>
              </el-form-item>
              <el-form-item prop="key">

                <el-tooltip class="item"
                            effect="light"
                            placement="top"
                            popper-class="register-tooltip">
                  <div slot="content"
                       class="tips-password">
                    <p class="flex">
                      <span :class="!item.key.length ? 'red':''">·不可为空 </span>
                    </p>
                    <p class="flex">
                      <span :class="item.checkKeyLength ? 'red':''"> ·不可超过32个字符,最多14位；</span>
                    </p>
                    <p class="flex">
                      <span> ·key支持英文字母、数字和特殊字符#@%&*-_/</span>
                    </p>
                    <p class="flex">
                      <span :class="item.checkExists ? 'red':''"> ·不可重复定义</span>
                    </p>
                  </div>
                  <el-input v-model="item.key"
                            @change="changeIndex(index)"
                            placeholder="请输入标签 Key"></el-input>
                </el-tooltip>

              </el-form-item>
              <el-form-item prop="value">

                <el-tooltip class="item"
                            effect="light"
                            placement="top"
                            popper-class="register-tooltip">
                  <div slot="content"
                       class="tips-password">
                    <p class="flex">
                      <span :class="!item.value.length ? 'red':''">·不可为空;</span>
                    </p>
                    <p class="flex">
                      <span :class="item.checkValueLength ? 'red':''"> ·不可超过128个字符,中文及日文算2个字符;</span>
                    </p>
                    <p class="flex tips">
                      <span style="width:345px"> ·支持中文、英文字母、数字和特殊字符下划线(_),中划线(-)、点号(.)、逗号(,)、井号（#）、特殊字符(@)、百分号(%)、反斜杠(/)、与号(&)和半角冒号(:) </span>
                    </p>
                  </div>
                  <el-input v-model="item.value"
                            @change="changeIndex(index)"
                            placeholder="请输入标签 value"></el-input>
                </el-tooltip>

              </el-form-item>
              <div class="struct-delete"
                   @click="labelDelete(index)">删除</div>
            </div>

          </el-form>
        </template>
      </iot-form>
      <div class="struct-add">
        <p @click="labelAdd">+ 新增参数</p>
      </div>

    </template>
  </iot-dialog>
</template>

<script>
import IotForm from '@/components/iot-form'
import IotDialog from '@/components/iot-dialog'
import {
  getDeviceTags,
  getDeviceTagsCheck,
  getDeviceTagsSave,
} from '@/api/device.js'
import { reg_one, reg_twentyOne, reg_twentyTwo } from '@/util/util.js'
export default {
  data() {
    return {
      visible: false,
      title: '修改标签',
      model: {}, //父页面数据
      formData: [
        {
          id: '',
          name: '',
          key: '',
          value: '',

          checkName: false,

          checkKeyLength: false,
          checkExists: false,

          checkValueLength: false,
        },
      ],
      rules: {
        name: [{ required: true, validator: this.checkName }],
        key: [{ required: true, validator: this.checkKey }],
        value: [{ required: true, validator: this.checkValue }],
      },
      checkIndex: 0,
    }
  },
  components: { IotForm, IotDialog },
  methods: {
    changeIndex(index) {
      this.checkIndex = index
    },
    checkName(rule, value, callback) {
      // debugger
      if (value !== '') {
        if (reg_one(value)) {
          this.formData[this.checkIndex].checkName = false
          callback()
        } else {
          this.formData[this.checkIndex].checkName = true
          callback(new Error(' '))
        }
      } else {
        this.formData[this.checkIndex].checkName = false
        callback(new Error(' '))
      }
    },
    checkKey(rule, value, callback) {
      if (value !== '') {
        if (reg_twentyOne(value)) {
          if (!this.getDeviceTagsCheck(value)) {
            this.formData[this.checkIndex].checkExists = false
            callback()
          } else {
            this.formData[this.checkIndex].checkExists = true
            callback(new Error(' '))
          }
          this.formData[this.checkIndex].checkKeyLength = false
        } else {
          this.formData[this.checkIndex].checkKeyLength = true
          callback(new Error(' '))
        }
      } else {
        this.formData[this.checkIndex].checkName = false
        callback(new Error(' '))
      }
    },
    checkValue(rule, value, callback) {
      if (value !== '') {
        if (reg_twentyTwo(value)) {
          this.formData[this.checkIndex].checkValueLength = false
          callback()
        } else {
          this.formData[this.checkIndex].checkValueLength = true
          callback(new Error(' '))
        }
      } else {
        this.formData[this.checkIndex].checkValueLength = false
        callback(new Error(' '))
      }
    },
    getDeviceTagsCheck(value) {
      let params = {
        deviceId: this.model.id,
        tagKey: value,
      }
      getDeviceTagsCheck(params).then((res) => {
        if (res.code == 200) {
          return res.data
        }
        return false
      })
    },
    // 表单验证触发
    fn_validate(name, value) {},
    fn_sure() {
      let flag = true
      let data = {}
      //清空标签
      if (
        (this.formData.length == 1) & (this.formData[0].name.length == 0) &&
        this.formData[0].key.length == 0 &&
        this.formData[0].value.length == 0
      ) {
        data = {
          deviceId: this.model.id,
          tags: [],
        }
      } else {
        for (let i = 0; i < this.$refs.produceForm.length; i++) {
          this.$refs.produceForm[i].validate((vaild) => {
            if (!vaild) {
              // 某一项校验失败
              flag = false
            }
          })
        }
        if (!flag) {
          return
        }
        console.log('flag', flag)
        let list = this.formData.map((item) => {
          return {
            id: item.id,
            tagName: item.name,
            tagKey: item.key,
            tagValue: item.value,
          }
        })

        data = {
          deviceId: this.model.id,
          tags: list,
        }
      }

      getDeviceTagsSave(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          })
          this.$emit('reload')
          this.visible = false
        } else {
          this.$newNotify.warning({
            message: res.message,
          })
        }
      })
    },
    labelAdd() {
      this.formData.push({
        id: '',
        name: '',
        key: '',
        value: '',
        checkName: false,

        checkKeyLength: false,
        checkExists: false,

        checkValue: false,
      })
    },
    labelDelete(index) {
      //   if (index === 0) return
      this.checkIndex = 0
      this.formData.splice(index, 1)
      if (!this.formData.length) {
        this.formData = [
          {
            id: '',
            name: '',
            key: '',
            value: '',
            checkName: false,

            checkKeyLength: false,
            checkExists: false,

            checkValue: false,
          },
        ]
      }
    },
    handleClose() {
      this.$refs.produceForm.resetFields()
    },
    open(record) {
      this.model = record
      //   this.produceForm = JSON.parse(JSON.stringify(this.model))
      getDeviceTags({ deviceId: this.model.id }).then((res) => {
        console.log(res)
        if (res.code == 200) {
          if (res.data.length) {
            this.formData = res.data.map((item) => {
              return {
                id: item.id,
                name: item.tagName,
                key: item.tagKey,
                value: item.tagValue,
                checkName: false,
                checkKeyLength: false,
                checkExists: false,
                checkValue: false,
              }
            })
          } else {
            this.formData = [
              {
                name: '',
                key: '',
                value: '',
                checkName: false,

                checkKeyLength: false,
                checkExists: false,

                checkValue: false,
              },
            ]
          }
        }

        console.log('this.formData', this.formData)
      })
      this.visible = true
    },
  },
}
</script>

<style lang="scss" scoped>
.label-item {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-bottom: 12px;
  .el-input {
    // width: 25%;
    width: 196px;
    margin-right: 10px;
  }
}
.el-form {
  .label-item {
    /deep/ {
      .el-form-item {
        margin-bottom: 0 !important;
      }
    }
  }
}
.struct-delete {
  flex-shrink: 0;
  padding-left: 12px;
  font-size: 14px;
  color: #018aff;
  cursor: pointer;
}
.struct-add {
  p {
    background: #ebf6ff;
    width: 160px;
    height: 34px;
    color: #0088fe;
    font-size: 14px;
    line-height: 34px;
    text-align: center;
    cursor: pointer;
    margin-top: 8px;
  }
}
/deep/ {
  .el-form-tips {
    margin-top: -16px !important;
  }
}
.red {
  color: #ff4d4f !important;
}
</style>


<style lang="scss">
.register-tooltip {
  border-radius: 0;
  border: 1px solid #e4e7ec !important;
  background: #ffffff !important;
  backdrop-filter: blur(4px);
  padding: 14px 18px;
  .popper__arrow {
    display: block !important;
    border-right-color: #e4e7ec !important;
    // left: -8px !important;
    border-width: 8px;
  }
  .popper__arrow::after {
    bottom: -8px !important;
    border-width: 8px;
  }

  .tips-password {
    p {
      align-items: center;
      padding-bottom: 8px;
      font-family: H_Medium;
      img {
        width: 14px;
      }
      span {
        padding-left: 8px;
        color: #515151;
        font-size: 13px;

        .tips {
        }
      }
    }
    p:last-child {
      padding-bottom: 0;
    }
  }
}
</style>