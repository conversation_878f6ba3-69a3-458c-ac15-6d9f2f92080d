//根据id
export const scrollToId = function (id) {
  document.querySelector(id).scrollIntoView({
    block: "start",
    behavior: "smooth",
  });
};
// 根据 ref
export const scrollToRef = function (ref, offset = 50, time = 10) {
  let timer = setInterval(() => {
    if (this.$refs[ref].scrollTop <= 0) {
      clearInterval(timer);
      timer = null;
    }
    this.$refs[ref].scrollTop -= offset;
  }, time);
};
