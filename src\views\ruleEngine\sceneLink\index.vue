<template>
  <div class="scene-link">
    <div class="link-top flex">
      <div class="top-left">
        <iot-button text="创建场景" @search="fn_open"></iot-button>
      </div>
      <div class="top-right">
        <el-input
          v-model="ruleName"
          @keyup.enter.native="fn_handle__query"
          clearable
          placeholder="输入规则名称"
          @clear="handleClear"
        >
          <i
            slot="suffix"
            class="el-input__icon el-icon-search"
            @click="fn_handle__query"
          ></i>
        </el-input>
      </div>
    </div>
    <div class="link-content">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="ruleState" slot-scope="{ row }">
          <div class="state-edit">
            <div class="status flex" v-if="row.ruleState == 0">
              <div class="yellow"></div>
              <div>未启动</div>
            </div>
            <div class="status flex" v-if="row.ruleState == 1">
              <div class="green"></div>
              <div>运行中</div>
            </div>
          </div>
        </template>
        <template slot="operation" slot-scope="{ row }">
          <div class="table-edit flex">
            <p class="color2" @click="fn_toDeviceDetail(row)">查看</p>
            <p class="table-line"></p>
            <p
              v-if="row.ruleState == 1"
              style="color: #ff4d4f"
              @click="fn_start_or_stop_rule(row)"
            >
              停止
            </p>
            <p
              v-if="row.ruleState == 0"
              class="color2"
              @click="fn_start_or_stop_rule(row)"
            >
              启动
            </p>
            <p v-if="row.ruleState == 1" class="table-line"></p>
            <p
              v-if="row.ruleState == 1"
              class="color2"
              v-throttle="500"
              @click="fn_trigger_rule(row)"
            >
              触发
            </p>
            <p class="table-line"></p>
            <p class="color2" @click="fn_toLogPage(row)">日志</p>
            <p class="table-line"></p>
            <p @click="fn_del(row.id)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="link-bottom" v-if="tableData.length">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      @callbackSure="fn_sure"
      @close="fn_close"
    >
      <template #body>
        <iot-form v-if="type == 1">
          <el-form
            class="link-form"
            ref="linkForm"
            :rules="rules"
            @validate="fn_validate"
            :model="linkForm"
            label-width="80px"
            :label-position="'top'"
          >
            <el-form-item label="规则名称" prop="name">
              <el-input v-model="linkForm.name"></el-input>
            </el-form-item>
            <div class="el-form-tips" v-if="nameTrue">
              支持中文、英文、数字、下划线（_）和短划线（-），
              长度限制为1~32个字符，中文算两个字符
            </div>
            <el-form-item label="规则描述" prop="description">
              <el-input
                :maxlength="200"
                type="textarea"
                v-model="linkForm.description"
              ></el-input>
            </el-form-item>
            <div class="el-form-tips" v-if="descTrue">最多不超过200个字符</div>
          </el-form>
        </iot-form>

        <div v-if="type == 2">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除规则后， 所有运行的规则都会被同步终止，
                    请确认是否删除该规则？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>
<script>
import IotButton from "@/components/iot-button";
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";
import { reg_fifteen, reg_seven } from "@/util/util";
import {
  getRuleSave,
  getRuleList,
  getRuleDelete,
  getRuleInfo,
  getRuleStart,
  getRuleStop,
  getRuleTrigger,
} from "@/api/sceneLink";
export default {
  name: "sceneLink",
  components: {
    IotButton,
    IotTable,
    IotPagination,
    IotDialog,
    IotForm,
  },
  data() {
    return {
      ruleName: "",
      columns: [
        { label: "规则Id", prop: "id" },
        { label: "规则名称", prop: "name" },
        { label: "运行状态", prop: "ruleState", slotName: "ruleState" },
        { label: "创建时间", prop: "createTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      tableData: [],
      loading: true,
      delIds: "",
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      visible: false,
      title: "创建场景",
      dialogWidth: "580px",
      rules: {
        name: [
          {
            required: true,
            trigger: "blur",
            message:
              "支持中文、英文、数字、下划线（_）和短划线（-），长度限制为1~32个字符，中文算两个字符",
          },
          { validator: this.nameValidate, trigger: "blur" },
        ],
        description: [
          {
            required: false,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      nameTrue: true,
      descTrue: true,
      type: 1,
      linkForm: {
        name: "",
        description: "",
      },
    };
  },
  created() {
    // this.fn_get_table_data();
  },
  // keepalive 生命周期      //组件激活时触发
  activated() {
    let data = {
      name: this.ruleName,
      current: this.pagination.current,
      size: this.pagination.size,
    };

    this.fn_get_table_data(data);
  },
  //  跳转非详情   清除 keep-alive 缓存数组中的缓存视图
  beforeRouteLeave(to, from, next) {
    if (to.path != "/ruleEdit") {
      // 取消缓存
      this.$clearKeepAlive(this, from.path);
    }
    next();
  },
  watch: {
    ruleName(val) {
      let { flag, newStr } = this.getLength(val, 32);
      if (flag) {
        this.ruleName = newStr;
      }
    },
    visible(val) {
      if (!val && this.type == 1) {
        this.$refs["linkForm"] && this.$refs["linkForm"].resetFields();
      }
    },
  },
  methods: {
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
    handleClear() {
      this.fn_get_table_data();
    },
    nameValidate(rule, value, callback) {
      if (value !== "") {
        if (!reg_fifteen(value)) {
          callback(
            "支持中文、英文、数字、下划线（_）和短划线（-），长度限制为1~32个字符，中文算两个字符"
          );
        } else {
          callback();
        }
        callback();
      } else {
        callback("须选择所属项目");
      }
    },
    // 获取表格数据
    fn_get_table_data(params = {}) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      others.descs = true;
      getRuleList(others)
        .then((res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
            }, 300);
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    fn_get_rule_info(id) {
      let params = {
        id: id,
      };
      getRuleInfo(params).then((res) => {
        if (res.code == 200) {
          let data = {
            id: res.data.id,
            title: res.data.name,
          };
          this.$store.dispatch("setLayoutInfo", data);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 打开创建场景弹窗
    fn_open() {
      this.visible = true;
      this.type = 1;
      this.title = "创建场景";
    },
    // 输入框icon查询
    fn_handle__query() {
      let params = {
        name: this.ruleName,
        current: 1,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    // 启动，停止规则
    fn_start_or_stop_rule(row) {
      let params = {
        id: row.id,
      };
      if (row.ruleState == 0) {
        params.ruleState = 1;
        getRuleStart(params).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.warning({
              message: res.message,
            });
          }
          this.fn_get_table_data();
        });
      } else {
        params.ruleState = 0;
        getRuleStop(params).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.warning({
              message: res.message,
            });
          }
          this.fn_get_table_data();
        });
      }
    },

    //触发
    fn_trigger_rule(row) {
      let params = {
        id: row.id,
      };
      if (row.ruleState == 1) {
        getRuleTrigger(params).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.fn_get_table_data();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        });
      }
    },
    // 查看规则
    fn_toDeviceDetail(row) {
      this.$router.push({
        path: "/ruleEdit",
        query: {
          id: row.id,
        },
      });
      this.fn_get_rule_info(row.id);
    },
    // 操作列日志跳转
    fn_toLogPage(row) {
      this.$router.push({
        path: "/logDetail",
        query: {
          id: row.id,
        },
      });
      this.fn_get_rule_info(row.id);
    },
    // 打开删除弹窗
    fn_del(id) {
      this.visible = true;
      this.type = 2;
      this.title = "删除规则";
      this.delIds = id;
    },
    // 当页长度
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.pagination.size = val;
      let params = {
        name: this.ruleName,
        size: this.pagination.size,
        current: 1,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.pagination.current = val;
      let params = {
        name: this.ruleName,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "description") {
        this.descTrue = value;
      }
      if (name === "name") {
        this.nameTrue = value;
      }
    },
    // 弹窗的确定按钮
    fn_sure() {
      if (this.type === 1) {
        this.$refs["linkForm"].validate((valid) => {
          if (valid) {
            getRuleSave(this.linkForm).then((res) => {
              if (res.code == 200) {
                this.$newNotify.success({
                  message: res.message,
                });
                this.pagination.current = 1;
                this.fn_get_table_data({
                  size: this.pagination.size,
                  current: 1,
                });
                this.visible = false;
              } else {
                this.$newNotify.error({
                  message: res.message,
                });
              }
            });
          }
        });
      } else if (this.type === 2) {
        let params = {
          ids: this.delIds,
        };
        getRuleDelete(params).then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.visible = false;
            this.fn_get_table_data({
              size: this.pagination.size,
              current: 1,
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        });
      }
    },
    fn_close() {
      this.nameTrue = true;
    },
    //限制文本框输入
    getLength(str, length) {
      let a = 0,
        flag,
        newStr = "",
        content = str;
      for (let i = 0; i < content.length; i++) {
        if (content[i].charCodeAt() > 0 && content[i].charCodeAt() < 255) {
          a += 1;
        } else {
          a += 2;
        }
        if (a <= length) {
          newStr += content[i].charAt();
        }
      }
      flag = a > length;
      return { flag, newStr }; //flag 是否超出 //newStr 新字符串
    },
  },
};
</script>
<style lang="scss" scoped>
.scene-link {
  padding: 22px 22px 0px 22px;
  .link-top {
    margin-top: 18px;
    justify-content: space-between;
    width: 100%;
    :deep(.el-input__inner) {
      border-radius: 0;
    }
    .top-right {
      :deep(.el-input__inner) {
        width: 240px;
      }
    }
  }
  .link-content {
    margin-top: 18px;
    .state-edit {
      .status {
        .green {
          background: #00c250;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 7px;
          margin-right: 6px;
        }
        .yellow {
          background: #e6a23c;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 7px;
          margin-right: 6px;
        }
      }
    }
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
  .link-bottom {
    text-align: right;
    margin-top: 14px;
  }
  .link-form {
    :deep(.el-form-item) {
      margin-bottom: 17px;
    }
    .el-form-tips {
      margin-top: -17px;
    }
    .upload-text {
      width: 160px;
      background: #ebf6ff;
      color: #0088fe;
      font-size: 14px;
      font-weight: 400;
      padding: 11px 0;
      user-select: none;
    }
    .el-upload__tip {
      color: #999999;
      font-size: 12px;
    }
  }
}
</style>
