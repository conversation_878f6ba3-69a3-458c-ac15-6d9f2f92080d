/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2023-04-21 14:39:43
 */
const path = require("path");
const TerserPlugin = require("terser-webpack-plugin"); // 引入插件
function resolve(dir) {
  return path.join(__dirname, dir);
}
// 打包情况报告
const BundleAnalyzerPlugin =
  require("webpack-bundle-analyzer").BundleAnalyzerPlugin;

// 获取当前git 分支名
const fs = require("fs");
let branchName;
try {
  const gitHEAD = fs.readFileSync(".git/HEAD", "utf-8").trim();
  const ref = gitHEAD.split(": ")[1];
  branchName = ref.split("/")[2]; //获取当前分支名
  process.env.VUE_APP_BRANCHNAME = branchName;
} catch (err) {
  //
}
let baseUrl = ""
console.log("nodeEnv",process.env.VUE_APP_BASE_URL);
if(process.env.NODE_ENV == "development"){
  // 开发环境
  baseUrl = process.env.VUE_APP_BASE_API
  console.log("开发环境");
}else if(process.env.NODE_ENV == "production"){
  // 生产环境
  baseUrl = process.env.VUE_APP_BASE_URL
  console.log("生产环境");
}else if(process.env.NODE_ENV == "test"){
  baseUrl = process.env.VUE_APP_BASE_URL 
  console.log("测试环境");
}else{
  throw "nodeEnv error"
}
console.log("baseUrl",baseUrl);
module.exports = {
  publicPath: "/",
  outputDir: process.env.outputDir,
  chainWebpack: (config) => {
    config.resolve.alias
      .set("@", resolve("src"))
      .set("@assets", resolve("src/assets"))
      .set("@style", resolve("src/style"))
      .set("@components", resolve("src/components"))
      .set("@views", resolve("src/views"))
      .set("@store", resolve("src/store"))
      .set("public", resolve("public"));

    
    // config.plugin('webpack-report').use(BundleAnalyzerPlugin, [
    //   {
    //     analyzerMode: 'static',
    //     openAnalyzer: false
    //   }
    // ])

    //忽略的打包文件
    config.externals({
      vue: 'Vue',
      'vue-router': 'VueRouter',
      vuex: 'Vuex',
      axios: 'axios',
      'element-ui': 'ELEMENT',
      echarts:'echarts'
    });
    // //最小化代码
    // config.optimization.minimize(true);
    // //分割代码
    // config.optimization.splitChunks({
    //   chunks: 'all',
    // });
  },
  devServer: {
    hot: false,
    disableHostCheck: true,
    port: 9527,
    overlay: {
      warnings: true,
      errors: true,
    },
    proxy: {
      "/api": {
        // target: "http://*************/api",

        // target: "http://**************:9999", //后端单体服务  开发地址*************:9999 生产地址 **************:9999 //上线环境9000
        // target: "http://*************:9999",
        // target: "https://iot.console.fj-yuchen.com/api",//线上环境
        target: baseUrl,
        // target: 'http://*************:9020',
        changeOrigin: true,
        pathRewrite: {
          "^/api": "/",
        },
      },
      // "/soc": {
      //   target: "http://*************:9020",
      //   changeOrigin: true,
      //   pathRewrite: {
      //     "^/soc": "/",
      //   },
      // },
    },
    headers: {
      "Access-Control-Allow-Origin": "*",
          },
  },
  pluginOptions: {
    pwa: {
      iconPaths: {
        favicon32: "favicon.ico",
        favicon16: "favicon.ico",
        appleTouchIcon: "favicon.ico",
        maskIcon: "favicon.ico",
        msTileImage: "favicon.ico",
      },
    },
  },
  configureWebpack: {
    optimization: {
      minimizer: [
        new TerserPlugin({
          terserOptions: {
            ecma: undefined,
            warnings: false,
            parse: {},
            compress: {
              drop_console: true,
              drop_debugger: true,
              pure_funcs: ["console.log", "debugger"], // 移除console,debugger
            },
          },
        }),
      ],
    },
  },
};
