/*
 * @Author: your name
 * @Date: 2021-11-03 13:40:36
 * @LastEditTime: 2022-01-15 14:58:08
 * @LastEditors: your name
 * @Description: 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 * @FilePath: \tenant-web\src\views\reset\conf.js
 */
import topBar from "@/components/topbar";
import stepItem1 from "./components/step1";
import stepItem2 from "./components/step2";
import stepItem3 from "./components/step3";
import confirm from "./components/confirm";
import toVW from "@/util/toVW.js";
import copyright from "@/components/copyright";

export default {
  data() {
    return {
      active: 0,
      stepList: [
        {
          title: "忘记密码",
          active: true,
          index: 0,
        },
        {
          title: "安全验证",
          active: false,
          index: 1,
        },
        {
          title: "修改密码",
          active: false,
          index: 2,
        },
      ],
      phone: "",
      captcha: "",
    };
  },
  components: { topBar, stepItem1, stepItem2, stepItem3, confirm, copyright },
  mounted() {
    // window.addEventListener("keydown", this.keyDown, true);
  },
  methods: {
    toVW,
    keyDown(event) {
      if (event.keyCode === 9) {
        event.preventDefault();
      }
    },
    handleLogin() {
      this.$router.replace({ path: "/login" });
    },
    handleNext(data) {
      if (data.step == 1) {
        this.phone = data.phone;
      } else if (data.step == 2) {
        // 验证code
        this.captcha = data.captcha;
      } else if (data.step == 3) {
        // 修改密码
        this.$refs.confirm.open();
        // 3 不需要切换active
        return;
      }
      this.active = data.step;
      this.$refs.carousel.setActiveItem(data.step);
    },
  },
  destroyed() {
    // window.removeEventListener("keydown", this.keyDown, true);
  },
};
