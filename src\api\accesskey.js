/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-30 10:33:43
 * @LastEditors: lb <EMAIL>
 * @LastEditTime: 2022-09-26 17:32:09
 */

import request from "./index";
import { BASE_SERVER, other } from "../conf/env";
// const baseServer = BASE_SERVER;
// const baseServer = "/core";
const baseServer = BASE_SERVER;

/**
 * @desc Accesskey列表
 * @params
 * @returns
 */
export const getAccessKeyList = (params) => {
    return request({
        url: `${other}/accessKey/list`,
        method: "get",
        params,
    });
};

/**
 * @desc 创建Accesskey
 * @params
 * @returns
 */
export const getAccessKeyAdd = (data) => {
    return request({
        url: `${other}/accessKey/add`,
        method: "post",
        data,
    });
};

/**
 * @desc 禁用Accesskey
 * @params
 * @returns
 */
export const getAccessKeyDisable = (params) => {
    return request({
        url: `${baseServer}/accessKey/disable`,
        method: "post",
        params,
    });
};

/**
 * @desc 启用Accesskey
 * @params
 * @returns
 */
export const getAccessKeyEnable = (params) => {
    return request({
        url: `${baseServer}/accessKey/enable`,
        method: "post",
        params,
    });
};

/**
 * @desc 删除Accesskey
 * @params
 * @returns
 */
export const getAccessKeyRemove = (params) => {
    return request({
        url: `${baseServer}/accessKey/remove`,
        method: "post",
        params,
    });
};
