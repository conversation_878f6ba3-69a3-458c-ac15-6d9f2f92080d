<template>
  <div class="alarm-config">
    <div class="config-top flex">
      <div class="top-left"></div>
      <div class="top-right flex">
        <el-select v-model="alarmOptions" @change="clear_optionsData($event)">
          <el-option
            v-for="item in searchOptonsList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <div>
          <el-input
            v-if="alarmOptions == 0 || alarmOptions == 1"
            class="alarm-input"
            :disabled="alarmOptions == 0"
            v-model="alarmRuleName"
            @keyup.enter.native="handleSearch"
            @clear="handleClear"
            clearable
            placeholder="输入关键词搜索"
          >
          </el-input>
          <el-select
            v-model="alarmLevel"
            placeholder="请选择告警级别"
            v-if="alarmOptions == 2"
          >
            <el-option
              v-for="item in alarmLevelList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="alarmClass"
            placeholder="请选择告警类型"
            v-if="alarmOptions == 3"
          >
            <el-option
              v-for="item in alarmClassList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-select
            v-model="alarmStatus"
            placeholder="请选择状态"
            v-if="alarmOptions == 4"
          >
            <el-option
              v-for="item in alarmStatusList"
              :key="item.value"
              :label="item.name"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </div>
        <iot-button
          text="查询"
          style="margin-left: 12px"
          @search="handleSearch"
        ></iot-button>
        <iot-button
          text="重置"
          type="white"
          style="margin: 0px 12px"
          @search="handleClear"
        ></iot-button>
        <iot-button text="新建规则" @search="fn_open"></iot-button>
      </div>
    </div>

    <div class="config-content">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="status" slot-scope="scope">
          <el-switch
            v-model="scope.row.ruleStatusBoolean"
            v-throttle="1000"
            active-color="#13ce66"
            inactive-color="#d9dae0"
            @change="changeStatus(scope.row)"
          >
          </el-switch>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p class="color2" @click="editRule(scope.row)">编辑规则</p>
            <p class="table-line"></p>
            <p class="color2" @click="deleRule(scope.row)">删除</p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="config-bottom" v-if="tableData.length != 0">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 弹出框 -->
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      @callbackSure="fn_sure"
      :maxHeight="'80vh'"
      :top="type==3?'23vh':'5vh'"
      @close="handleClose"
    >
      <template #body>
        <iot-form v-if="type == 1 || type == 2">
          <el-form
            class="configForm"
            ref="configForm"
            :label-position="'top'"
            :model="configForm"
            :rules="rules"
            @validate="fn_validate"
            label-width="80px"
          >
            <el-form-item label="规则名称" prop="ruleName">
              <el-input v-model="configForm.ruleName"></el-input>
            </el-form-item>
            <div class="el-form-tips" v-if="nameTrue">
              1~32个字符，支持中文、数字及特殊字符，必须以中文或英文字符开头
            </div>

            <el-form-item label="告警类型" prop="alarmClass">
              <el-select
                v-model="configForm.alarmClass"
                filterable
                placeholder="请选择告警类型"
              >
                <el-option
                  v-for="item in alarmClassList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="告警级别" prop="alarmLevel">
              <el-select
                v-model="configForm.alarmLevel"
                filterable
                placeholder="请选择告警级别"
              >
                <el-option
                  v-for="item in alarmLevelList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="告警来源" prop="alarmSource">
              <el-select
                v-model="configForm.alarmSource"
                filterable
                placeholder="请选择告警来源"
              >
                <el-option
                  v-for="item in alarmSourcelist"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                  :disabled="item.disabled"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="资源范围" v-if="configForm.alarmSource == 1">
              <div class="resourceScope">
                <el-form-item label="选择资源" prop="needResources">
                  <el-select
                    v-model="configForm.needResources"
                    filterable
                    placeholder="请选择资源范围"
                  >
                    <el-option
                      v-for="item in resourceScopeList"
                      :key="item.value"
                      :label="item.name"
                      :value="item.value"
                      :disabled="item.disabled"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="选择设备"
                  prop="device"
                  v-if="configForm.needResources == 1"
                >
                  <el-select
                    v-model="configForm.device"
                    filterable
                    clearable
                    placeholder="请选择"
                    @click.native="open_deviceList"
                  >
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="选择事件"
                  prop="eventOptions"
                  v-if="eventList.length"
                >
                  <el-checkbox-group v-model="configForm.eventOptions" class="sourse_box">
                      <span v-for="item in eventList" :key="item.eventIdentifier" class="sourse_option">
                        <el-checkbox
                        
                        :key="item.eventIdentifier"
                        :label="item"
                        
                        >{{ item.eventDeviceName }}
                        </el-checkbox>
                      </span>
                  </el-checkbox-group>
                </el-form-item>
              </div>
            </el-form-item>

            <el-form-item
              label="关联触发场景"
              prop="alarmSource"
              v-if="configForm.alarmSource == 2"
            >
              <el-select
                v-model="configForm.sceneId"
                filterable
                placeholder="请选择场景规则"
              >
              </el-select>
            </el-form-item>

            <el-form-item label="静默时间" prop="silenceTime">
              <el-select
                v-model="configForm.silenceTime"
                filterable
                placeholder="请选择静默时间"
              >
                <el-option
                  v-for="item in timeList"
                  :key="item.value"
                  :label="item.name"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>

            <el-form-item label="通知方式" prop="notificationWayList">
              <el-checkbox-group v-model="configForm.notificationWayList">
                <el-checkbox label="1" disabled>告警中心</el-checkbox>
                <el-checkbox label="2">钉钉推送</el-checkbox>
              </el-checkbox-group>
            </el-form-item>

            <el-form-item
              label="钉钉配置"
              prop="dingDing"
              v-if="configForm.notificationWayList.length == 2"
            >
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容规则描述"
                v-model="configForm.dingDing"
              >
              </el-input>
            </el-form-item>

            <el-form-item label="生效时间" prop="timeTaking">
              <el-date-picker
                v-model="configForm.timeTaking"
                type="datetimerange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                prefix-icon="riqi"
                class="custom-datepicker"
              >
              </el-date-picker>
            </el-form-item>

            <el-form-item label="规则描述" prop="ruleDesc">
              <el-input
                type="textarea"
                :rows="2"
                placeholder="请输入内容规则描述"
                v-model="configForm.ruleDesc"
              >
              </el-input>
            </el-form-item>
            <div class="el-form-tips" v-if="descTrue">最多不超过100个字符</div>
          </el-form>
        </iot-form>
        <div v-if="type==3">确认删除此规则？删除后无法还原！</div>
      </template>
    </iot-dialog>
    <relation
      ref="relation"
      @close="handleReset"
      :uuID="uuid"
      :ruleID="chenckNameId"
      @deviceNameWhole="getdeviceNameWhole"
      @geteventList="geteventListT"
      :rowProductKey="rowProductKey"
      :type="type"
      :isclear="isclear"
    />
  </div>
</template>
<script>
import IotTable from "@/components/iot-table";
import IotButton from "@/components/iot-button";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";
import IotPagination from "@/components/iot-pagination";
import relation from "./components/relation";
import { reg_seven } from "@/util/util.js";
import { reg_twentyfoure, isJson } from "@/util/util";
import {
  getRuleList,
  changeRuleStatus,
  getuuID,
  removeData,
  creatRule,
  checkRuleName,
  getRuleDetail,
  editRule,
  getEventList,
  removeRule,
  getrelatonBindListMain
} from "@/api/alarmCenter";
export default {
  name: "alarmConfig",
  components: {
    IotForm,
    IotDialog,
    IotTable,
    IotPagination,
    IotButton,
    relation,
  },
  watch: {
    "configForm.timeTaking"(newVal) {
      //处理时间选择器中获取到的时间
      if (Array.isArray(newVal)) {
        const [start, end] = newVal.map((time) => {
          const d = new Date(time);
          const year = d.getFullYear();
          const month = (d.getMonth() + 1).toString().padStart(2, "0");
          const day = d.getDate().toString().padStart(2, "0");
          const hours = d.getHours().toString().padStart(2, "0");
          const minutes = d.getMinutes().toString().padStart(2, "0");
          const seconds = d.getSeconds().toString().padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        });
        this.configForm.validBeginTime = start;
        this.configForm.validEndTime = end;
      }
    },
  },
  data() {
    return {
      isclear:false,
      inputHolder: "请选择告警级别",
      uuid: "",
      eventList: [],
      deviceCount: 0,
      rowProductKey:'',//当前行产品key
      deleID:'',
      rowEventOptions:[],
      columns: [
        { label: "规则名称", prop: "ruleName",width:180 },
        { label: "告警类型", prop: "alarmTypeName",width:120 },
        { label: "告警等级", prop: "alarmLevelName",width:120 },
        { label: "规则描述", prop: "ruleDesc",width:220 },
        { label: "触发场景", prop: "alarmSourceName" },
        { label: "告警方式", prop: "notificationWayName" },
        { label: "创建时间", prop: "createTime" },
        { label: "启用状态", prop: "ruleStatus", slotName: "status" },
        { label: "操作", prop: "", slotName: "operation" },
      ],
      configForm: {
        id:'',//规则id
        alarmLevel: "", //告警级别
        alarmClass: "", //告警类型
        ruleName: "", //规则名称
        alarmSource: "", //告警来源
        silenceTime: "", //静默时间
        notificationWayList: ["1"],
        ruleDesc: "", //规则描述
        sceneId: "", //场景规则
        needResources: "", //资源范围
        device: "", //选择设备
        dingDing: "", //钉钉配置
        timeTaking: [],
        validBeginTime: "", //生效开始时间
        validEndTime: "", //生效结束时间时间
        eventOptions: [], //选择事件
        eventTypeIdentifier: "",
        ruleStatus:'',
        isEdit:''
      },
      chenckNameId: "",//当前行规则ID
      tableData: [],
      loading: false,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      visible: false,
      alarmOptions: 0,
      levelList: [],
      levelListCopy: [], //覆盖elementui 搜索方法   解决出现非数组内的取值导致显示异常
      searchOptonsList: [
        { name: "全部", value: 0 },
        { name: "规则名称", value: 1 },
        { name: "告警等级", value: 2 },
        { name: "告警类型", value: 3 },
        { name: "状态", value: 4 },
      ],
      alarmLevel: "",
      alarmLevelList: [
        { name: "紧急", value: 1 },
        { name: "重要", value: 2 },
        { name: "次重要", value: 3 },
        { name: "提示", value: 4 },
      ],
      alarmClass: "",
      alarmClassList: [
        { name: "设备告警", value: 1 },
        { name: "业务告警", value: 2 },
        { name: "系统告警", value: 3 },
        { name: "其他告警", value: 4 },
      ],
      alarmStatus: "",
      alarmStatusList: [
        { name: "启用", value: 1 },
        { name: "禁用", value: 2 },
      ],
      alarmSourcelist: [
        { name: "主动事件上报", value: 1, disabled: false },
        { name: "场景规则关联", value: 2, disabled: true },
      ],
      timeList: [
        { name: "无", value: 0 },
        { name: "5分钟", value: 5 },
        { name: "10分钟", value: 10 },
        { name: "15分钟", value: 15 },
        { name: "30分钟", value: 30 },
        { name: "1小时", value: 60 },
        { name: "3小时", value: 180 },
        { name: "6小时", value: 360 },
        { name: "12小时", value: 720 },
        { name: "24小时", value: 1440 },
      ],
      resourceScopeList: [
        { name: "产品", value: 1, disabled: false },
        { name: "设备分组", value: 2, disabled: true },
      ],

      sceneRuleList: [],
      alarmRuleName: "",
      dialogWidth: "718px",
      title: "",
      type: 1,
      descTrue: true,
      nameTrue: true,
      delIds: "",
      isEdit: false, //是否为编辑
      count:'',
      rules: {
        ruleName: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        sceneId: [
          {
            required: true,
            trigger: "change",
            message: "请选择关联场景联动",
          },
        ],
        alarmClass: [
          {
            required: true,
            trigger: "change",
            message: "请选择告警类型",
          },
        ],
        alarmLevel: [
          {
            required: true,
            trigger: "change",
            message: "请选择告警级别",
          },
        ],
        alarmSource: [
          {
            required: true,
            trigger: "change",
            message: "请选择告警来源",
          },
        ],
        silenceTime: [
          {
            required: true,
            trigger: "change",
            message: "请选择静默时间",
          },
        ],
        notificationWayList: [
          {
            required: true,
            trigger: "change",
            message: "请选择通知方式",
          },
        ],
        needResources: [
          {
            required: true,
            trigger: "change",
            message: "请选择资源",
          },
        ],
        device: [
          {
            required: true,
            trigger: "change",
            // message: "请选择设备",checkDvice
            validator: this.checkDvice,
          },
        ],
        timeTaking: [
          {
            required: true,
            trigger: "blur",
            message: "请选择生效时间",
          },
        ],
        dingDing: [
          {
            required: true,
            trigger: "blur",
            // message: "请输入钉钉配置",
            validator: this.checkJson,
          },
        ],
        ruleDesc: [
          {
            required: true,
            trigger: "blur",
            // message: "请输入规则描述",
            validator: this.checkLength,
          },
        ],
        eventOptions: [
          {
            required: true,
            trigger: "blur",
            message: "请选择事件",
          },
        ],
      },
    };
  },
  mounted() {
    this.fn_get_table_data();
//     window.addEventListener("beforeunload", (event)=> {
//     this.handleClose()
// });
  },
  methods: {
    //点击编辑获取当前规则详情
    editRule(row) {
      this.uuid=''
      this.type=2;
      this.title = '编辑规则'
      this.visible = true;
      getRuleDetail({id:row.id}).then(res=>{
        console.log(res);
        const {data} = res
        this.configForm.eventTypeIdentifier=data.eventTypeIdentifier,
        this.rowProductKey = data.eventTypeIdentifier,
        this.chenckNameId = data.id
        this.configForm.alarmLevel=data.alarmLevel,
        this.configForm.alarmSource=data.alarmSource,
        this.configForm.alarmClass=data.alarmType,
        this.configForm.eventOptions=data.configSubVOList,
        this.configForm.dingDing=data.dingDingConfig,
        this.configForm.needResources=data.eventType,
        this.configForm.notificationWayList=data.notificationWayList,
        this.configForm.id=data.id
        this.configForm.ruleDesc=data.ruleDesc,
        this.configForm.ruleName=data.ruleName,
        this.configForm.ruleStatus=data.ruleStatus,
        this.configForm.silenceTime=data.silenceTime,
        this.configForm.validBeginTime=data.validBeginTime,
        this.configForm.validEndTime=data.validEndTime
        this.configForm.timeTaking = [
          new Date(data.validBeginTime),
          new Date(data.validEndTime)
        ];
        this.getEvent(data)
        this.getbindDevice(row,data.eventTypeIdentifier)

      })
      
    },
    //获取绑定列表信息
    getbindDevice(row,prod){
      let params = {
        productKey:prod,
        ruleId:row.id,
      }
      getrelatonBindListMain(params).then((res)=>{
        this.count = res.data.records.length
        const name = res.data.records.map((item) => {
          return item.deviceName;
        });
        if(res.data.records.length==0){
          this.eventList = []
        }
        if(name.length>3){
          this.configForm.device = name.slice(0,3).join("/") + "...--共选择" + res.data.records.length + "个设备";
          if(res.data.total>10){
            this.configForm.device = name.slice(0,3).join("/") + "...--共选择" + res.data.total + "个设备";
          }
        }else{
          this.configForm.device = name.slice(0,3).join("/") + "--共选择" + res.data.records.length + "个设备";
        }
      })
    },
    //点击编辑时获取事件接口
    getEvent(row) {
      this.rowEventOptions = row.configSubVOList
      let params = { productKey: this.configForm.eventTypeIdentifier };
      getEventList(params).then((res) => {
        if (res.code == 200) {
          this.eventList = res.data;
          // 清空已选中的事件选项
          this.configForm.eventOptions = [];
          // 遍历eventList数组
          for (let item of this.eventList) {
            // 检查是否有匹配的eventIdentifier
            let isMatched = row.configSubVOList.some(
              (configItem) => configItem.eventIdentifier === item.eventIdentifier
            );
            // 如果有匹配项，则设置为默认选中
            if (isMatched) {
              this.configForm.eventOptions.push(item);
            }
          }
        }
      });
},

    //点击关闭时还原数据
    handleClose() {
      if(this.type==1 ||this.type==2){
        removeData({ requestUuid: this.uuid }).then((res) => {});
        this.$refs.configForm.resetFields();
        this.nameTrue = true;
        this.descTrue = true;
        this.chenckNameId = '';
        this.isclear = true
        if(this.type==1){
          this.configForm.eventTypeIdentifier = '',
          this.rowProductKey = ''
        }
        }
    },
    //获取子组件传递的设备名称与数量
    getdeviceNameWhole(WholeName) {
      this.count = WholeName.deviceCount;
      this.deviceCount = WholeName.deviceCount;
      const name = WholeName.deviceNames.map((item) => {
          return item.deviceName;
      });
      if(WholeName.deviceCount==0){
        this.eventList = []
      }
      if(name.length>3){
        this.configForm.device = name.slice(0,3).join("/") + "...--共选择" + WholeName.deviceCount + "个设备";
      }else{
        this.configForm.device = name.slice(0,3).join("/") + "--共选择" + WholeName.deviceCount + "个设备";
      }
    },
    //子组件传递回选择所需事件数据
    geteventListT(eventList) {
      this.eventList = eventList.eventList;
      this.configForm.eventTypeIdentifier = eventList.productKey;
      console.log(this.eventList);

      // 清空已选中的事件选项
      this.configForm.eventOptions = [];
        // 遍历eventList数组
        for (let item of this.eventList) {
          // 检查是否有匹配的eventTypeIdentifier
          let isMatched = this.rowEventOptions.some(
            (configItem) => configItem.eventIdentifier === item.eventIdentifier
          );
          // 如果有匹配项，则设置为默认选中
          if (isMatched&&this.type==2) {
            this.configForm.eventOptions.push(item);
          }
        }
    },
    // 获取uuid
    getuuid() {
      getuuID().then((res) => {
        this.uuid = res.data;
      });
    },
    relationDevice() {
      this.$refs.relation.open();
    },
    fn_get_table_data(params) {
      //获取表格数据
      getRuleList(params).then((res) => {
        console.log(res);
        if (200 == res.code) {
          this.tableData = res.data.records;
          this.pagination.total = res.data.total;
        }
      });
    },
    //打开删除弹出框
    deleRule(row){
      this.type = 3
      this.title = '删除规则'
      this.visible = true
      this.deleID = row.id
    },

    fn_open() {
      //打开弹出框
      this.type = 1;
      this.title = '新建规则'
      this.visible = true;
      this.configForm.ruleDesc = ''
      this.configForm.eventTypeIdentifier = ''
      this.getuuid();
      this.chenckNameId = ''
      this.rowProductKey = ''
      this.configForm.dingDing = '',
      this.configForm.timeTaking = [],
      this.configForm.notificationWayList = ["1"],
      this.configForm.silenceTime = 0,
      this.configForm.needResources = '',
      this.configForm.device = '',
      this.eventList = [],
      this.configForm.eventOptions = []
    },

    //打开选择设备弹出框
    open_deviceList() {
      if(this.type==1){
        this.rowProductKey = this.configForm.eventTypeIdentifier
      }
      this.configForm.device = "";
      this.$refs.relation.open();
    },

    handleReset() {},

    changeStatus(row) {
      //改变状态
      let status = "";
      if (row.ruleStatusBoolean == true) {
        status = 1;
      } else {
        status = 2;
      }
      let data = {
        id: row.id,
        status: status,
      };
      changeRuleStatus(data).then((res) => {
        if(res.code==200){
          this.$bus.$emit("get_alarm_static")//触发兄弟组件方法
          this.$newNotify.success({
                message: res.message,
              });
        }else{
          this.$newNotify.error({
                message: res.message,
              });
        }
      });
    },

    // 表单验证触发
    fn_validate(name, value) {
      if (name === "ruleName") {
        this.nameTrue = value;
      }
      if (name === "ruleDesc") {
        this.descTrue = value;
      }
    },
    //查询
    handleSearch() {
      let params = {
        alarmLevel: this.alarmLevel,
        alarmType: this.alarmClass,
        ruleStatus: this.alarmStatus,
        ruleName: this.alarmRuleName,
      };
      this.fn_get_table_data(params);
    },
    //重置
    handleClear() {
      this.alarmLevel = "";
      this.alarmClass = "";
      this.alarmStatus = "";
      this.alarmRuleName = "";
      let params = {
        alarmLevel: "",
        alarmType: "",
        ruleStatus: "",
        ruleName: "",
      };
      this.fn_get_table_data(params);
    },

    //创建规则
    fn_creat() {
      if(this.eventList.length){
            creatRule({
            alarmLevel: this.configForm.alarmLevel,
            alarmSource: this.configForm.alarmSource,
            alarmType: this.configForm.alarmClass,
            configSubDTOList: this.configForm.eventOptions,
            dingDingConfig: this.configForm.dingDing,
            eventType: this.configForm.needResources,
            eventTypeIdentifier: this.configForm.eventTypeIdentifier,
            notificationWayList: this.configForm.notificationWayList,
            requestUuid: this.uuid,
            ruleDesc: this.configForm.ruleDesc,
            ruleName: this.configForm.ruleName,
            ruleStatus: "1",
            silenceTime: this.configForm.silenceTime,
            validBeginTime: this.configForm.validBeginTime,
            validEndTime: this.configForm.validEndTime,
          }).then((res) => {
            if (res.code==200) {
              this.fn_get_table_data();
              this.$refs.configForm.resetFields();
              this.visible = false;
              this.$newNotify.success({
                message: res.message,
              });
              this.$bus.$emit("get_alarm_static")//触发兄弟组件方法
              this.eventList = []
              this.configForm.eventOptions = [];
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
      }else {
              this.$newNotify.error({
                message: '该产品无主动事件上报，无法创建告警规则，请切换其他产品',
              });
            }
      
    },

    //编辑规则接口
    fn_edit(){
      if(this.eventList.length){
          editRule({
          id:this.chenckNameId,
          alarmLevel: this.configForm.alarmLevel,
          alarmSource: this.configForm.alarmSource,
          alarmType: this.configForm.alarmClass,
          configSubDTOList: this.configForm.eventOptions,
          dingDingConfig: this.configForm.dingDing,
          eventType: this.configForm.needResources,
          eventTypeIdentifier: this.configForm.eventTypeIdentifier,
          notificationWayList: this.configForm.notificationWayList,
          requestUuid: this.uuid,
          ruleDesc: this.configForm.ruleDesc,
          ruleName: this.configForm.ruleName,
          ruleStatus: this.configForm.ruleStatus,
          silenceTime: this.configForm.silenceTime,
          validBeginTime: this.configForm.validBeginTime,
          validEndTime: this.configForm.validEndTime,
        }).then((res)=>{
          console.log(res);
          if(res.code==200){
            this.fn_get_table_data();
            // this.$refs.configForm.resetFields();
            this.visible = false;
            this.configForm.eventTypeIdentifier = ''
            this.eventList = []
            this.configForm.eventOptions = [];
            this.$refs.configForm.resetFields();
            this.$newNotify.success({
              message: res.message,
            });
          }else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
      }else{
        this.$newNotify.error({
              message: '该产品无主动事件上报，无法创建告警规则，请切换其他产品',
            });
      }
    },

    //弹出框确定按钮
    fn_sure() {
      if (this.type === 1) {
        this.$refs["configForm"].validate((valid) => {
          if (valid) {
            this.fn_creat();
          }
        });
      }
      if (this.type === 2) {
        this.$refs["configForm"].validate((valid) => {
          if (valid) {
            this.fn_edit()
          }
        });
      }
      if(this.type ===3){
        removeRule({ids:this.deleID}).then((res)=>{
          if(res.code==200){
          this.$bus.$emit("get_alarm_static")//触发兄弟组件方法
          this.fn_get_table_data();
          // this.$refs.configForm.resetFields();
          this.visible = false;
          this.$newNotify.success({
            message: res.message,
          });
        }else {
          this.$newNotify.error({
            message: res.message,
          });
        }
        })
      }
    },

    checkName(rule, value, callback) {
      // 检验规则名称
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入告警规则名称"));
      } else if (!reg_twentyfoure(value)) {
        return callback(
          new Error(
            "1~32个字符，支持中文、数字及特殊字符，必须以中文或英文字符开头"
          )
        );
      } else {
        checkRuleName({ id: this.chenckNameId, name: value })
          .then((res) => {
            if (res.data) {
              return callback(new Error("规则名称不能与现有的规则名称相同"));
            } else {
              callback();
            }
          })
          .catch((error) => {
            // 处理错误
            console.error(error);
            return callback(new Error("检查规则名称发生错误"));
          });
      }
    },
    checkLength(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入规则描述"));
      } else if (!reg_seven(value, 101)) {
        return callback(new Error("最多不超过100个字符"));
      } else {
        callback();
      }
    },
    checkJson(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入钉钉配置"));
      } else if (!isJson(value)) {
        return callback(new Error("请输入json格式的钉钉配置"));
      } else {
        callback();
      }
    },
    checkDvice(rule, value, callback) {
      if (this.count == 0) {
        return callback(new Error("请选择设备"));
      } else {
        callback();
      }
    },

    fn_notNull(val) {
      return val !== 0 && !val;
    },

    clear_optionsData(val) {

      //清除查询关键字
      if (val == 1 || val == 0) {
        this.alarmLevel = "", 
        this.alarmClass = "", 
        this.alarmStatus = "";
      } else if (val == 2) {
        this.alarmRuleName = "",
          this.alarmClass = "",
          this.alarmStatus = "";
      } else if (val == 3) {
        this.alarmLevel = "",
          this.alarmRuleName = "",
          this.alarmStatus = "";
      } else if (val == 4) {
        this.alarmRuleName = "",
          this.alarmClass = "",
          this.alarmLevel = "";
      }
    },

    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.current = 1;
      this.pagination.size = val;
      let params = {
        ruleName: this.alarmRuleName,
        alarmLevel: this.alarmLevel,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;
      let params = {
        ruleName: this.alarmRuleName,
        alarmLevel: this.alarmLevel,
        current: this.pagination.current,
        size: this.pagination.size,
      };
      this.fn_get_table_data(params);
    },
  },
};
</script>
<style lang="scss" scoped>
.alarm-config {
  margin: 0px 10px 0px 10px;
  padding-bottom: 50px;
  :deep(.el-form-item) {
    margin-bottom: 24px;
  }
  .el-form-tips {
    // margin-top: -17px;
    margin-top: -22px;
    margin-bottom: 6px;
  }
  .config-top {
    margin-top: 18px;
    justify-content: space-between;
    .top-right {
      .el-input {
        width: 188px !important;
      }
      .el-range-editor {
        border-radius: 0;
        :deep(.el-input__inner) {
          padding: 0;
        }
        :deep(.el-input__icon) {
          height: auto;
        }
        :deep(.el-range-separator) {
          height: 32px;
        }
      }
      .el-select {
        width: 188px;
        margin-left: 14px;
        border-radius: 0;
        .el-select-dropdown__item {
          height: 38px;
          line-height: 38px;
        }
      }
      :deep(.el-input__inner) {
        border-radius: 0;
      }
      .alarm-input {
        width: 240px;
        margin-left: 14px;
      }
    }
  }
  .config-content {
    margin-top: 16px;
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }

    .table-status {
      .status {
        .red {
          background: #ff4d4f;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .green {
          background: #00c250;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
        .yellow {
          background: #ccc;
          width: 8px;
          height: 8px;
          border-radius: 4px;
          margin-top: 8px;
          margin-right: 6px;
        }
      }
    }
  }
  .config-bottom {
    margin-top: 16px;
    text-align: right;
    // padding: 14px 0;
  }
  .resourceScope {
    padding: 0px 14px;
    border: 1px dashed rgba(228, 231, 236, 1);
    background-color: rgba(1, 138, 255, 0.05);
    .sourse_box{
      width: 637px;display: flex;flex-wrap: wrap;
      .sourse_option{
        width: 200px;
        display: inline-block;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }
  }
  :deep(.custom-datepicker .el-input__icon) {
    /* 调整图标位置的样式 */
    // margin-right: 20px;
    margin-bottom: 9px;
  }
}
</style>
<style lang="scss">
.select-popper {
  border-radius: 0;
  border: 1px solid #e4e7ec !important;
  background: #ffffff !important;
  backdrop-filter: blur(4px);
  padding: 12px 18px;
  color: #515151;
  font-size: 14px;
  margin-left: 30px !important;
  .popper__arrow {
    border-right-color: #e4e7ec !important;
    left: -10px !important;
    border-width: 10px;
  }
  .popper__arrow::after {
    bottom: -10px !important;
    border-width: 10px;
  }
}
</style>
