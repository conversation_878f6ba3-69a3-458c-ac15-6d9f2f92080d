<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:51:08
 * @LastEditors: hs
 * @LastEditTime: 2021-11-22 11:52:45
-->
<template>
  <div class="model">
    <div class="model-top">
      <div class="model-top-count">子设备管理（{{ tableData.length }}）</div>
      <div class="model-top-search">
        <div class="top-left">
          <iot-button text="关联子设备" @search="relationDevice"></iot-button>
        </div>
        <div class="top-right">
          <!-- 搜索栏 -->
          <form-search
            defaultId="2"
            :options="selectOptions"
            @search="fn_search_table_data"
            @clear="fn_clear_search_info"
            :inputHolder="inputHolder"
            :selectHolder="selectHolder"
          />
        </div>
      </div>
    </div>

    <div class="model-table">
      <!-- 表格 -->
      <iot-table
        :columns="columns"
        :data="tableData"
        :loading="loading"
        @selection-change="fn_select_more_data"
        @selection-del="fn_del_more_data"
        @del-callbackSure="handleMultipleDelete"
      >
        <!-- <template slot="nodeType" slot-scope="{ row }">
          <span>{{ row.nodeType | nodeTypeText(that) }}</span>
        </template>-->
        <template slot="status" slot-scope="{ row }">
          <span :class="point(row.status)" />
          <span>{{ row.status | statusText }}</span>
        </template>
        <template slot="operation" slot-scope="scope">
          <div class="flex table-edit">
            <p slot="operation" @click="fn_open(scope.row, 1)" class="color2">
              查看
            </p>
            <p></p>
            <p @click="handleDialog(scope.row, 1)" class="color2">解除绑定</p>
            <p></p>
            <p @click="handleDialog(scope.row, 0)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>

    <div class="model-bottom" v-if="tableData.length">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <relation
      ref="relation"
      :hostProductKey="hostProductKey"
      :hostDeviceName="hostDeviceName"
      @close="handleReset"
    />
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      width="620px"
      @callbackSure="fn_sure"
    >
      <template #body>
        <div v-if="selectType">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">请确认是否解除关联该设备？</div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
        <div v-else>
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除该设备，设备删除后不可恢复，请确认是否删除该设备？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
import IotForm from "@/components/iot-form";
import FormSearch from "@/components/form-search";
import IotButton from "@/components/iot-button";
import IotDialog from "@/components/iot-dialog";
import relation from "./components/relation";
import { getDictNetList } from "@/api/dictionary";
import {
  getDeviceSubList,
  getDeviceSubRemove,
  getDeviceDelete,
} from "@/api/device";
export default {
  name: "SubDevice",
  components: {
    IotTable,
    IotPagination,
    IotForm,
    FormSearch,
    IotButton,
    IotDialog,
    relation,
  },
  inject: ["reload"],
  data() {
    return {
      that: this,
      loading: false,
      selectOptions: [
        { id: "1", name: "备注名称" },
        { id: "2", name: "DeviceName" },
      ],
      // pageSource: [],
      tableData: [],
      columns: [
        {
          type: "selection",
          selectionText: true,
          visible: false,
          title: "删除设备",
          text: "删除该设备，设备删除后不可恢复，请确认是否删除该设备？",
          isShowdelete: true,
        },
        {
          label: "DeviceName",
          prop: "deviceName",
          width: 250,
        },
        { label: "备注名称", prop: "aliasName", width: 250 },
        { label: "设备所属产品", prop: "productName", width: 250 },
        {
          label: "节点类型",
          prop: "nodeType",
          // slotName: "nodeType",
        },
        { label: "状态/启用状态", prop: "status", slotName: "status" },
        { label: "最后上报时间", prop: "lastOnLineTime" },
        { label: "操作", prop: "operation", slotName: "operation", width: 200 },
      ],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        // pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },

      inputHolder: "输入名称搜索",
      selectHolder: "请选择",
      dictnetList: [],
      deviceName: "",
      aliasName: "",
      deviceId: "", ///设备id

      visible: false,
      title: "",
      selectData: {}, //  解除绑定 / 删除点击的项
      selectType: null, // 解除绑定 1     删除 0
    };
  },
  props: {
    hostProductKey: {
      type: String,
    },
    hostDeviceName: {
      type: String,
    },
  },
  filters: {
    nodeTypeText(val, that) {
      return (
        that.dictnetList.find((item) => item.id === val) &&
        that.dictnetList.find((item) => item.id === val).dictValue
      );
    },
    statusText(val) {
      return ["未激活", "在线", "离线"][+val - 4];
    },
  },
  created() {
    this.deviceId = this.$route.query.id;
    this.fn_get_dictnet_list();
    this.fn_get_device_detail();
  },
  methods: {
    fn_select_more_data() {},
    fn_del_more_data(record) {
      this.delIds = record.join(",");
      this.columns[0].visible = true;
    },
    point(val) {
      return `status-points${val - 4}`;
    },
    // 节点类型
    fn_get_dictnet_list() {
      getDictNetList().then((res) => {
        if (res.code == 200) {
          this.dictnetList = res.data;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 子设备列表
    fn_get_device_detail() {
      let params = {
        deviceId: this.deviceId,
        aliasName: this.aliasName,
        current: this.pagination.current,
        deviceName: this.deviceName,
        size: this.pagination.size,
      };
      this.loading = true;
      getDeviceSubList(params)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    // 搜索
    fn_search_table_data(params) {
      let data = {};
      if (params.id === "1") {
        this.deviceName = "";
        this.aliasName = params.value;
      } else {
        this.deviceName = params.value;
        this.aliasName = "";
      }
      data.size = this.pagination.size;
      this.fn_get_device_detail();
    },
    // 清除输入搜索
    fn_clear_search_info() {
      this.deviceName = "";
      this.aliasName = "";
      this.fn_get_device_detail();
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.size = val;
      this.pagination.current = 1;
      this.fn_get_device_detail();
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;
      // this.pagination.size = 10;
      this.fn_get_device_detail();
    },
    relationDevice() {
      this.$refs.relation.open(this.deviceId);
    },
    handleReset() {
      this.pagination.current = 1;
      this.fn_get_device_detail();
    },
    fn_open(data, num) {
      // debugger
      // this.$router.push({
      //   path: "/device",
      // });
      this.$nextTick(() => {
        this.$router.push({
          path: "/deviceChildDetail",
          query: {
            id: data.id,
            // title: data.deviceName,
            // status: data.status,
            // key: data.nodeTypeKey,
            num: num,
          },
        });
      });

      // this.reload();
    },
    handleDialog(data, flag) {
      this.selectData = data;
      this.selectType = flag;
      this.title = flag ? "解除绑定" : "删除设备";
      this.visible = true;
    },
    // 解除绑定
    handleRelieve(data) {
      getDeviceSubRemove({
        hostProductKey: this.hostProductKey,
        hostDeviceName: this.hostDeviceName,
        deviceId: this.deviceId,
        subDeviceIds: data.id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.handleReset();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          this.visible = false;
        });
    },
    handleMultipleDelete() {
      let data = {
        id: this.delIds,
      };
      this.handleDelete(data);
    },
    // 删除
    handleDelete(data) {
      getDeviceDelete({
        ids: data.id,
      })
        .then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.handleReset();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          this.visible = false;
          this.columns[0].visible = false;
        });
    },
    fn_sure() {
      if (this.selectType) {
        // 解除绑定
        this.handleRelieve(this.selectData);
      } else {
        // 删除
        this.handleDelete(this.selectData);
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.model {
  .model-top {
    font-family: HarmonyOS Sans SC;
    .model-top-count {
      margin-top: 18px;
      p {
        display: inline-block;
        font-size: 14px;
        line-height: 16px;
        letter-spacing: 1px;
        font-weight: normal;
      }
      span {
        font-size: 18px;
        line-height: 20px;
        margin: 0 6px;
        font-weight: 500;
      }
    }
    .model-top-search {
      margin: 18px 0 18px 0;
      width: 100%;
      display: flex;
      justify-content: space-between;
    }
  }
  .model-table {
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      p:nth-child(2) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
      p:nth-child(4) {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
  .model-bottom {
    text-align: right;
    margin-top: 18px;
  }
  .del-tips {
    width: 420px;
  }
}

/deep/ {
  .cm-s-idea {
    height: 60vh;
  }
}

/deep/ {
  .el-radio-button__inner {
    width: 110px;
  }
  .el-input__inner {
    border-radius: 0;
    height: 34px;
  }
  .el-textarea__inner {
    border-radius: 0;
    height: 100px;
    color: #515151;

    font-family: H_Medium;
  }
  .el-textarea__inner::placeholder {
    font-family: H_Regular;
    font-weight: normal;
  }
}

.status-points1 {
  width: 8px;
  height: 8px;
  background: #00c250;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}
.status-points2 {
  width: 8px;
  height: 8px;
  background: #ff4d4f;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}
.status-points0 {
  width: 8px;
  height: 8px;
  background: #e6a23c;
  border-radius: 50%;
  display: inline-block;
  margin-right: 6px;
}
</style>
