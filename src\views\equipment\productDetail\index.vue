<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:06:54
 * @LastEditors: hs
 * @LastEditTime: 2021-12-24 17:13:51
-->
<template>
  <div class="detail">
    <!-- <div class="detail_desc">
      <div class="info-title">
        <div>产品信息</div>
        <div class="info-edit color2">
          <img src="~@/assets/images/index/edit.svg" />
          <span>编辑</span>
        </div>
      </div>
      <div class="desc_box">
        <div class="desc_left">
            <div class="desc_pic">
              <img src="~@/assets/images/product/default.png" alt="">
            </div>
        </div>
        <div class="desc_right">
          <div class="info-row flex">
            <div class="item">
              <span>ProductKey</span>
              <span>2nRRiqzou3j4zgAo</span>
            </div>
            <div class="item">
              <span>ProductSecret</span>
              <span>eHsJ5F2MAX5TUP5X</span>
            </div>
            <div class="item">
              <span>节点类型</span>
              <span>网关子设备</span>
            </div>
          </div>
          <div class="info-row flex">
            <div class="item">
              <span>产品分类</span>
              <span>智慧生活</span>
            </div>
            <div class="item">
              <span>数据协议</span>
              <span>物模型</span>
            </div>
            <div class="item">
              <span>联网方式</span>
              <span>自定义</span>
            </div>
          </div>
          <div class="info-row flex">
            <div class="item" style="flex: 0.5;">
              <span>建模事件</span>
              <span>2016-08-30 15:57：23</span>
            </div>
            <div class="item">
              <span>设备数</span>
              <span>10</span>
            </div>
          </div>
          <div class="info-row flex">
            <div class="item" style="flex: 0.5;">
              <span>产品描述</span>
              <span>或许很长的一段话...</span>
            </div>
          </div>
          <div class="info-row flex">
            <div class="item" style="flex: 0.5;">
              <span>产品标签</span>
              <span>无标签信息</span>
            </div>
          </div>
        </div>
      </div>

    </div> -->
    <div class="detail-top">
      <el-tabs v-model="activeName" type="border-card">
        <el-tab-pane name="0" label="产品信息"> </el-tab-pane>
        <el-tab-pane name="1" label="功能定义">
          <domain-definition
            :productTitle="title"
            :productKey="productKey"
            :tenant_id="tenant_id"
          />
        </el-tab-pane>
        <el-tab-pane label="Topic类" v-if="false">Topic类</el-tab-pane>
        <el-tab-pane label="服务端订阅" v-if="false">服务端订阅</el-tab-pane>
        <el-tab-pane label="设备开发" v-if="false">设备开发</el-tab-pane>
      </el-tabs>
      <product-info v-if="activeName == '0'" />
    </div>
  </div>
</template>

<script>
import ProductInfo from "./components/productInfo";
import DomainDefinition from "./components/domainDefinition";

import {
  getProductDetail,
} from "@/api/product.js";

// import { getProductDetail } from '@/api/product.js'
import { mapGetters } from "vuex";
export default {
  name: "ProductDetail",
  components: {
    ProductInfo,
    DomainDefinition,
  },
  data() {
    return {
      productKey: this.mapProductKey,
      tenant_id: "",
      title: this.mapTitle,
      activeName: "0",
      produceForm: {
        id: this.$route.query.id,
        name: "",
        productSecret: "", // 产品密码
        productKey: "", // 产品密钥
        description: "",
        createTime: "",
        aclWayId: "",
        productDisableStatus: false, // 是否禁止
        dynamicRegisterAllowed: false, // 动态注册
        autoRegisterAllowed: false, // 自动注册
        classifiedName: "", // 产品品类
        projectName: "", // 项目名称
        networkWayName: "", // 通信方式
        aclWayName: "", // 认证方式
        dataFormatName: "", // 数据协议
        deviceGatewayTypeId: "", // 节点类型
        networkWayId: "", // 联网方式ID
        hierarchyClassifiedName: "",
        deviceGatewayTypeName: "",
      },
    };
  },
  computed: {
    ...mapGetters(["layoutInfo"]),
    mapProductKey() {
      return this.layoutInfo.productKey;
    },
    mapTitle() {
      return this.layoutInfo.title;
    },
  },
  mounted() {
    this.productKey = this.mapProductKey;
  },
  watch: {
    mapProductKey(val) {
      console.log("key", val);
      this.productKey = val;
    },
    mapTitle(val) {
      this.title = val;
    },
  },
  created() {
    if (this.$route.params.num) {
      this.activeName = this.$route.params.num;
    }
    // this.http_get_detail()
    this.tenant_id = this.Encrypt.decryptoByAES(
      localStorage.getItem("tenant_id")
    );
    this.productKey = this.mapProductKey;
    this.title = this.mapTitle;
    this.fn_get_product_detail()
  },
  methods: {
    // http_get_detail() {
    //   let data = {
    //     // id: 1458046283196428289,
    //     productKey: 'mec08bDde4rL6GzQ'
    //   }
    //   getProductDetail(data).then(res => {
    //     console.log(res)
    //   })
    // }


    // 产品详细信息
    fn_get_product_detail() {
          let params = {
            id: this.$route.query.id,
          };
          getProductDetail(params).then((res) => {
            if (res.code == 200) {
              this.produceForm = res.data;
              // this.productDisableStatus =
              //   this.produceForm.productDisableStatus === 1 ? true : false
              // this.dynamicRegisterAllowed =
              //   this.produceForm.dynamicRegisterAllowed === 1 ? true : false
              // this.autoRegisterAllowed =
              //   this.produceForm.autoRegisterAllowed === 1 ? true : false
              let data = {
                id: res.data.id,
                title: res.data.name,
                productKey: res.data.productKey,
              };
              this.$store.dispatch("setLayoutInfo", data);
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
        },

  },
};
</script>

<style lang="scss" scoped>
.detail {
  padding: 0px 32px 0 32px;
  .detail_desc{
    width: 100%;
    height: 260px;
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 20px 32px;
    // background-color: aqua;
    .info-title {
    display: flex;
    justify-content: space-between;
    .info-edit {
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: H_Regular;
      img {
        width: 12px;
        height: 12px;
        margin-right: 6px;
      }
    }
  }
  .desc_box{
    display: flex;
    .desc_left{
    width: 132px;
    height: 168px;
    background-color: #E8E8E8;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
    .desc_pic{
      width: 62px;
      height: 53px;
      img{
        width: 100%;
      }
    }
  }
  .desc_right{
    width: 100%;
    height: 168px;
    .info-row {
      .item {
        flex: 1;
        padding-top: 20px;
        width: 50%;
        display: flex;
        height: 100%;
        font-size: 14px;
        font-weight: 400;
        span {
          height: 16px;
          line-height: 16px;
          display: block;
          &:first-child {
            color: #999999;
            width: 100px;
            text-align: right;
            flex-shrink: 0;
          }
          &:last-child {
            // width: 100%;
            flex: 1;
            color: #515151;
            margin-left: 48px;
          }
        }
        .item-span {
          padding-right: 22px;
          width: calc(100% - 100px - 22px);
          color: #515151;
          margin-left: 48px;
          word-wrap: break-word;
        }
      }
    }
  }
  }
  
  }
  .detail-top {
    margin-top: 20px;
  }
}

.el-tabs--border-card {
  box-shadow: none;
  border: none;
}
:deep(.el-tabs__item) {
  height: 48px;
  line-height: 48px;
  color: rgba(51, 51, 51, 1);
  padding: 0px 35px;
  height: 42px;
  line-height: 42px;
}
:deep(.el-tabs--border-card > .el-tabs__header) {
  background-color: #edf1f7;
  border-bottom: none;
}
:deep(.el-tabs--border-card > .el-tabs__content) {
  padding: 0px;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item) {
  color: rgba(51, 51, 51, 1);
  font-family: HarmonyOS Sans SC;
  font-style: normal;
  font-weight: normal;
  font-size: 16px;
  letter-spacing: 1px;
}
:deep(.el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:nth-child(2)) {
  padding-left: 35px;
}
:deep(.el-tabs--top.el-tabs--border-card > .el-tabs__header .el-tabs__item:last-child) {
  padding-right: 35px;
}
:deep(.el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active::after) {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 3px;
  background-color: #515151;
  z-index: 1;
}
:deep(.is-active) {
  color: rgba(51, 51, 51, 1);
}
</style>
