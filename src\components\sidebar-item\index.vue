<template>
  <div
    class="menu-parent"
    v-if="menu.children && menu.children.length > 0"
    :style="menuStyle(menu)"
  >
    <div class="menu-item menu-title flex" @click="openMenu">
      <p class="flex">
        <i :class="menu.meta.icon"></i>
        <span>{{ menu.meta.title }}</span>
        
      </p>
      <b
        class="menu-right"
        :style="{
          transform: menu.isOpen ? `none` : 'rotate(90deg)',
        }"
      ></b>
    </div>
    <sidebar-item
      v-for="(child, index) in menu.children"
      :menu="child"
      :index="index"
      :key="child.name"
      @open="emitOpen"
      @active="emitActive"
    />
  </div>
  <div class="menu" v-else>
    <div
      class="menu-item flex"
      :class="menu.isActive ? 'active' : ''"
      @click="routeLink(menu)"
      v-if="menu.meta.isShow"
    >
      <!-- <router-link :to="menu.path"><i></i> <span>{{ menu.meta.title }}</span></router-link> -->
      <p class="flex">
        <i :class="menu.meta.icon === 'gailan' ? menu.meta.icon : ''"></i>
        <span class="menu_title">{{ menu.meta.title }}<img src="../../assets/images/index/NEW.png" alt="" v-if="menu.meta.title=='告警中心'||menu.meta.title=='网关运维'"></span>
      </p>
    </div>
  </div>
</template>

<script>
export default {
  name: "sidebar-item",
  props: {
    menu: {
      type: Object,
      require: true,
    },
    index: {
      type: [String, Number],
    },
    // 是否展开
    isCollapse: {
      type: Boolean,
    },
  },
  computed: {
    menuStyle() {
      return function (menu) {
        // console.log(menu)
        let isShowArray = menu.children.filter((item) => {
          return item.meta.isShow;
        });
        let height = menu.isOpen
          ? (50 / 1920) * 100 + "vw"
          : ((isShowArray.length * 50 + 50) / 1920) * 100 + "vw";
        // console.log(height)
        return {
          height,
        };
      };
    },
  },
  methods: {
    routeLink(menu) {
      this.$router.push(menu.path);
      // 点击菜单时需要清空vuex的数据，以避免存在数据污染
      this.$store.dispatch("setLayoutInfo", {});
      this.$emit("active", menu.name);
    },
    emitActive(data) {
      this.$emit("active", data);
    },
    // 向父级传递当前项的下标
    openMenu() {
      this.$emit("open", [this.index]);
    },
    // 接收子级传递的下标 并组装自身下标  传回父级
    emitOpen(data) {
      this.$emit("open", [this.index, ...data]);
    },
  },
};
</script>

<style lang="scss" scoped>
@import '~@/style/font-1/iconfont.css';
.menu-right {
  font-family: "iconfont";
  transition: 0.5s;
  color: #666666;
  font-size: 1px;
  transform: rotate(90deg);
}
.menu-right::before {
  content: "\e65d";
}
.menu,
.menu-parent {
  transition: 0.5s;
  overflow: hidden;
  .menu-item {
    height: 50px;
    align-items: center;
    justify-content: space-between;
    padding: 0 18px 0 24px;
    cursor: pointer;
    transition: 0.5s;
    p {
      align-items: center;
    }
    i {
      width: 20px;
      font-style: normal;
      font-size: 20px;
      color: #515151;
      transition: all 0.3s;
    }
    span {
      padding-left: 18px;
      color: #333333;
      font-size: 18px;
      line-height: 21px;
      font-family: H_Medium;
      // letter-spacing: 1px;
      // font-weight: 700;
    }
    .menu_title{
      position: relative;
      img{
        width: 20px;
        height: 8px;
        position: absolute;
        right: -23px;
      }
    }
  }
  .active {
    background: rgba(1, 138, 255, 0.08);
    position: relative;
    i {
      transform: scale(1.1);
      color: #018aff;
    }
    span {
      color: #018aff;
    }
    b {
      color: #018aff;
    }
  }
  .active::before {
    content: "";
    width: 2px;
    height: 100%;
    background: #018aff;
    box-shadow: 0px 0px 10px #018aff;
    position: absolute;
    right: 0;
    top: 0;
  }
}
.menu {
  .menu-item:hover {
    i {
      transform: scale(1.1);
      color: #018aff;
    }
    span {
      color: #018aff;
    }
  }
}
.menu-parent {
  .menu-title:hover {
    i {
      transform: scale(1.1);
      color: #018aff;
    }
    span {
      color: #018aff;
    }
    b {
      color: #018aff;
    }
  }
}
</style>
