<template>
  <div class="firmware-update">
    <div class="firmware-top flex">
      <div class="top-left">
        <iot-button text="添加固件" @search="fn_open"></iot-button>
      </div>
      <div class="top-right">
        <form-search
          :options="productOptionsCopy"
          @search="handleSearch"
          @clear="fn_clear_search_info"
          :inputHolder="inputHolder"
        />
      </div>
    </div>
    <!-- 表格内容 -->
    <div class="firm-content">
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="operation" slot-scope="{ row }">
          <div class="table-edit flex">
            <p @click="fn_toUpdate(row)" class="color2">固件升级</p>
            <p class="table-line"></p>
            <p class="color2" @click="fn_toDeviceDetail(row)">查看</p>
            <p class="table-line"></p>
            <p @click="fn_del(row.id)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>
    <div class="firm-bottom" v-if="tableData.length">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      :showLoading="true"
      @callbackSure="fn_sure"
    >
      <template #body>
        <iot-form v-if="type == 1">
          <template #default>
            <el-form
              class="firmwareForm"
              ref="firmwareForm"
              :label-position="'top'"
              :model="firmwareForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <el-form-item label="选择产品" prop="productKey">
                <el-select
                  v-model="firmwareForm.productKey"
                  filterable
                  placeholder="请选择产品"
                >
                  <template slot="empty">
                    <div class="empty-project">
                      <span>产品列表里没有匹配的数据，请先去创建产品</span>
                    </div>
                  </template>
                  <el-option
                    v-for="item in productOptions"
                    :key="item.id"
                    :label="item.name"
                    :value="item.productKey"
                  ></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="固件名称" prop="name">
                <el-input type="text" v-model="firmwareForm.name"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧），
                必须以中文、英文或数字开头，长度不超过32个字符
              </div>
              <el-form-item label="固件版本号" prop="version">
                <el-input type="text" v-model="firmwareForm.version"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="versionTrue">
                仅支持英文字母、数字、点、中划线和下划线，长度限制1~32个字符
              </div>
              <el-form-item label="上传文件" prop="fileName">
                <iot-upload
                  ref="iotUpload"
                  :isSlice="true"
                  :mergeUrl="completeAction"
                  :errorMsg="uploadErrorMsg"
                  :beforeUpload="fn_upload__before"
                  :success="fn_upload__success"
                  :error="fn_upload__error"
                  :delete="fn_upload__remove"
                  :partUrl="action"
                />
              </el-form-item>

              <el-form-item label="固件描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="firmwareForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </el-form>
          </template>
        </iot-form>
        <!-- 删除 -->
        <div v-if="type == 2">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除固件后，
                    属于该固件的批量升级任务及设备升级任务数据将被同步删除，
                    请确认是否删除该固件？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
        <div v-if="type == 3">
          <iot-form>
            <template #default>
              <el-form
                class="taskForm"
                ref="taskForm"
                :label-position="'top'"
                :model="taskForm"
                :rules="taskRules"
                @validate="fn_validate"
                label-width="80px"
              >
                <el-form-item label="所属产品">
                  <el-input
                    disabled
                    type="text"
                    v-model="taskFormProduct"
                  ></el-input>
                </el-form-item>
                <el-form-item label="固件名称">
                  <el-input
                    disabled
                    type="text"
                    v-model="taskFormName"
                  ></el-input>
                </el-form-item>
                <el-form-item label="固件版本号">
                  <el-input
                    disabled
                    type="text"
                    v-model="taskFormVersion"
                  ></el-input>
                </el-form-item>
                <el-form-item label="升级方式" prop="upgradeWay">
                  <el-select
                    v-model="taskForm.upgradeWay"
                    filterable
                    placeholder="请选择升级方式"
                    @change="fn_upgradeWay"
                  >
                    <el-option
                      v-for="item in jobUpgradeWayList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="升级范围" prop="upgradeScope">
                  <el-select
                    v-model="taskForm.upgradeScope"
                    filterable
                    placeholder="请选择升级范围"
                    @change="fn_upgradeScope"
                  >
                    <el-option
                      v-for="item in jobUpgradeScopeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="升级版本"
                  prop="upgradeVersion"
                  v-if="!showSpecifyDevice"
                >
                  <el-select
                    v-model="taskForm.upgradeVersion"
                    filterable
                    placeholder="请选择升级版本"
                  >
                    <el-option
                      v-for="item in upgradeVersionList"
                      :key="item"
                      :label="item"
                      :value="item"
                    ></el-option>
                  </el-select>
                </el-form-item>

                <el-form-item
                  label="设备范围"
                  prop="upgradeDevice"
                  v-if="showSpecifyDevice"
                >
                  <el-input
                    v-model="selectDevice"
                    placeholder="请选择设备范围"
                    :readonly="true"
                    @focus="fn_showDeviceList"
                  >
                  </el-input>
                </el-form-item>

                <!-- showSpecifyDevice -->
                <el-form-item label="升级描述" prop="description">
                  <el-input
                    :maxlength="200"
                    type="textarea"
                    v-model="taskForm.description"
                  ></el-input>
                </el-form-item>
                <div class="el-form-tips" v-if="descTrue">
                  最多不超过200个字符
                </div>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
    <iot-dialog
      :visible.sync="deviceVisible"
      :title="'请选择设备'"
      :width="'732px'"
      @callbackSure="fn_device_sure"
    >
      <template #body>
        <div class="device-content">
          <div class="device-top flex">
            <div class="left">
              <span></span>
              <span>已选中设备{{ upgradeDeviceNameCount }}个</span>
            </div>
            <div class="right">
              <form-search
                :options="versionList"
                @search="handleDeviceSearch"
                @clear="fn_clear_search_device"
                :inputHolder="inputDeviceHolder"
                :selectHolder="selectDeviceHolder"
              />
            </div>
          </div>
          <div class="device-table">
            <iot-table
              ref="iotTable"
              :columns="deviceColumns"
              :data="deviceData"
              @selection-change="fn_select_more_data"
              :loading="deviceLoading"
            ></iot-table>
          </div>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>
<script>
import FormSearch from "@/components/form-search";
import IotButton from "@/components/iot-button";
import IotTable from "@/components/iot-table";
import IotUpload from "@/components/iot-upload";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotForm from "@/components/iot-form";
import { reg_seven } from "@/util/util.js";
import { baseUrl } from "@/conf/env";
import Encrypt from "@/util/aes";
import { fn_util__filter_null } from "@/util/util";
import { reg_sixteen, reg_seventeen } from "@/util/util.js";
import { getProductSelect } from "@/api/product";

import {
  getFirmwareList,
  postFirmwareAdd,
  // postFirmwareUpload,
  deleteFirmware,
  getPullDownList,
  getVersionByProduct,
  postFirmwareJobAdd,
  getDeviceByProduct,
  partUpload,
  partMerge,
} from "@/api/firmwareUpdate";

export default {
  name: "firmWareUpdate",
  components: {
    FormSearch,
    IotButton,
    IotTable,
    IotPagination,
    IotDialog,
    IotForm,
    IotUpload,
  },
  data() {
    return {
      // action: `${baseUrl}/device-ota/ota/firmware/upload`,
      action: partUpload,
      // completeAction: `${baseUrl}/device-ota/ota/firmware/multipart/complete`,
      completeAction: partMerge,

      productKey: "",
      productOptionsCopy: [
        {
          id: "1",
          name: "全部产品",
        },
      ],
      productOptions: [],
      inputHolder: "输入固件名称",
      columns: [
        { label: "固件名称", prop: "name" },
        { label: "固件版本号", prop: "version" },
        { label: "所属产品", prop: "productName" },
        { label: "添加时间", prop: "createTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      tableData: [
        // {
        //   id: "2333",
        //   createTime: "2021-12-20 15:15:15",
        // },
      ],
      loading: false,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      visible: false,
      title: "添加新固件",
      dialogWidth: "732px",
      type: 1,
      firmwareForm: {
        productKey: "",
        name: "",
        description: "",
        // updateDescription: "",
        fileName: "",
        firmwareSign: "",
        packageObjectName: "",
        version: "",
        size: "",
      },
      taskForm: {
        description: "",
        firmwareId: 0,
        upgradeDeviceName: [],
        upgradeProductKey: "",
        upgradeScope: "",
        upgradeVersion: "",
        upgradeWay: "",
      },
      taskFormName: "",
      taskFormProduct: "",
      taskFormVersion: "",
      rules: {
        productKey: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择产品",
          },
        ],
        name: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        version: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkVersion,
          },
        ],
        fileName: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "blur",
            message: "请上传固件升级文件",
          },
        ],
        description: [
          {
            required: false,
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      taskRules: {
        // upgradeWay
        // upgradeScope
        // upgradeVersion
        upgradeWay: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择升级方式",
          },
        ],
        upgradeScope: [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择升级范围",
          },
        ],
        upgradeVersion: [
          {
            required: this.isRequired,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择带升级版本",
          },
        ],
      },
      isRequired: true,
      nameTrue: true,
      descTrue: true,
      versionTrue: true,
      uploadTrue: true,
      delIds: "",
      upgradeDeviceName: "",
      upgradeDeviceNameCount: 0,
      postData: {},
      headers: {},
      searchParams: {},
      upgradeVersionList: [],

      //任务升级方式
      jobUpgradeWayList: [],

      //任务升级范围
      jobUpgradeScopeList: [],
      jobUpgradeScopeListOriginal: [],

      //设备升级状态
      deviceUpgradeStatusList: [],

      //固件任务状态
      jobStatusList: [],

      showSpecifyDevice: "",
      deviceVisible: false,
      versionList: [
        {
          id: "1",
          name: "全部版本",
        },
      ],
      inputDeviceHolder: "输入设备名称",
      selectDeviceHolder: "请选择版本号",
      deviceColumns: [
        {
          type: "selection",
        },
        { label: "设备名称", prop: "deviceName" },
        { label: "产品Key", prop: "productKey" },
        { label: "版本号", prop: "currentVersion" },
      ],
      deviceData: [],
      deviceLoading: false,
      selectDevice: "",
      upgradeDeviceNameTemp: [],

      //  原始文件数据
      fileData: "",
      uploadErrorMsg: ``,
    };
  },
  mounted() {
    this.fn_Init();
    // this.fn_get_PullDownList();
    // this.fn_get_table_data();
    // this.fn_get_product_select();
    //this.fn_get_VersionByProduct()
  },
  // keepalive 生命周期      //组件激活时触发
  activated() {
    let data = {
      ...this.searchParams,
      current: this.pagination.current,
      size: this.pagination.size,
    };
    this.fn_get_table_data(data);
    this.fn_get_PullDownList();
    this.fn_get_product_select();
  },
  //  跳转非详情   清除 keep-alive 缓存数组中的缓存视图
  beforeRouteLeave(to, from, next) {
    if (to.path != "/firmwareUpdateInfo") {
      // 取消缓存
      this.$clearKeepAlive(this, from.path);
    }
    next();
  },
  methods: {
    fn_Init() {
      this.headers = {
        "Nest-Auth": localStorage.getItem("access_token") || "",
        "Tenant-Id": Encrypt.decryptoByAES(
          localStorage.getItem("tenant_id") || ""
        ),
        Authorization: "Basic c21hcnRfcGFyazpzbWFydF9wYXJr",
      };
    },
    fn_get_PullDownList() {
      getPullDownList().then((res) => {
        if (res.code == 200) {
          this.deviceUpgradeStatusList = res.data.deviceUpgradeStatusList;
          this.jobStatusList = res.data.jobStatusList;
          this.jobUpgradeScopeList = res.data.jobUpgradeScopeList;
          this.jobUpgradeScopeListOriginal = res.data.jobUpgradeScopeList;
          this.jobUpgradeWayList = res.data.jobUpgradeWayList;
        }
      });
    },
    // 获取表格数据
    fn_get_table_data(params = {}) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      getFirmwareList(others)
        .then((res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
            }, 300);
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;

            this.fn_clear_form();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 多选数据
    fn_select_more_data(data) {
      // console.log(data)
      this.upgradeDeviceNameCount = data.length;
      this.upgradeDeviceNameTemp = data.map((item) => {
        return item.deviceName;
      });
    },
    fn_clear_form() {
      this.firmwareForm = {
        productKey: "",
        name: "",
        description: "",
        // updateDescription: "",
        fileName: "",
        firmwareSign: "",
        packageObjectName: "",
        version: "",
        size: "",
      };
      this.taskForm = {
        description: "",
        firmwareId: 0,
        upgradeDeviceName: [],
        upgradeProductKey: "",
        upgradeScope: "",
        upgradeVersion: "",
        upgradeWay: "",
      };
      this.selectDevice = "";
    },
    fn_upload__remove() {
      this.firmwareForm.fileName = "";
      this.firmwareForm.firmwareSign = "";
      this.firmwareForm.packageObjectName = "";
      this.firmwareForm.size = 0;
    },
    fn_upload__error(err) {
      // 公共拦截 已抛出提示语
      this.uploadErrorMsg = "固件上传失败";
      // this.$newNotify.error({
      //   message: "上传失败",
      // });
    },
    fn_upload__success(res) {
      try {
        if (res.code != "200") {
          this.$newNotify.error({
            message: res.message,
          });
          return;
        }
        this.firmwareForm.fileName = res.data.fileName;
        this.firmwareForm.firmwareSign = res.data.firmwareSign;
        this.firmwareForm.packageObjectName = res.data.packageObjectName;
        this.firmwareForm.size = res.data.size;

        this.postData = {
          packageObjectName: res.data.packageObjectName,
        };
        this.uploadTrue = false;
      } catch (e) {
        this.firmwareForm.fileName = "";
        this.firmwareForm.firmwareSign = "";
        this.firmwareForm.packageObjectName = "";
        this.firmwareForm.size = 0;
      }
      this.$refs.firmwareForm.clearValidate("fileName");
    },

    fn_upload__before(fileList) {
      let file = fileList[0];
      let isIMAGE = true;
      let nameList = file.name.split(".");
      let format = nameList[nameList.length - 1];
      if (
        format != "bin" &&
        format != "tar" &&
        format != "gz" &&
        format != "zip"
      ) {
        isIMAGE = false;
      }
      const isLt1024M = file.size / 1024 / 1024 < 2048;
      if (!isIMAGE) {
        this.uploadErrorMsg = "文件格式错误";
        this.$newNotify.error({
          message: "请上传正确格式的文件!",
        });
        return false;
      }
      if (!isLt1024M) {
        this.$newNotify.error({
          message: "上传文件不能大于2048M!",
        });
        return false;
      }

      this.fileData = file;
      return true;
    },
    // 产品列表下拉
    fn_get_product_select() {
      getProductSelect().then((res) => {
        if (res.code == 200) {
          this.productOptions = res.data;
          this.productOptionsCopy = [
            {
              name: "全部产品",
              id: "",
            },
            ...res.data.map((item) => {
              return {
                id: item.productKey,
                name: item.name,
              };
            }),
          ];
        }
      });
    },
    fn_get_VersionByProduct(id) {
      getVersionByProduct(id).then((res) => {
        if (res.code == 200) {
          this.upgradeVersionList = res.data;
          this.versionList = [
            {
              id: "",
              name: "全部版本",
            },
            ...res.data.map((item) => {
              return {
                id: item,
                name: item,
              };
            }),
          ];
        }
      });
    },
    //getVersionByProduct
    // 添加固件
    fn_open() {
      this.type = 1;
      this.title = "添加新固件";
      this.visible = true;
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
      if (name === "version") {
        this.versionTrue = value;
      }

      if (name === "description") {
        this.descTrue = value;
      }

      if (name == "fileName") {
        this.uploadTrue = value;
      }
    },
    // 搜索的下拉和输入的参数及操作
    handleSearch(params) {
      let others = {
        productKey: params.id == "1" ? "" : params.id,
        name: params.value,
      };
      this.productKey = params.id;
      this.searchParams = others;
      let newParams = fn_util__filter_null(others);
      this.fn_get_table_data(newParams);
    },
    // 清空输入框的操作
    fn_clear_search_info() {
      this.searchParams.name = "";
      this.fn_get_table_data({
        productKey: this.productKey == "1" ? "" : this.productKey,
      });
    },
    // 弹窗的确定按钮
    fn_sure() {
      if (this.type == 1) {
        this.$refs["firmwareForm"].validate((valid) => {
          if (valid) {
            let loading = this.$loading({
              lock: true,
              text: "loading...",
              spinner: "el-icon-loading",
              background: "rgba(255, 255, 255, 0.9)",
              target: document.querySelector(".iot-dialog-content"),
            });
            postFirmwareAdd(this.firmwareForm)
              .then((res) => {
                if (res.code == 200) {
                  this.$newNotify.success({
                    message: res.message,
                  });
                  this.pagination.current = 1;
                  this.fn_get_table_data({
                    size: this.pagination.size,
                    current: 1,
                  });
                  this.visible = false;
                } else {
                  this.$newNotify.error({
                    message: res.message,
                  });
                }
              })
              .finally(() => {
                loading.close();
              });
          }
        });
      } else if (this.type == 2) {
        let loading = this.$loading({
          lock: true,
          text: "loading...",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.9)",
          target: document.querySelector(".iot-dialog-content"),
        });
        deleteFirmware(this.delIds)
          .then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.visible = false;
              this.fn_get_table_data({
                size: this.pagination.size,
                current: 1,
              });
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          })
          .finally(() => {
            loading.close();
          });
      } else {
        this.$refs["taskForm"].validate((valid) => {
          if (valid) {
            let loading = this.$loading({
              lock: true,
              text: "loading...",
              spinner: "el-icon-loading",
              background: "rgba(255, 255, 255, 0.9)",
              target: document.querySelector(".iot-dialog-content"),
            });
            postFirmwareJobAdd(this.taskForm)
              .then((res) => {
                if (res.code == 200) {
                  this.$newNotify.success({
                    message: res.message,
                  });
                  this.visible = false;
                  this.fn_get_table_data({
                    size: this.pagination.size,
                    current: 1,
                  });
                } else {
                  this.$newNotify.error({
                    message: res.message,
                  });
                }
              })
              .finally(() => {
                loading.close();
              });
          }
        });
      }
    },
    fn_upgradeWay(val) {
      if (val == "dynamic") {
        this.taskRules.upgradeVersion = [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择升级版本",
          },
        ];
        this.jobUpgradeScopeList = [
          {
            value: "all",
            label: "全部设备",
          },
        ];
        this.taskForm.upgradeScope = "all";
        this.showSpecifyDevice = false;
      } else {
        this.taskRules.upgradeVersion = [
          {
            required: false,
            // message: '最多不超过200个字符',
            trigger: "change",
            message: "请选择升级版本",
          },
        ];
        this.jobUpgradeScopeList = this.jobUpgradeScopeListOriginal;
      }
    },
    fn_upgradeScope(val) {
      //指定设备
      this.showSpecifyDevice = val == "specify";

      if (val == "specify") {
        this.taskRules.upgradeDevice = [
          {
            required: true,
            // message: '最多不超过200个字符',
            trigger: "change",
            validator: this.checkUpgradeDevice,
          },
        ];
      }
    },

    fn_showDeviceList() {
      this.deviceVisible = true;
      let params = {
        productKey: this.taskFormProductKey,
      };
      this.fn_getDeviceByProduct(params);
    },
    fn_getDeviceByProduct(params) {
      getDeviceByProduct(params).then((res) => {
        if (res.code == 200) {
          this.deviceData = res.data;
          res.data.map((item, index) => {
            let flag = this.taskForm.upgradeDeviceName.includes(
              item.deviceName
            );
            if (flag) {
              this.$nextTick(() => {
                this.$refs.iotTable.toggleSelect(this.deviceData[index], true);
              });
            }
            return item;
          });
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 固件升级
    fn_toUpdate(row) {
      this.type = 3;
      this.visible = true;
      this.title = "固件升级";
      this.fn_get_VersionByProduct(row.productKey);

      this.taskForm.firmwareId = row.id;
      this.taskFormName = row.name;
      this.taskFormProduct = row.productName;
      this.taskFormProductKey = row.productKey;
      this.taskFormVersion = row.version;
    },
    // 查看详情
    fn_toDeviceDetail(row) {
      this.$router.push({
        path: "/firmwareUpdateInfo",
        query: {
          id: row.id,
        },
      });
    },
    // 打开删除弹窗
    fn_del(id) {
      this.delIds = id;
      this.type = 2;
      this.title = "确定删除此固件？";
      this.visible = true;
    },
    // 设备版本搜索
    handleDeviceSearch(form) {
      let params = {
        productKey: this.taskFormProductKey,
        version: form.id,
        deviceName: form.value,
      };
      this.fn_getDeviceByProduct(params);

      // getDeviceByProduct(params).then((res) => {
      // 	if (res.code == 200) {
      // 		this.deviceData = res.data
      // 	} else {
      // 		this.$newNotify.error({
      // 			message: res.message,
      // 		})
      // 	}
      // })
    },
    // 设备搜索信息清除
    fn_clear_search_device() {
      let params = {
        productKey: this.taskFormProductKey,
      };
      this.fn_getDeviceByProduct(params);

      // getDeviceByProduct(params).then((res) => {
      // 	if (res.code == 200) {
      // 		this.deviceData = res.data
      // 	} else {
      // 		this.$newNotify.error({
      // 			message: res.message,
      // 		})
      // 	}
      // })
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.searchParams.current = this.pagination.current;
      this.searchParams.size = val;
      this.fn_get_table_data(this.searchParams);
    },
    // 当前页
    handleCurrentChange(val) {
      this.searchParams.current = val;
      this.searchParams.size = this.pagination.size;
      this.fn_get_table_data(this.searchParams);
    },
    fn_notNull(val) {
      return val !== 0 && !val;
    },
    checkUpgradeDevice(rule, value, callback) {
      if (this.upgradeDeviceNameCount <= 0) {
        return callback(new Error("请选择设备范围"));
      } else {
        callback();
      }
    },
    checkName(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入固件名称"));
      } else if (!reg_sixteen(value)) {
        return callback(
          new Error(
            "支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧），必须以中文、英文或数字开头，长度不超过32个字符"
          )
        );
      } else {
        callback();
      }
    },
    checkVersion(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入固件版本号"));
      } else if (!reg_seventeen(value)) {
        return callback(
          new Error(
            "仅支持英文字母、数字、点、中划线和下划线，长度限制1~32个字符"
          )
        );
      } else {
        callback();
      }
    },
    fn_device_sure() {
      this.taskForm.upgradeDeviceName = this.upgradeDeviceNameTemp;
      this.deviceVisible = false;
      this.selectDevice = `已选择${this.upgradeDeviceNameCount}个设备`;
    },
    // 长度检验
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
  },
  watch: {
    visible(val) {
      if (!val && this.type == 1) {
        this.$refs["firmwareForm"] && this.$refs["firmwareForm"].resetFields();
        // 弹窗关闭  中断文件处理
      }
      if (this.type == 1)
        this.$refs.iotUpload && this.$refs.iotUpload.handleReset();
      if (!val && this.type == 3) {
        this.fn_clear_form();
        this.$refs["taskForm"] && this.$refs["taskForm"].resetFields();
      }
    },
    upgradeDeviceNameCount(val) {
      console.log(val);
    },
  },
};
</script>
<style lang="scss" scoped>
.firmware-update {
  padding: 32px 32px 0px 32px;
  .firmware-top {
    margin-top: 18px;
    justify-content: space-between;
    width: 100%;
  }
  .firm-content {
    margin-top: 18px;
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
  }
  .firm-bottom {
    text-align: right;
    margin-top: 14px;
  }
  .firmwareForm {
    /deep/ .el-form-item {
      margin-bottom: 17px;
    }
    .el-form-tips {
      margin-top: -17px;
    }
    .upload-text {
      width: 160px;
      background: #ebf6ff;
      color: #0088fe;
      height: 36px;
      line-height: 36px;
      font-size: 14px;
      font-weight: 400;
      padding: 0 0;
      user-select: none;
    }
    .el-upload__tip {
      color: #999999;
      font-size: 12px;
      font-weight: normal;
      font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
    }
  }
  .el-uploader-progress {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100px;
    color: #fff;
    line-height: 100px;
    font-size: 12px;
    font-weight: bold;
    border-radius: 5px;
    background-color: rgba(0, 0, 0, 0.5);
  }
  .device-content {
    .device-top {
      margin-top: 5px;
      justify-content: space-between;
      .left {
        height: 34px;
        line-height: 34px;
        span {
          &:first-child {
            height: 14px;
            width: 4px;
            background: #1890ff;
            margin-right: 10px;
            display: inline-block;
          }
          &:last-child {
            height: 18px;
            font-size: 18px;
            font-weight: 500;
            font-family: HarmonyOS Sans SC;
          }
        }
      }
    }
    .device-table {
      margin-top: 26px;
      margin-bottom: 28px;
      height: 496px;
      overflow-y: auto;
    }
  }
}
.upload-demo {
  /deep/ {
    .el-upload-list__item {
      height: 40px;
      background: #fbfbfc;
      border: 1px solid #ecedee;
      display: flex;
      align-items: center;
      padding: 0 12px;
      border-radius: 0;
      .el-upload-list__item-name {
        .el-icon-document {
          font-family: "tenant" !important;
          color: #379cff;
          font-size: 18px;
        }
        .el-icon-document::before {
          content: "\e652";
        }
      }
      .el-upload-list__item-status-label {
        top: 5px;
        right: 12px;
        font-size: 16px;
        .el-icon-upload-success {
          font-family: "tenant" !important;
          color: #0081ff;
          font-size: 16px;
        }
        .el-icon-upload-success::before {
          content: "\e650";
        }
      }
      .el-icon-close {
        font-size: 16px;
        top: 8px;
        right: 12px;
      }
      .el-progress {
        width: calc(100% - 24px);
        top: 24px;
      }
    }
    // .el-upload-list__item {
    //   transition: none !important;
    // }
  }
}
.el-form-tips {
  margin-top: -17px;
}
</style>
