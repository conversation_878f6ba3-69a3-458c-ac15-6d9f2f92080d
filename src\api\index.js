import axios from "axios";
import store from "../store";
import { baseUrl } from "../conf/env";
import router from "@/router";
import Encrypt from "@/util/aes";
import Vue from "vue";
axios.defaults.timeout = 60 * 1000;
// 请求拦截
axios.interceptors.request.use((config) => {
    let access_token = localStorage.getItem("access_token") || "";
    let tenant_id = Encrypt.decryptoByAES(
        localStorage.getItem("tenant_id") || ""
    );

    config.url = baseUrl + config.url;
    config.headers["Authorization"] = "Basic c21hcnRfcGFyazpzbWFydF9wYXJr";
    // config.headers["Authorization"] =
    //   "Basic aW90X2NvbnNvbGU6WjM4V2FFaDNzQXhxTFJMMA==";

    config.headers["Nest-Auth"] = access_token;
    config.headers["Tenant-Id"] = tenant_id;
    return config;
});

let formatResponse = (res) => {
    return {
        code: Number(res.Code || res.SubCode || res.code),
        data: res.Data != undefined ? res.Data : {},
        message: res.SubMessage || res.Message || res.message,
        requestId: res.RequestId || "",
    };
};

// 响应拦截
axios.interceptors.response.use(
    (response) => {
        let status = response.status;
        let businessStatus = response.data.Code || 500;
        if (response.config.url.indexOf(`/tenant/product/thingModel/export`) > -1) {
            return response.data;
        }
        let newResponse = formatResponse(response.data);
        // 特殊处理   登录接口失败的code码   后端归属为鉴权失败20001  但此处不能统一处理为20001
        if (
            response.config.url.indexOf("/oauth/token") > -1 &&
            businessStatus == 20001
        ) {
            return newResponse;
        }
        if (status == 401 || businessStatus == 20001) {
            //   跳转登录
            Vue.prototype.$notify({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                iconClass: "notify-error",
                message: newResponse.message || "会话已过期，请重新登录平台",
            });
            // 清空缓存
            store.dispatch("loginOut");
        } else if (status == 500 || businessStatus == 20000) {
            Vue.prototype.$notify({
                dangerouslyUseHTMLString: true,
                duration: 3000,
                iconClass: "notify-error",
                message: newResponse.message || "服务器异常，请联系管理员",
            });
            return Promise.reject(newResponse);
        } else if (status === 404) {
            router.push({
                path: "/404",
            });
        } else {
            return newResponse;
        }
    },
    (error) => {
        console.log("api提示：请求出现错误");
        if (error.response && error.response.status) {
            switch (error.response.status) {
                case 401:
                    // 未登录
                    Vue.prototype.$notify({
                        dangerouslyUseHTMLString: true,
                        duration: 3000,
                        iconClass: "notify-error",
                        message: "未登录",
                    });
                    // 清空缓存
                    store.dispatch("loginOut");
                    break;
                case 403:
                    // 登录过期  清除token
                    Vue.prototype.$notify({
                        dangerouslyUseHTMLString: true,
                        duration: 3000,
                        iconClass: "notify-error",
                        message: "登录过期",
                    });
                    break;
                case 404:
                    // if (error.response.data.message) {
                    //   Message({
                    //     message: error.response.data.message,
                    //     type: "error",
                    //   });
                    // }
                    // console.log('Route', router.push)
                    router.push({
                        path: "/404",
                    });
                    break;
                // case 500:
                //   Vue.prototype.$notify({
                //     dangerouslyUseHTMLString: true,
                //     duration: 3000,
                //     iconClass: "notify-error",
                //     message: error.response.data.Message || "服务器异常，请联系管理员",
                //   });
                //   break;
                // case 502:
                //   Vue.prototype.$notify({
                //     dangerouslyUseHTMLString: true,
                //     duration: 3000,
                //     iconClass: "notify-error",
                //     message: error.response.data.Message || "服务器异常，请联系管理员",
                //   });
                //   break;
                default:
                    console.log("*--*");
                    Vue.prototype.$notify({
                        dangerouslyUseHTMLString: true,
                        duration: 3000,
                        iconClass: "notify-error",
                        message: error.response.data.Message || "服务器异常，请联系管理员",
                    });
                    break;
            }
        }
        return Promise.reject(error.response);
    }
);

export default (config) => {
    return new Promise((resolve, reject) => {
        axios(config)
            .then((res) => {
                resolve(res);
            })
            .catch((err) => {
                reject(err);
            });
    });
};
