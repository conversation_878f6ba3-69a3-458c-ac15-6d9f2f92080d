<template>
  <div class="online-config">
    <div class="online-top">
      <el-radio-group v-model="type" size="medium" @change="handleTabChange">
        <el-radio-button :label="1">属性调试</el-radio-button>
        <el-radio-button :label="2">服务调用</el-radio-button>
      </el-radio-group>
    </div>
    <div class="service" v-if="type == 2">
      <el-select v-model="serviceIndex" placeholder="请选择服务">
        <el-option
          v-for="item in serviceList"
          :key="item.id"
          :value="item.id"
          :label="item.name"
        ></el-option>
      </el-select>
    </div>
    <div class="online-content" :style="onlineContent">
      <div style="height: 100%" v-if="online">
        <template v-if="type == 1 && form.modelData.length > 0">
          <div class="form-top flex">
            <p>功能名称 / 标识符</p>
            <p class="value">输入值</p>
          </div>
          <div class="form-content">
            <el-form ref="form" :model="form">
              <online-custom-item
                v-for="(item, index) in form.modelData"
                :key="item.id"
                :attr="item"
                :prop="`modelData.${index}`"
              />
            </el-form>
          </div>
          <div class="form-bottom flex">
            <el-button type="primary" v-throttle="500" @click="handleSubmit"
              >设置值</el-button
            >
            <el-button @click="handleReset">重置</el-button>
          </div>
        </template>
        <template v-else-if="type == 2 && serviceList.length > 0">
          <div class="code-content">
            <b-code-editor
              v-model="jsonData"
              :theme="theme"
              :auto-format="false"
              ref="editor"
              :show-number="showNumber"
              :readonly="readonly"
              :lint="lint"
              height="100%"
            />
          </div>
          <div class="form-bottom flex">
            <el-button
              type="primary"
              :class="!serviceIndex || !jsonData ? 'disabled' : ''"
              @click="handleSubmit"
              v-throttle="500"
              >发送调试</el-button
            >
          </div>
        </template>

        <div class="code-empty flex" v-else>
          <img src="~@/assets/images/empty/emptyData.png" alt />
          <p>
            暂无{{ type == 1 ? "属性" : "服务" }}功能定义，请先创建物模型属性
            <span class="cursor" @click="handleRoute">功能定义</span
            >，再进行设备调试
          </p>
        </div>
      </div>
      <div class="code-empty flex" v-else>
        <img src="~@/assets/images/empty/emptyData.png" alt />
        <p>当前设备不在线，请先保持设备在线</p>
      </div>
    </div>
  </div>
</template>
<script>
import { getDeviceSet, getDeviceCall } from "@/api/onlineDubug";
import onlineCustomItem from "../online-custom-item";
import { productAbilityDetail } from "@/api/product.js";
import { fn_util__date_format } from "@/util/util";
import toVW from "@/util/toVW.js";
import { mapGetters } from "vuex";
export default {
  name: "onlineConfig",
  components: {
    onlineCustomItem,
  },
  props: {
    tenant_id: {
      type: String,
    },
    deviceName: {
      type: String,
      default: "",
    },
    online: {
      type: Boolean,
    },
    productKey: {
      type: String,
    },
    productId: {
      type: String,
    },
  },
  data() {
    return {
      type: 1,
      form: {
        modelData: [],
      },
      serviceIndex: "",
      serviceList: [],
      jsonData: "",
      //编辑器配置
      showNumber: true,
      lint: false,
      readonly: false,
      wrap: true,
      theme: "idea",
    };
  },
  computed: {
    ...mapGetters(["userInfo"]),
    onlineContent() {
      return { height: this.type == 1 ? toVW(618) : toVW(570) };
    },
  },
  mounted() {
    this.getModel();
  },
  methods: {
    // 属性调试设置
    http_getDeviceSet(params) {
      const data = {
        deviceName: this.deviceName,
        params: params,
        productKey: this.productKey,
      };
      getDeviceSet(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: "操作成功",
          });
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 服务调用设置
    http_getDeviceCall(params) {
      const result = params; //.replace(/\s/g, "");
      const data = {
        identifier: this.serviceIndex,
        deviceName: this.deviceName,
        params: result,
        productKey: this.productKey,
      };
      getDeviceCall(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: "操作成功",
          });
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    getModel() {
      productAbilityDetail({
        // tenantId: this.tenant_id,
        productKey: this.productKey,
      }).then((res) => {
        if (res.code == 200 && typeof res.data == "string") {
          let model = JSON.parse(res.data);
          let list = model.properties;
          let service = model.services.filter(
            (item) => item.id !== "get" && item.id !== "set"
          );
          // console.log(list)
          this.form.modelData = this.formatModel(list);
          this.serviceList = service;
        }
      });
    },
    formatModel(list) {
      if (!Array.isArray(list) || list.length == 0) return list;
      let result = list.map((item) => {
        if (item.data.type === "enum" || item.data.type === "bool") {
          // 枚举 或 bool
          let childList = item.data.type === "enum" ? "enumList" : "boolList";
          item.data[childList] = [];
          for (let i in item.data.rules) {
            item.data[childList].push({
              key: i,
              value: i,
              label: item.data.rules[i],
            });
          }
        } else if (item.data.type === "object") {
          item.data.objectList = this.formatModel(item.data.rules);
        }
        item.value = "";
        return item;
      });
      return result;
    },
    // tab切换
    handleTabChange() {},
    deepModel(list) {
      if (!Array.isArray(list) || list.length == 0) return {};
      let result = {};
      list.map((item) => {
        let object;
        if (item.data.type == "time" && item.value) {
          let { timestamp } = fn_util__date_format(item.value);
          let time = Number((timestamp / 1000 + "").split(".")[0]);
          object = `${time}`;
        } else if (item.data.type == "object") {
          // object
          object = this.deepModel(item.data.rules);
        } else if (item.data.type == "array") {
          if (item.value) {
            object = JSON.parse(`${item.value}`);
          }
          /* object.forEach(i => {  //后台int类型值转换
            if (Object.prototype.toString.call(i === '[object Object]')) {
              Object.keys(i).forEach(k => {
                i[k] = `${i[k]}`
              })
            }
          }) */
        } else {
          // int float double enum text bool time array
          object = item.value;
        }
        result[item.id] = object;
        return item;
      });
      return result;
    },
    emptyValidate(object) {
      if (typeof object !== "object") return false;

      const newObject = this.filterResultObj(object);
      if (!Object.keys(newObject).length) {
        return 1;
      }

      const emptyArray = ["null", null, undefined, "undefined", ""];
      for (let key in object) {
        if (Object.prototype.toString.call(object[key]) === "[object Object]") {
          const newSecondObject = this.filterResultObj(object[key]);
          if (!Object.keys(newSecondObject).length) {
            return 0;
          } else {
            if (
              !Object.values(object[key]).every(
                (item) => !emptyArray.includes(item)
              )
            ) {
              return 2;
            } else {
              return 0;
            }
          }
        }
      }
    },
    handleSubmit() {
      if (this.type == 1) {
        this.$refs.form.validate((valid) => {
          if (valid) {
            let result = this.deepModel(this.form.modelData);
            let flag = this.emptyValidate(result);
            if (flag == 1) {
              this.$newNotify.warning({
                message: "无效调试设置属性，请填写属性值",
              });
              return;
            }
            if (flag == 2) {
              this.$newNotify.warning({
                message: "请填写完整的object格式数据",
              });
              return;
            }
            const newResult = this.filterResultObj(result);
            this.http_getDeviceSet(JSON.stringify(newResult));
          }
        });
      } else if (this.type == 2) {
        const result = this.jsonData;
        if (!this.serviceIndex || !this.jsonData) return;
        try {
          if (JSON.parse(result.trim())) {
            this.http_getDeviceCall(result);
          }
        } catch (e) {
          this.$newNotify.warning({
            message: "不是标准json",
          });
        }
      }
    },
    // 过滤空数据
    filterResultObj(obj) {
      const res = {};
      for (let key in obj) {
        const value = obj[key];
        const emptyArray = ["null", null, undefined, "undefined", ""];
        if (Object.prototype.toString.call(value) === "[object Object]") {
          res[key] = this.filterResultObj(value);
          if (emptyArray.includes(value) || !Object.keys(res[key]).length)
            delete res[key];
        } else {
          !emptyArray.includes(value) && (res[key] = value);
        }
      }
      return res;
    },
    handleReset() {
      this.$refs.form.resetFields();
      // this.getModel();
    },
    handleRoute() {
      this.$router.push({
        name: "productDetail",
        query: {
          id: this.productId,
        },
        params: {
          num: "1",
        },
      });
    },
  },
  watch: {
    jsonData(newVal) {
      this.lint = !!newVal;
    },
  },
};
</script>
<style lang="scss" scoped>
.online-config {
  width: 700px;
  margin-right: 18px;
  .online-top {
    margin-bottom: 18px;
    :deep(.el-radio-button__inner) {
      height: 34px;
    }
  }
  .online-content {
    border: 1px solid #eeeff1;
    height: 618px;
    box-shadow: inset 0px 0px 8px rgba(0, 0, 0, 0.08);
    .form-top {
      height: 48px;
      background: #f7f7f7;
      padding: 0 18px;
      align-items: center;
      justify-content: space-between;
      p {
        color: #515151;
        font-size: 14px;
      }
      .value {
        width: 458px;
      }
    }
    .form-content {
      height: calc(100% - 48px - 58px);
      padding: 18px;
      overflow: auto;
    }
    .code-content {
      height: calc(100% - 58px);
      padding: 18px;
      :deep(.cm-s-idea) {
        height: 100%;
      }
    }
    .code-empty {
      height: 100%;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      img {
        width: 90px;
        height: 108px;
      }
      p {
        font-size: 14px;
        line-height: 16px;
        padding-top: 28px;
        color: #888888;
        span {
          color: #018aff;
        }
      }
    }
  }
  .form-bottom {
    height: 58px;
    align-items: center;
    padding: 0 18px;
    background: #fbfbfb;
    :deep(.el-button) {
      border-radius: 0;
      // width: 90px;
      padding: 0 24px;
      height: 34px;
      line-height: 34px;
      text-align: center;
      font-family: H_Medium;
    }
    :deep(.el-button--primary) {
      background: linear-gradient(180deg, #0088fe, #006eff 100%);
    }
    :deep(.el-button--default) {
      color: #333333;
      margin-left: 14px;
    }
    .disabled {
      background: #cecece;
      color: #ffffff;
      border-color: #cecece;
    }
  }
}
.service {
  padding-bottom: 14px;
}
:deep(.el-input__inner) {
  border-radius: 0;
}

.cursor {
  cursor: pointer;
}
</style>
