<template>
  <div class="top-bar flex">
    <p class="flex">
      <img
        src="~@/assets/images/index/newLogo.png"
        alt=""
        ondragstart="return false;"
        style="width: 40px;height: 40px;"
      />
      <span>钰辰物联网平台</span>
    </p>
    <!-- islogin字段判断用户是否登录，登录则渲染以下页面 -->
    <div class="user flex" v-if="isLogin">
      <div class="user-text"><span>服务支持</span><span>文档中心</span></div>
      
      <div class="user-message flex">
        <img src="~@/assets/images/index/message-icon.png" alt="" />
      </div>
      <el-tooltip
        class="item"
        effect="light"
        placement="bottom-end"
        popper-class="top-bar-tooltip"
      >
        <div slot="content" class="content">
          <p class="phone">{{ phone }}</p>
          <div class="user-info">
            <div class="item flex" @click="handleToAccountInfo">
              <img src="~@/assets/images/index/user-mini-icon.png" alt="" />
              <span>账号信息</span>
            </div>
            <div class="item flex" @click="handleUpdatePwd">
              <img src="~@/assets/images/index/edit-mini-icon.png" alt="" />
              <span>修改密码</span>
            </div>
          </div>
          <p class="out" @click="loginOut">退出登录</p>
        </div>
        <img
          class="user-pic"
          src="~@/assets/images/index/user-icon.png"
          alt=""
        />
      </el-tooltip>
    </div>
  </div>
</template>

<script>
import { mapGetters } from "vuex";
import { getLoginOut } from "@/api/user";
export default {
  props: {
    isLogin: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {}
  },
  computed: {
    ...mapGetters(["userInfo"]),//使用 Vuex 的 mapGetters 辅助函数，将 userInfo getter 映射到该组件中，以便可以直接通过 this.userInfo 访问到用户信息
    phone() {
      let reg = /(\d{3})\d*(\d{4})/;
      if (this.userInfo.phone) {//判断 this.userInfo.phone 是否存在，即判断是否已经从 Vuex 的 userInfo getter 中获取到了手机号码。
        return this.userInfo.phone.replace(reg, "$1****$2") || "----";//如果已经获取到了手机号码，则使用正则表达式将手机号码进行部分隐藏处理，即将手机号码中间的数字替换为 ****，并返回处理后的结果；如果未能获取到手机号码，则返回 "----" 表示未知
      } else {
        return (//如果未能从 Vuex 的 userInfo getter 中获取到手机号码,则从 localStorage 中获取用户信息（通常是在用户登录时保存到 localStorage 中），然后从中提取手机号码，并进行部分隐藏处理，最终返回处理后的结果；如果未能获取到手机号码，则返回 "----" 表示未知
          JSON.parse(localStorage.getItem("userInfo")).phone.replace(
            reg,
            "$1****$2"
          ) || "----"
        );
      }
    },
  },
  mounted() {
    // console.log(this.userInfo);
  },
  methods: {
    loginOut() {//退出登录
      getLoginOut().then((res) => {
        if (res.code == 200) {
          this.$store.dispatch("loginOut");//调用 Vuex 的 dispatch 方法触发 loginOut 动作，用于清除用户登录状态。
          this.$router.replace({ path: "/login" });
        } else {
          this.$message.warning("服务器异常，请联系管理员");
        }
      });
    },
    handleToAccountInfo() {//跳转至账号信息页面
      this.$router.push({
        path: 'accountInfo',
        query: {
          type: '0'
        }
      })
    },
    handleUpdatePwd() {//跳转至修改密码页面
      this.$router.push({
        path: 'accountInfo',
        query: {
          type: '1'
        }
      })
    },
    // 弹窗确定按钮
    fn_sure() {}
  },
};
</script>

<style lang="scss" scoped>
.top-bar {
  width: 100%;
  height: 76px;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px 0 20px;
  background: #ffffff;
  box-shadow: 0px 3px 8px rgba(0, 0, 0, 0.05), inset 0px -1px 0px rgba(100, 100, 100, 0.1);

  position: fixed;
  top: 0;
  left: 0;
  z-index: 99;
  p {
    align-items: center;
    img {
      width: 30px;
    }
    span {
      padding-left: 10px;
      font-family: H_Medium;
      font-size: 26px;
      color: #333333;
      font-weight: 500;
    }
  }
  .user {
    .user-text{
        font-size: 14px;
        font-weight: 350;
        line-height: 30px;
        color: #515151;
        span{
          margin-left: 10px;
          margin-right: 7px;
        }
        span:hover{
          cursor: pointer;
        }
      }
    .user-message {
      width: 30px;
      height: 30px;
      background: #ffffff;
      // border-radius: 2px;
      position: relative;
      align-items: center;
      justify-content: center;
      img {
        width: 12px;
      }
      
    }
    .user-message::before {
      content: "";
      width: 5px;
      height: 5px;
      border-radius: 50%;
      background: #f83e37;
      position: absolute;
      top: 0px;
      right: 3px;
    }
    .user-message:hover{
      cursor: pointer;
    }
    .user-pic {
      width: 36px;
      margin-left: 20px;
    }
    .user-pic:hover{
      cursor: pointer;
    }
  }
}
</style>
<style lang="scss">
.top-bar-tooltip {
  border-radius: 3px !important;
  // border: 1px solid #e4e7ec !important;
  border: none !important;
  background: #ffffff !important;
  box-shadow: 0px 0px 10px rgba(0, 0, 0, 0.15) !important;
  backdrop-filter: blur(4px);
  padding: 0;
  font-family: H_Medium;
  top: 35px !important;
  .popper__arrow {
    border-width: 10px;
    border-bottom-color: #e4e7ec !important;
    top: -10px !important;
  }
  .popper__arrow::after {
    border-width: 10px;
    left: -5px;
  }

  .content {
    width: 194px;
    // height: 200px;
    p {
      display: flex;
      align-items: center;
      height: 53px;
      padding: 0 30px;
      border-bottom: 1px solid #eeeeee;
    }
    p:last-child {
      border-bottom: none;
    }
    .phone {
      color: #262626;
      font-size: 16px;
    }
    .user-info {
      border-bottom: 1px solid #eeeeee;
      font-size: 14px;
      color: #595959;
      .item {
        height: 38px;
        line-height: 38px;
        padding: 0 30px;
        margin-top: 7px;
        cursor: pointer;
        &:nth-child(2) {
          margin-bottom: 7px;
          margin-top: 0;
        }
        &:hover {
          background-color: #F2F9FF;
        }
        img {
          width: 12px;
          height: 13px;
          margin-right: 10px;
          margin-top: 11px;
        }
      }
    }
    .out {
      color: rgba(0, 136, 254, 1);
      font-size: 15px;
      letter-spacing: 1px;
      cursor: pointer;
      font-weight: normal;
      line-height: 21px;
    }
  }
}
</style>
