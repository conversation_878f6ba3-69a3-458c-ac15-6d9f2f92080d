<template>
  <div class="reset">
    <top-bar :isLogin="false" />
    <div class="step-bar flex">
      <div class="step-bar-item" v-for="(item, index) in stepList" :key="index">
        <h5
          :style="{
            color: active >= item.index ? '#0088FE' : '#333333',
          }"
        >
          {{ item.title }}
        </h5>
        <span
          :style="{
            background: active >= item.index ? '#0088FE' : '#cccccc',
          }"
        >
          <b></b>
          <i :style="{ width: active > item.index ? toVW(90) : '0px' }"></i>
        </span>
      </div>
    </div>
    <div class="content flex">
      <!-- <step-item1 v-if="active == 0" @next="handleNext" />
      <step-item2 v-if="active == 1" @next="handleNext" />
      <step-item3 v-if="active == 2" @next="handleNext" /> -->
      <el-carousel
        ref="carousel"
        style="width: 100%"
        :height="toVW(670)"
        arrow="never"
        indicator-position="none"
        :autoplay="false"
        :loop="false"
      >
        <!--  -->
        <el-carousel-item>
          <step-item1
            key="stepItem1"
            :step="active"
            @next="handleNext"
            @route="handleLogin"
          />
        </el-carousel-item>
        <el-carousel-item>
          <step-item2
            key="stepItem2"
            :step="active"
            :phone="phone"
            @next="handleNext"
            @route="handleLogin"
          />
        </el-carousel-item>
        <el-carousel-item>
          <step-item3
            key="stepItem3"
            :step="active"
            :phone="phone"
            :captcha="captcha"
            @next="handleNext"
            @route="handleLogin"
          />
        </el-carousel-item>
      </el-carousel>
    </div>
    <copyright />
    <confirm title="重置密码成功" ref="confirm" />
  </div>
</template>

<script>
import conf from "./conf";

export default conf;
</script>

<style lang="scss" scoped>
.reset {
  height: 100vh;
  padding-top: 52px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .step-bar {
    justify-content: center;
    padding-top: 58px;
    .step-bar-item {
      text-align: center;
      margin-right: 58px;
      h5 {
        color: #333333;
        font-weight: normal;
        font-size: 12px;
        line-height: 14px;
        padding-bottom: 8px;
      }
      span {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: #cccccc;
        font-size: 0;
        position: relative;
      }
      b {
        width: 90px;
        height: 1px;
        background: #cccccc;
        position: absolute;
        left: calc(100% + 2px);
        top: 50%;
        transform: translateY(-50%);
        z-index: 1;
      }
      i {
        width: 0px;
        height: 1px;
        background: #0088fe;
        transition: all 0.3s;
        position: absolute;
        left: calc(100% + 2px);
        top: 50%;
        transform: translateY(-50%);
        z-index: 2;
      }
    }
    .step-bar-item:last-child {
      margin-right: 0;
      b,
      i {
        display: none;
      }
    }
  }
  .content {
    padding-top: 56px;
    justify-content: center;
  }
}
</style>
