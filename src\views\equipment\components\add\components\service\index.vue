<template>
  <div>
    <el-form-item prop="mode">
      <div class="form-item">
        <p class="form-item-label">调用方式</p>
        <el-radio-group v-model="isSync" @change="syncChange">
          <el-radio label="async">异步调用</el-radio>
          <el-radio label="sync">同步调用</el-radio>
        </el-radio-group>
      </div>
    </el-form-item>
    <el-form-item>
      <json-struct
        title="输入参数"
        :data="inList"
        key="inStruct"
        :isChild="false"
        @addAttribute="(data) => addAttribute(data, 'in')"
        @editAttribute="(data) => editAttribute(data, 'in')"
        @deleteAttribute="(data) => deleteAttribute(data, 'in')"
      />
    </el-form-item>
    <div class="el-form-tips" v-if="inTips">{{ inTips }}</div>
    <el-form-item>
      <json-struct
        title="输出参数"
        :data="outList"
        key="outStruct"
        :isChild="false"
        @addAttribute="(data) => addAttribute(data, 'out')"
        @editAttribute="(data) => editAttribute(data, 'out')"
        @deleteAttribute="(data) => deleteAttribute(data, 'out')"
      />
    </el-form-item>
    <div class="el-form-tips" v-if="outTips">{{ outTips }}</div>
  </div>
</template>

<script>
import jsonStruct from "../json-struct";
export default {
  name: "form-service",
  components: { jsonStruct },
  data() {
    return {
      inList: [],
      outList: [],
      isSync: "async",
    };
  },
  props: {
    form: {
      type: Object,
    },
    outTips: {
      type: String,
    },
    inTips: {
      type: String,
    },
  },
  watch: {
    form: {
      deep: true,
      handler: function () {
        this.inList = this.form.in || [];
        this.outList = this.form.out || [];
        this.isSync = this.form.isSync || "async";
      },
    },
  },
  mounted() {
    if (this.form) {
      this.inList = this.form.in || [];
      this.outList = this.form.out || [];
      this.isSync = this.form.isSync || "async";
    }
  },
  methods: {
    addAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("addAttribute", params);
    },
    editAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("editAttribute", params);
    },
    deleteAttribute(data, source) {
      let params = {
        data,
        source,
      };
      this.$emit("deleteAttribute", params);
    },
    syncChange() {
      this.$emit("change", {
        key: "isSync",
        value: this.isSync,
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.el-form-tips {
  font-size: 12px;
  font-weight: 400;
  color: #f56c6c;
  margin-top: -28px;
  margin-bottom: 12px;
}
</style>
