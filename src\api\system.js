/*
 * @Author: your name
 * @Date: 2021-11-08 21:06:25
 * @LastEditTime: 2022-04-19 15:09:47
 * @LastEditors: Please set LastEditors
 * @Description: In User Settings Edit
 * @FilePath: \tenant-web\src\api\system.js
 */
import request from "./index";
import { BASE_SERVER } from "../conf/env";
const system = BASE_SERVER;

/**
 * @desc child-list
 * @params params
 * @returns
 */

export const getChildList = (params) => {
  return request({
    url: `${system}/dict-biz/child-list`,
    method: "get",
    params,
  });
};
