/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-06 14:27:25
 */
/**
 * 日期格式化
 * @param {*} value
 * @returns
 */

export const fn_util__date_format = (value = new Date()) => {
    let date = new Date(value);
    date === "Invalid Date" && (date = new Date());
    if (date !== "Invalid Date") {
        let yy = date.getFullYear(), // year
            MM = date.getMonth() + 1, // month
            dd = date.getDate(), // day
            hh = date.getHours(), // hour
            mm = date.getMinutes(), // minute
            ss = date.getSeconds(), // second
            timestamp = date.getTime(), // 时间搓
            linuxtime = Number((timestamp / 1000 + "").split(".")[0]),
            day = date.getDay(); // 周几
        MM = MM > 9 ? MM : "0" + MM;
        dd = dd > 9 ? dd : "0" + dd;
        hh = hh > 9 ? hh : "0" + hh;
        mm = mm > 9 ? mm : "0" + mm;
        ss = ss > 9 ? ss : "0" + ss;
        day = +day === 0 ? 7 : day;
        let dayToUpperCase = ["一", "二", "三", "四", "五", "六", "日"];
        return {
            yy,
            MM,
            dd,
            hh,
            mm,
            ss,
            timestamp,
            linuxtime,
            day,
            dayToUpperCase: dayToUpperCase[day - 1],
        };
    }
};

/*
 *  description: 在vue中使用的防抖函数
 *  param fnName {String}  函数名
 *  param time {Number}    延迟时间
 *  return: 处理后的执行函数
 */
export const VueDebounce = (fnName, time) => {
    let timeout = null;
    return function () {
        if (timeout) {
            clearTimeout(timeout);
        }
        timeout = setTimeout(() => {
            this[fnName]();
        }, time);
    };
};

export const fnThrottle = (func, delay = 300) => {
    let prev = 0;
    return function () {
        let now = Date.now();
        if (now - prev >= delay) {
            func.apply(this, arguments);
            prev = Date.now();
        }
    };
};

export const getLength = (val) => {
    let str = new String(val);
    let bytesCount = 0;
    for (let i = 0, n = str.length; i < n; i++) {
        let c = str.charCodeAt(i);
        if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)) {
            bytesCount += 1;
        } else {
            bytesCount += 2;
        }
    }
    return bytesCount;
};

/**
 * @desc 过滤对象中的空数据
 * @param {Object} obj
 * @returns {Object}
 */
export const fn_util__filter_null = (obj) => {
    const res = {};
    for (let key in obj) {
        const value = obj[key];
        const emptyVal = ["null", null, undefined, "undefined", ""];
        !emptyVal.includes(value) && (res[key] = value);
    }
    return res;
};

// 支持中文、英文、数字、下划线的组合，最多不超过32个字符
export const reg_one = (val) => {
    return (
        /^[a-zA-Z0-9_\u4e00-\u9fa5]{1,32}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 33
    );
};

// 支持中文、英文、数字、下划线和短划线的组合，最多不超过32个字符  最小4字符
export const reg_one_one = (val) => {
    return (
        /[a-zA-Z0-9-_\u4e00-\u9fa5]{2,32}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 33 &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length >= 4
    );
};

// 支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制 4~30个字符，中文及日文算 2 个字符
export const reg_two = (val) => {
    return (
        /^[a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00 -@()/]{0,30}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length < 31 &&
        val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length > 3
    );
};

// 支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点；必须以中文、英文或数字开头，不超过 30 个字符
export const reg_three = (val) => {
    return (
        /^[a-zA-Z0-9\u4e00-\u9fa5][a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00-@()/\\.]{0,29}$/.test(
            val
        ) && val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length < 31
    );
};

// 支持中文、英文、数字、下划线的组合，不超过 50 个字符
export const reg_four = (val) => {
    return (
        /^[a-zA-Z0-9_\u4e00-\u9fa5]{1,50}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 51
    );
};

// 取值范围(最小值不得大于等于最大值)
export const reg_five = (val, val2) => {
    return +val >= +val2;
};

// 判断为整数
export const reg_six = (val) => {
    // return typeof val === "number" && val % 1 === 0;
    return /^[0-9]+([.]{1}[0-9]+){0,1}$/.test(val) && val % 1 === 0;
};

// 不得超过num个字符
export const reg_seven = (val, num = 101) => {
    return val.length < num;
};

// 取值范围为 1 ~ num 的正整数
export const reg_eight = (val, num = 1000) => {
    return (
        /^[0-9]+([.]{1}[0-9]+){0,1}$/.test(val) &&
        val % 1 === 0 &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < num
    );
};

// 支持英文、数字、下划线的组合，不超过 50 个字符
export const reg_nine = (val) => {
    return (
        /^[a-zA-Z0-9_]{1,50}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 51
    );
};

// 取值范围(最小值不得大于最大值)
export const reg_ten = (val, val2, val3) => {
    return +val < +val3 && +val3 <= +val2;
};

// 取值范围(最小值不得大于最大值)
export const reg_eleven = (val) => {
    return /^\d+(\.\d+)?$/.test(val);
};

// 取值范围为 1 ~ num 的正整数
export const reg_twelve = (val, num = 1000) => {
    return /^[0-9]+([.]{1}[0-9]+){0,1}$/.test(val) && val % 1 === 0 && val <= num;
};
// 支持英文字母、数字、下划线(_)、中划线(-)、点号(.)、半冒号(:)和特殊字符(@)、长度限制为6-16个字符
export const reg_thirteen = (val) => {
    return (
        /^[a-z_A-Z0-9- \\.@:]{5,16}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 17 &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length > 5
    );
};
// 支持英文字母、数字、下划线(_)、中划线(-)、点号(.)、半冒号(:)和特殊字符(@)、长度限制为1-32个字符
export const reg_fifteen = (val) => {
    return (
        /[a-z_A-Z0-9-\u4e00-\u9fa5\\.@:]{1,32}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 32 &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length >= 1
    );
};
// http 校验
export const reg_fourteen = (val) => {
    // eslint-disable-next-line no-useless-escape
    return /^http:\/\/?[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+(:\d+)*(\/\w+\.\w+)*([\?&]\w+=\w*)*$/.test(
        val
    );
};

//支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符
export const reg_sixteen = (val) => {
    return (
        /^[a-zA-Z0-9\u0391-\uFFE5][a-z_A-Z0-9\u0391-\uFFE5-_()]{0,32}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 32 &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length > 0
    );
};

//支持中文、英文大小写、数字、部分常用符号（下划线，减号，括弧）， 必须以中文、英文或数字开头，长度不超过32个字符
export const reg_twentyfoure = (val) => {
    return (
        /^[a-zA-Z\u4e00-\u9fa5\u0391-\uFFE5][a-zA-Z0-9_\u4e00-\u9fa5\u0391-\uFFE5\-()]{0,31}$/.test(val) &&
        val.length <= 32 &&
        val.length > 0
    );
};

// 支持中文、英文、数字、下划线的组合，最多不超过30个字符
export const reg_twentyOne = (val) => {
    return (
        /^[a-zA-Z0-9_\u4e00-\u9fa5]{1,30}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5]/g, "aa").length < 31
    );
};

// 支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制 4~30个字符，中文及日文算 2 个字符
export const reg_twentyTwo = (val) => {
    return (
        /^[a-zA-Z0-9_\u4e00-\u9fa5\u0800-\u4e00 -@()/]{0,128}$/.test(val) &&
        val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length < 127
    );
};

//1-64位字符、支持中文、英文、数字及特殊符号，必须中文或者英文字母开头
export const reg_twentyThree = (val) => {
    return /^[\u4e00-\u9fa5a-zA-Z][\u4e00-\u9fa5a-zA-Z0-9!@#$%^&*()_+=[\]{}|\\;:'",<.>/?]{0,63}$/.test(val)
}


//仅支持英文字母、数字、点、中划线和下划线，长度限制1~32个字符
export const reg_seventeen = (val) => {
    return /^[0-9a-z_A-Z_.-]{1,32}$/.test(val);
};
// 仅支持数字且小数点后不得超过7位
export const eighteen = (val) => {
    return /^-?\d{1,12}([.]\d{0,7})?$/.test(val);
};
// 仅支持数字且小数点后不得超过7位
export const nineteen = (val) => {
    return /^-?\d{1,12}([.]\d{0,16})?$/.test(val);
};
// 仅支持数字
export const twenty = (val) => {
    return /^[-+]?[0-9]+(\.?[0-9]+)?$/.test(val);
};
//验证是否为json
export const isJson = (val) => {
    if (typeof val == 'string') {
        try {
          var obj = JSON.parse(val);
          // 等于这个条件说明就是JSON字符串 会返回true
          if (typeof obj == 'object' && obj) {
            return true;
          } else {
              //不是就返回false
            return false;
          }
        } catch (e) {
          return false;
        }
      }
      return false;
};

export const compareData = [
    {
        type: "int",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
            {
                symbol: "!=",
                text: "不等",
                value: "not",
            },
            {
                symbol: ">",
                text: "大于",
                value: "gt",
            },
            {
                symbol: "<",
                text: "小于",
                value: "lt",
            },
            {
                symbol: ">=",
                text: "大于等于",
                value: "gte",
            },
            {
                symbol: "<=",
                text: "小于相等",
                value: "lte",
            },
        ],
    },
    {
        type: "float",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
            {
                symbol: "!=",
                text: "不等",
                value: "not",
            },
            {
                symbol: ">",
                text: "大于",
                value: "gt",
            },
            {
                symbol: "<",
                text: "小于",
                value: "lt",
            },
            {
                symbol: ">=",
                text: "大于等于",
                value: "gte",
            },
            {
                symbol: "<=",
                text: "小于相等",
                value: "lte",
            },
        ],
    },
    {
        type: "double",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
            {
                symbol: "!=",
                text: "不等",
                value: "not",
            },
            {
                symbol: ">",
                text: "大于",
                value: "gt",
            },
            {
                symbol: "<",
                text: "小于",
                value: "lt",
            },
            {
                symbol: ">=",
                text: "大于等于",
                value: "gte",
            },
            {
                symbol: "<=",
                text: "小于相等",
                value: "lte",
            },
        ],
    },
    {
        type: "bool",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
        ],
    },
    {
        type: "time",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
            {
                symbol: "!=",
                text: "不等",
                value: "not",
            },
            {
                symbol: ">",
                text: "大于",
                value: "gt",
            },
            {
                symbol: "<",
                text: "小于",
                value: "lt",
            },
            {
                symbol: ">=",
                text: "大于等于",
                value: "gte",
            },
            {
                symbol: "<=",
                text: "小于相等",
                value: "lte",
            },
        ],
    },
    {
        type: "enum",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
        ],
    },
    {
        type: "text",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
        ],
    },
    {
        type: "array",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
        ],
    },

    {
        type: "object",
        condition: [
            {
                symbol: "==",
                text: "相等",
                value: "eq",
            },
        ],
    },
];
