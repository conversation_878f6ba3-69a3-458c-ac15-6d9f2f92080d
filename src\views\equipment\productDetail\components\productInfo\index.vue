<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-09 10:49:46
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-01-15 16:14:54
-->
<template>
  <div class="info">
    <div class="info-content">
      <div class="info-title">
        <div>产品信息</div>
        <div class="info-edit color2" @click="fn_open">
          <img src="~@/assets/images/index/edit.svg" />
          <span>编辑</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>ProductKey</span>
          <span>{{ produceForm.productKey || "-" }}</span>
        </div>
        <div class="item">
          <span>ProductSecret</span>
          <span>{{ produceForm.productSecret || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>产品名称</span>
          <span>{{ produceForm.name || "-" }}</span>
        </div>
        <div class="item">
          <span>产品分类</span>
          <span>{{ produceForm.hierarchyClassifiedName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>节点类型</span>
          <span>{{ produceForm.deviceGatewayTypeName || "-" }}</span>
        </div>
        <div class="item">
          <span>认证方式</span>
          <span>{{ produceForm.aclWayName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>通信方式</span>
          <span>{{ produceForm.networkWayName || "-" }}</span>
        </div>
        <div class="item">
          <span>数据协议</span>
          <span>{{ produceForm.dataFormatName || "-" }}</span>
        </div>
      </div>
      <div class="info-row flex">
        <div class="item">
          <span>创建时间</span>
          <span>{{ produceForm.createTime || "-" }}</span>
        </div>
        <div class="item">
          <span>产品描述</span>
          <div class="item-span">
            {{ produceForm.description || "-" }}
          </div>
        </div>
      </div>
    </div>

    <div class="info-config">
      <div class="info-title">
        <div>产品配置</div>
        <div class="color2"></div>
      </div>
      <div class="info-config-content">
        <div class="flex">
          <p class="info-config-key">是否禁止</p>
          <p>
            <el-switch
              v-throttle="500"
              v-model="produceForm.productDisableStatus"
              @change="fn_isdisable"
              active-color="#13ce66"
              inactive-color="#efefef"
            ></el-switch>
            <span class="config-text">{{
              produceForm.productDisableStatus ? "已开启" : "已关闭"
            }}</span>
          </p>
        </div>
        <div class="flex">
          <p class="info-config-key">动态注册</p>
          <p>
            <el-switch
              v-throttle="500"
              v-model="produceForm.dynamicRegisterAllowed"
              @change="fn_isregister"
              active-color="#13ce66"
              inactive-color="#efefef"
            ></el-switch>
            <span class="config-text">{{
              produceForm.dynamicRegisterAllowed ? "已开启" : "已关闭"
            }}</span>
          </p>
        </div>

        <div class="flex">
          <p class="info-config-key">自动注册</p>
          <p>
            <el-switch
              v-throttle="500"
              v-model="produceForm.autoRegisterAllowed"
              :disabled="!produceForm.dynamicRegisterAllowed"
              @change="fn_isauto"
              active-color="#13ce66"
              inactive-color="#efefef"
            ></el-switch>
            <span class="config-text">{{
              produceForm.autoRegisterAllowed ? "已开启" : "已关闭"
            }}</span>
          </p>
        </div>
        <div></div>
      </div>
    </div>

    <iot-dialog
      :visible.sync="visible"
      :title="title"
      width="718px"
      :showLoading="true"
      @callbackSure="fn_sure"
    >
      <template #body>
        <!-- 编辑产品 -->
        <iot-form>
          <template #default>
            <el-form
              ref="produceForm"
              class="produceForm"
              :label-position="'top'"
              :model="produceForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <el-form-item label="产品名称" prop="name">
                <el-input v-model="produceForm.name"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制
                4~30个字符，中文及日文算 2 个字符
              </div>

              <el-form-item label="产品品类">
                <el-input
                  :disabled="true"
                  v-model="produceForm.classifiedName"
                ></el-input>
              </el-form-item>

              <el-form-item label="节点类型">
                <el-input
                  :disabled="true"
                  v-model="deviceGatewayTypeName"
                ></el-input>
              </el-form-item>

              <el-form-item label="数据协议">
                <el-input
                  :disabled="true"
                  v-model="produceForm.dataFormatName"
                ></el-input>
              </el-form-item>

              <el-form-item label="连网方式">
                <el-input :disabled="true" v-model="networkWayName"></el-input>
              </el-form-item>

              <el-form-item label="产品描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="produceForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </el-form>
          </template>
        </iot-form>
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import IotForm from "@/components/iot-form";
import IotDialog from "@/components/iot-dialog";
import {
  getProductDetail,
  getProductUpdate,
  setProductConfig,
} from "@/api/product.js";
import { getDictNetList } from "@/api/dictionary";
import { reg_two, reg_seven } from "@/util/util.js";
export default {
  name: "ProductInfo",
  components: {
    IotForm,
    IotDialog,
  },
  data() {
    return {
      value: false,
      value2: false,
      value3: false,
      visible: false,
      produceForm: {
        id: this.$route.query.id,
        name: "",
        productSecret: "", // 产品密码
        productKey: "", // 产品密钥
        description: "",
        createTime: "",
        aclWayId: "",
        productDisableStatus: false, // 是否禁止
        dynamicRegisterAllowed: false, // 动态注册
        autoRegisterAllowed: false, // 自动注册
        classifiedName: "", // 产品品类
        projectName: "", // 项目名称
        networkWayName: "", // 通信方式
        aclWayName: "", // 认证方式
        dataFormatName: "", // 数据协议
        deviceGatewayTypeId: "", // 节点类型
        networkWayId: "", // 联网方式ID
        hierarchyClassifiedName: "",
        deviceGatewayTypeName: "",
      },
      productDisableStatus: false,
      dynamicRegisterAllowed: false,
      autoRegisterAllowed: false,
      descLength: 0,
      rules: {
        name: [
          {
            required: true,
            // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        description: [
          {
            required: false,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      nameTrue: true,
      descTrue: true,
      title: "",
      dictnetList: [], //节点类型
      netList: [], //连网方式
      // deviceGatewayTypeName: '',
      // networkWayName: ''
    };
  },
  computed: {
    deviceGatewayTypeName() {
      return (
        this.dictnetList.find(
          (item) => item.id === this.produceForm.deviceGatewayTypeId
        ) &&
        this.dictnetList.find(
          (item) => item.id === this.produceForm.deviceGatewayTypeId
        ).dictValue
      );
    },
    networkWayName() {
      let netList =
        this.dictnetList.find(
          (item) => item.id === this.produceForm.deviceGatewayTypeId
        ) &&
        this.dictnetList.find(
          (item) => item.id === this.produceForm.deviceGatewayTypeId
        ).children;

      return (
        netList.find((item) => item.id === this.produceForm.networkWayId) &&
        netList.find((item) => item.id === this.produceForm.networkWayId)
          .dictValue
      );
    },
  },
  created() {
    this.fn_get_product_detail();
    this.fn_get_dictnet_list();
    // console.log('string', this.descLength)
  },
  methods: {
    // 名称校验
    checkName(rule, value, callback) {
      if (!reg_two(value)) {
        return callback(
          new Error(
            "支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制 4~30个字符，中文及日文算 2 个字符"
          )
        );
      } else {
        callback();
      }
    },
    // 长度检验
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
      if (name === "description") {
        this.descTrue = value;
      }
    },
    // 节点类型
    fn_get_dictnet_list() {
      getDictNetList().then((res) => {
        if (res.code == 200) {
          this.dictnetList = res.data;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 产品详细信息
    fn_get_product_detail() {
      let params = {
        id: this.$route.query.id,
      };
      getProductDetail(params).then((res) => {
        // console.log(res,'%%%%');
        if (res.code == 200) {
          this.produceForm = res.data;
          // this.productDisableStatus =
          //   this.produceForm.productDisableStatus === 1 ? true : false
          // this.dynamicRegisterAllowed =
          //   this.produceForm.dynamicRegisterAllowed === 1 ? true : false
          // this.autoRegisterAllowed =
          //   this.produceForm.autoRegisterAllowed === 1 ? true : false
          let data = {
            id: res.data.id,
            title: res.data.name,
            productKey: res.data.productKey,
          };
          this.$store.dispatch("setLayoutInfo", data);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 产品设置
    fn_set_product_config() {
      // console.log(this.produceForm)
      let data = {
        productKey: this.produceForm.productKey,
        isAutoRegister: this.produceForm.autoRegisterAllowed,
        isDynamicRegister: this.produceForm.dynamicRegisterAllowed,
        isEnable: this.produceForm.productDisableStatus,
      };
      setProductConfig(data).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
          this.fn_get_product_detail();
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 编辑产品弹窗
    fn_open() {
      this.title = "编辑产品";
      this.visible = true;
    },
    fn_sure() {
      this.$refs["produceForm"].validate((valid) => {
        if (valid) {
          let loading = this.$loading({
            lock: true,
            text: "loading...",
            spinner: "el-icon-loading",
            background: "rgba(255, 255, 255, 0.9)",
            target: document.querySelector(".iot-dialog-content"),
          });
          getProductUpdate(this.produceForm)
            .then((res) => {
              if (res.code == 200) {
                this.$newNotify.success({
                  message: res.message,
                });
                // console.log('这里也要改',this.$route)
                this.fn_get_product_detail();
                this.$router.replace({
                  path: "/productDetail",
                  query: {
                    id: this.$route.query.id,
                    title: escape(this.produceForm.name),
                    productKey: this.produceForm.productKey,
                  },
                });
                this.visible = false;
              } else {
                this.$newNotify.error({
                  message: res.message,
                });
              }
            })
            .finally(() => {
              loading.close();
            });
        } else {
          return false;
        }
      });
    },
    fn_isdisable() {
      this.fn_set_product_config();
    },
    // 动态注册
    fn_isregister(val) {
      if (!val) {
        this.produceForm.autoRegisterAllowed = false;
      }
      this.fn_set_product_config();
    },
    // 自动注册
    fn_isauto() {
      this.fn_set_product_config();
    },
  },
  watch: {
    visible(val) {
      if (!val) {
        this.$refs["produceForm"] && this.$refs["produceForm"].resetFields();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.info {
  padding: 18px 0;
  .info-title {
    display: flex;
    justify-content: space-between;
    .info-edit {
      cursor: pointer;
      display: flex;
      align-items: center;
      font-size: 14px;
      font-family: H_Regular;
      img {
        width: 12px;
        height: 12px;
        margin-right: 6px;
      }
    }
  }
  .info-content {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    padding: 20px 32px;
    .info-row {
      .item {
        flex: 1;
        padding-top: 20px;
        width: 50%;
        display: flex;
        height: 100%;
        font-size: 14px;
        font-weight: 400;
        span {
          height: 16px;
          line-height: 16px;
          display: block;
          &:first-child {
            color: #999999;
            width: 100px;
            text-align: right;
            flex-shrink: 0;
          }
          &:last-child {
            // width: 100%;
            flex: 1;
            color: #515151;
            margin-left: 48px;
          }
        }
        .item-span {
          padding-right: 22px;
          width: calc(100% - 100px - 22px);
          color: #515151;
          margin-left: 48px;
          word-wrap: break-word;
        }
      }
    }
  }
  .info-config {
    box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
    border-radius: 2px;
    margin-top: 18px;
    padding: 20px 32px;
    .info-title {
      padding-bottom: 20px;
    }
    .info-config-content {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .flex {
        align-items: center;
      }
      .info-config-key {
        color: #999999;
        font-size: 14px;
        padding: 0 48px 0 34px;
      }
    }
    .config-text {
      font-size: 14px;
      margin-left: 6px;
      color: #515151;
      font-weight: normal;
    }
  }
  .produceForm {
    .el-form-tips {
      margin-top: -17px;
      margin-bottom: 0;
    }
  }
  :deep(.el-form-item) {
    margin-bottom: 17px !important;
  }
}
</style>
