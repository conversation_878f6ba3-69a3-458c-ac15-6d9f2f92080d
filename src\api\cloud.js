import request from "./index";
import { BASE_SERVER } from "../conf/env";
const baseServer = BASE_SERVER;
const mqttUrl = baseServer;
const dictionary_core = `${baseServer}/dict/child-list`;

/**
 * @desc 创建密钥对
 * @params
 * @returns
 */
export const addSecretKey = (data) => {
  return request({
    url: `${mqttUrl}/secretKey/save`,
    method: "post",
    data,
  });
};

/**
 * @desc 查询密钥对
 * @params
 * @returns
 */
export const getSecretKey = (params) => {
  return request({
    url: `${mqttUrl}/secretKey/query`,
    method: "get",
    params,
  });
};

/**
 * @desc 创建密钥对
 * @params
 * @returns
 */
export const updateSecretKey = (data) => {
  return request({
    url: `${mqttUrl}/secretKey/update`,
    method: "put",
    data,
  });
};

/**
 * @desc 创建密钥对
 * @params
 * @returns
 */
export const deleteSecretKey = (params) => {
  return request({
    url: `${mqttUrl}/secretKey/delete`,
    method: "delete",
    params,
  });
};
/**
 * @desc mqtt订阅列表
 * @params
 * @returns
 */
export const mqttSubscriberList = (params) => {
  return request({
    url: `${mqttUrl}/mqtt/subscriber/list`,
    method: "get",
    params,
  });
};

/**
 * @desc mqtt订阅列表  下  事件列表 （字典）
 * @params
 * @returns
 */
export const mqttEventList = (params) => {
  return request({
    url: `${dictionary_core}?parentId=1480456517764616194`,
    method: "get",
    params,
  });
};

/**
 * @desc mqtt订阅列表  事件更新
 * @params
 * @returns
 */
export const mqttEventUpdate = (data) => {
  return request({
    url: `${mqttUrl}/mqtt/subscriber/update`,
    method: "post",
    data,
  });
};

/**
 * @desc mqtt订阅列表  状态更新
 * @params
 * @returns
 */
export const mqttEventState = (data) => {
  return request({
    url: `${mqttUrl}/mqtt/subscriber/update/state`,
    method: "post",
    data,
  });
};
