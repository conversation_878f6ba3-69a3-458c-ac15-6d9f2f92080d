<template>
  <el-dialog
    class="add"
    :title="title"
    :visible.sync="dialogVisible"
    top="10vh"
    width="728px"
    :append-to-body="true"
    :close-on-click-modal="false"
    :before-close="handleClose"
  >
    <div class="content">
      <div class="form">
        <el-form
          ref="form"
          :model="form"
          :rules="rules"
          @validate="fn_validate"
        >
          <el-form-item v-if="!type" prop="">
            <div class="form-item">
              <p class="form-item-label">功能分类 <span>*</span></p>
              <el-radio-group v-model="form.classification" size="medium">
                <el-radio-button :label="0">属性</el-radio-button>
                <el-radio-button :label="1">事件</el-radio-button>
                <el-radio-button :label="2">服务</el-radio-button>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item prop="name">
            <div class="form-item">
              <p class="form-item-label">功能名称 <span>*</span></p>
              <el-input
                v-model="form.name"
                type="text"
                placeholder="请输入功能名称"
              ></el-input>
            </div>
          </el-form-item>
          <div class="el-form-tips" v-if="nameTrue">
            支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点；必须以中文、英文或数字开头，不超过
            30 个字符
          </div>
          <el-form-item prop="id">
            <div class="form-item">
              <p class="form-item-label">标识符 <span>*</span></p>
              <el-input
                v-model="form.id"
                type="text"
                placeholder="请输入标识符"
              ></el-input>
            </div>
          </el-form-item>
          <div class="el-form-tips" v-if="idTrue">
            支持英文、数字、下划线的组合，不超过 50 个字符
          </div>
          <!-- 属性/事件/服务 -->
          <attribute
            ref="attribute"
            v-if="form.classification == 0"
            :form="form"
            :enumTips="enumTips"
            :boolTips="boolTips"
            :objectTips="objectTips"
            :arrayTips="arrayTips"
            :unitList="unitList"
            @change="valChange"
            @addAttribute="addAttribute"
            @editAttribute="editAttribute"
            @deleteAttribute="deleteAttribute"
          />
          <event
            ref="event"
            v-if="form.classification == 1"
            :form="form"
            :outTips="outTips"
            @change="valChange"
            @addAttribute="addAttribute"
            @editAttribute="editAttribute"
            @deleteAttribute="deleteAttribute"
          />
          <service
            ref="service"
            v-if="form.classification == 2"
            :form="form"
            :inTips="inTips"
            :outTips="outTips"
            @change="valChange"
            @addAttribute="addAttribute"
            @editAttribute="editAttribute"
            @deleteAttribute="deleteAttribute"
          />
          <!--  -->
          <el-form-item prop="desc">
            <div class="form-item">
              <p class="form-item-label">功能描述</p>
              <el-input
                v-model="form.desc"
                type="textarea"
                :maxlength="100"
                placeholder="请输入功能描述"
              ></el-input>
            </div>
          </el-form-item>
          <div class="el-form-tips" v-if="idTrue">最多不超过200个字符</div>
        </el-form>
      </div>
    </div>
    <div class="footer flex">
      <p class="cancel" @click="handleClose">取消</p>
      <p class="submit" v-throttle="500" @click="handleSubmit">提交</p>
    </div>
    <child-add
      ref="childAdd"
      :mode="this.form.classification"
      :unitList="unitList"
      :reservedList="reservedList"
      @append="attributeAppend"
      @edit="attributeEdit"
    />
  </el-dialog>
</template>

<script>
import attribute from "./components/attribute";
import event from "./components/event";
import service from "./components/service";
import childAdd from "./components/child-add";
import { set as lodashSet, merge as lodashMerge } from "lodash";
import { productAbilityAdd } from "@/api/product.js";
import { getDictUnitList } from "@/api/dictionary";
// productAbilityEdit

import {
  reg_one,
  reg_three,
  reg_nine,
  reg_six,
  reg_five,
  reg_ten,
  reg_eleven,
  reg_twelve,
  eighteen,
  nineteen,
  twenty,
} from "@/util/util.js";
export default {
  data() {
    return {
      type: 0, //0 新增  1 修改
      title: "添加功能",
      form: {
        classification: 0, //功能分类
        name: "", //功能名称
        id: "", //标识符
        desc: "", //描述
        type: "info", //事件类型
        isSync: "async",
        // 属性
        data: {
          type: "int", //数据类型
          rules: {
            arrayType: "int",
            min: "",
            max: "",
            unit: "",
            unitName: "",
            size: "",
            step: "",
            length: "",
            item: {
              size: "",
              rules: [],
            },
          },
          enumList: [
            {
              isIntFocus: false,
              key: "",
              value: "",
            },
          ], // 枚举数据   需结构  默认有一项
          jsonData: [], //对象数据
          arrayData: [], //数组数据
        }, //
        mode: "rw", //只读  r 读   rw 读写
        // 事件  只有输出参数
        out: [],
        // 服务   有输入 和输出参数
        in: [],
      },
      rules: {
        name: [
          {
            required: true,
            // message: "请输入功能名称",
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        id: [
          {
            required: true,
            // message: '支持英文、数字、下划线的组合，不超过 50 个字符',
            trigger: "blur",
            validator: this.checkId,
          },
        ],
        mode: [{ required: true, message: "请选择", trigger: "blur" }],
        desc: [{ required: false, trigger: "blur" }],
        "data.type": [
          {
            required: true,
            message: "请选择数据类型",
            trigger: "blur",
          },
        ],
        "data.rules": [
          {
            required: false,
            message: "请选择数据类型",
            trigger: "blur",
          },
        ],
        "data.rules.min": [
          {
            required: true,
            // message: "请输入最小值",
            trigger: "blur",
            validator: this.checkMin,
          },
        ],
        "data.rules.max": [
          {
            required: false,
            message: "请输入最大值",
            trigger: "blur",
          },
        ],
        "data.rules.unit": [
          { required: false, message: "请选择单位", trigger: "blur" },
        ],
        "data.rules.size": [
          {
            required: false,
            // message: "请输入个数",
            trigger: "blur",
            validator: this.checkSize,
          },
        ],
        "data.rules.step": [
          {
            required: false,
            // message: "请输入步长",
            trigger: "blur",
            validator: this.checkStep,
          },
        ],
        "data.rules.length": [
          {
            required: true,
            // message: "请输入最小值",
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
        "data.rules.item": [
          { required: true, message: "请填写子集", trigger: "blur" },
        ],
        // 枚举 自定义字段进行判断
        // "data.enumList": [
        //   {
        //     required: true,
        //     // message: "请填写子集",
        //     trigger: "change",
        //     validator: this.checkEnum,
        //   },
        // ],
      },
      // 验证
      nameTrue: true,
      idTrue: true,
      enumTips: "",
      boolTips: "",
      objectTips: "",
      arrayTips: "",
      inTips: "",
      outTips: "",
      // ---------------------
      dialogVisible: false,
      thingModel: {
        profile: {
          productKey: "mec08bDde4rL6GzQ", //
          version: "1.0", // 添加版本号
        },
        properties: [], //属性
        events: [], //事件
        services: [], //服务
      }, ////物模型数据   回显数据来源---------------------------------------------------------重要
      nowIndex: "", //---------------------------------------  修改标识   重要
      oldId: "",
      unitList: [],
      reservedList: [
        "get",
        "set",
        "post",
        "prop",
        "property",
        "value",
        "time",
        "event",
      ], //id 保留字
    };
  },
  props: {
    productKey: {
      type: String,
    },
    tenant_id: {
      type: String,
    },
    UniqueCode: {
      type: String,
    },
  },
  watch: {
    productKey() {
      this.thingModel.profile.productKey = this.productKey;
    },
  },
  components: { attribute, event, service, childAdd },
  methods: {
    getUnit() {
      getDictUnitList().then((res) => {
        this.unitList = res.data.map((item) => {
          // let name = item.dictValue.split("/");
          // item.unitName = name[0];
          return item;
        });
      });
    },
    // 数据结构变化事件*---------------------------------------------------------
    // 数据组件 添加参数申请   经过data-type 组件的  json-struct 都会携带数据类型  {data,type}
    addAttribute(data) {
      let flag = false;
      let config = data;
      if (this.form.classification == 0) {
        //属性 中 对象结构申请增加或修改字段
        flag = true;
      } else {
        // 事件 服务
        flag = false;
      }
      this.$refs.childAdd.add(flag, config, this.form);
    },
    //数据组件 申请修改数据
    editAttribute(data) {
      let flag = false;
      if (this.form.classification == 0) {
        //属性 中 对象结构申请增加或修改字段
        flag = true;
      } else {
        // 事件    服务
        flag = false;
      }
      this.$refs.childAdd.edit(flag, data.data, data, this.form);
      // if(data.type == 'object'){
      //   this.$refs.childAdd.edit(flag, data.data);
      // }
    },
    deleteAttribute(data) {
      let name = "";
      if (this.form.classification == 0) {
        if (data.type == "object") {
          name = "jsonData";
        } else if (data.type == "array") {
          name = "arrayData";
        }
        this.form.data[name] = this.form.data[name].filter(
          (item) => item.id != data.data.id
        );
      } else {
        name = data.source;
        this.form[name] = this.form[name].filter(
          (item) => item.id != data.data.id
        );
      }
    },
    // 子组件 属性添加完成事件
    attributeAppend(data) {
      if (data.classification == 0) {
        // 属性       data.data 为添加的结构
        // ? 是否直接修改rules 类型  还是暂存 jsonData   目前暂存   提交时在做处理
        if (data.type == "array") {
          this.form.data.arrayData.push(data.data);
        } else if (data.type == "object") {
          this.form.data.jsonData.push(data.data);
        }
      } else {
        // 事件 服务  变量名
        this.form[data.source].push(data.data);
        if (data.source == "in") {
          //
          this.inTips = "";
        } else if (data.source == "out") {
          this.outTips = "";
        }
      }
      this.enumTips = "";
      this.boolTips = "";
      this.objectTips = "";
      this.arrayTips = "";
    },
    attributeEdit(data) {
      if (data.classification == 0) {
        // 属性       data.data 为添加的结构
        // ? 是否直接修改rules 类型  还是暂存 jsonData   目前暂存   提交时在做处理
        if (data.type == "array") {
          this.$set(this.form.data.arrayData, data.nowIndex, data.data);
        } else if (data.type == "object") {
          this.$set(this.form.data.jsonData, data.nowIndex, data.data);
        }
      } else {
        // 事件 服务  变量名
        this.$set(this.form[data.source], data.nowIndex, data.data);
      }
      // this.resetRules();
    },
    //   子项数值变换传递
    resetRules() {
      this.enumTips = "";
      this.boolTips = "";
      this.objectTips = "";
      this.arrayTips = "";
      this.form.data.rules = {
        arrayType: "int",
        min: "",
        max: "",
        unit: "",
        unitName: "",
        size: "",
        step: "",
        length: "",
        item: {
          size: "",
          rules: [],
        },
      };
    },
    valChange(data) {
      if (data.key == "data.type" || data.key == "data.rules.arrayType") {
        this.resetRules();
      }
      lodashSet(this.form, data.key, data.value);
      this.$refs.form.clearValidate();
      // this.form[data.key] = data.value;
    },

    // 表单基本事件----------------------------------------------------
    // ---------------------------------校验 start
    checkId(rule, value, callback) {
      if (!reg_nine(value)) {
        return callback(
          new Error("支持英文、数字、下划线的组合，不超过 50 个字符")
        );
      }
      if (this.reservedList.includes(value)) {
        return callback(new Error("是系统保留字段，不能用于标识符定义"));
      }
      // 功能标识符 产品下唯一     参数标识符 功能下唯一   ------- 此处为功能标识符 ！！！
      let flag;
      if (this.type && this.oldId == value) {
        // 修改  且新旧id相等
        if (this.oldId == value) {
          //
          flag = -1;
        }
        // if (this.form.classification == 0) {
        //   // 属性
        //   // nowIndex 用于修改时排除自己
        //   flag = this.thingModel.properties.findIndex(
        //     (item, index) => item.id == value && index !== this.nowIndex
        //   );
        // } else if (this.form.classification == 1) {
        //   // 事件
        //   flag = this.thingModel.events.findIndex(
        //     (item, index) => item.id == value && index !== this.nowIndex
        //   );
        // } else if (this.form.classification == 2) {
        //   // 服务
        //   flag = this.thingModel.services.findIndex(
        //     (item, index) => item.id == value && index !== this.nowIndex
        //   );
        // }
      } else {
        // 新增  合并 属性事件服务 查找是否存在相同标识
        flag = this.thingModel.properties
          .concat(this.thingModel.events)
          .concat(this.thingModel.services)
          .findIndex((item) => item.id == value);
      }

      if (flag !== -1) {
        //
        return callback(new Error("已存在该标识符"));
      }
      callback();
    },
    checkName(rule, value, callback) {
      if (value !== "") {
        let flag;
        // 属性
        if (this.form.classification == 1) {
          // 事件
          if (this.type) {
            flag = this.thingModel.events.findIndex(
              (item, index) => item.name == value && index !== this.nowIndex
            );
          } else {
            flag = this.thingModel.events.findIndex(
              (item) => item.name == value
            );
          }
          if (flag !== -1) {
            //
            return callback(new Error("同一类型下的参数名称不能重复"));
          }
        }
      }
      if (!reg_three(value)) {
        return callback(
          new Error(
            "支持中文、大小写字母、日文、数字、短划线、下划线、斜杠和小数点；必须以中文、英文或数字开头，不超过 30 个字符"
          )
        );
      } else {
        callback();
      }
    },
    checkMin(rule, value, callback) {
      if (value !== "" || this.form.data.rules.max !== "") {
        if (value !== "") {
          this.fn_checkMin(value, callback);
        }
        if (this.form.data.rules.max !== "") {
          this.fn_checkMin(this.form.data.rules.max, callback);
        }
        if (
          this.form.data.rules.max !== "" &&
          reg_five(value, this.form.data.rules.max)
        ) {
          return callback(new Error("最小值不得大于等于最大值"));
        }
        callback();
      } else if (this.form.data.rules.max !== "") {
        //
      } else {
        return callback();
      }
    },
    fn_checkMin(value, callback) {
      if (this.form.data.type == "int") {
        if (!/^-?[0-9]\d*$/.test(value)) {
          return callback(new Error("当前仅支持整数"));
        }
      } else {
        if (this.form.data.type == "float") {
          if (!eighteen(value)) {
            return callback(new Error("仅支持数字且小数点后不得超过7位"));
          }
        } else if (!nineteen(value)) {
          return callback(new Error("仅支持数字且小数点后不得超过16位"));
        }
      }
    },
    checkStep(rule, value, callback) {
      if (value !== "") {
        if (this.form.data.type == "int") {
          if (!/(^[1-9]\d*$)/.test(value)) {
            return callback(new Error("当前仅支持大于0的正整数"));
          }
        } else {
          if (!reg_eleven(value)) {
            return callback(new Error("当前仅支持正数"));
          }
          if (this.form.data.type == "float") {
            if (!eighteen(value)) {
              return callback(new Error("仅支持正数且小数点后不得超过7位"));
            }
          } else if (!nineteen(value)) {
            return callback(new Error("仅支持正数且小数点后不得超过16位"));
          }
        }
        if (this.form.data.rules.min && this.form.data.rules.max) {
          if (
            !reg_ten(
              0,
              Math.abs(this.form.data.rules.max) +
                Math.abs(this.form.data.rules.min),
              value
            )
          ) {
            return callback(new Error("超出取值范围"));
          } else {
            callback();
          }
        } else {
          callback();
        }
      } else {
        callback();
      }
    },
    checkEnum() {
      let flag = true;
      let newArr = [];
      this.enumTips = "";
      if (this.form.data.enumList && this.form.data.enumList.length == 0) {
        this.enumTips = "至少输入一项";
        flag = false;
      } else {
        // 判断项是否满足
        for (let i = 0; i < this.form.data.enumList.length; i++) {
          if (
            this.form.data.enumList[i].key == "" ||
            this.form.data.enumList[i].value == ""
          ) {
            this.enumTips = "数值与说明不能为空";
            flag = false;
            break;
          }
          if (!twenty(this.form.data.enumList[i].key)) {
            this.enumTips = "数值只能为数字";
            flag = false;
            break;
          }
          if (
            !this.form.data.enumList[i].value ||
            !reg_one(this.form.data.enumList[i].value)
          ) {
            this.enumTips = "说明不能为空且不能超过32个字符";
            flag = false;
            break;
          }
          // 判断是否重复
          if (newArr.indexOf(this.form.data.enumList[i].key) == -1) {
            newArr.push(this.form.data.enumList[i].key);
          }
        }
        if (!flag) return flag;
        if (newArr.length != this.form.data.enumList.length) {
          this.enumTips = "数值不能重复";
          flag = false;
        }
      }
      return flag;
    },
    // bool 验证
    checkBool() {
      this.boolTips = "";

      if (
        (this.form.data.rules["0"] == undefined ||
          this.form.data.rules["0"] == "") &&
        (this.form.data.rules["1"] == undefined ||
          this.form.data.rules["1"] == "")
      ) {
        this.boolTips = "布尔值的文字说明不能为空";
        return false;
      }

      if (
        this.form.data.rules["0"] == undefined ||
        this.form.data.rules["0"] == ""
      ) {
        this.boolTips = `缺少'否'含义的文字说明`;
        return false;
      }
      if (
        this.form.data.rules["0"] != "" &&
        !reg_one(this.form.data.rules["0"])
      ) {
        this.boolTips = "'否'的说明不能为空且不能超过32个字符";
        return false;
      }
      if (
        this.form.data.rules["1"] == undefined ||
        this.form.data.rules["1"] == ""
      ) {
        this.boolTips = `缺少'是'含义的文字说明`;
        return false;
      }
      if (
        this.form.data.rules["1"] != "" &&
        !reg_one(this.form.data.rules["1"])
      ) {
        this.boolTips = "'是'的说明不能为空且不能超过32个字符";
        return false;
      }
      return true;
    },
    checkLength(rule, value, callback) {
      if (value !== "") {
        if (!reg_twelve(value)) {
          //
          return callback(new Error("取值范围为1~1000的正整数"));
        }
      }
      callback();
    },
    checkSize(rule, value, callback) {
      if (value != "") {
        if (!reg_six(value)) {
          //
          return callback(new Error("只能输入正整数"));
        } else if (value > 100) {
          return callback(new Error("不得大于100"));
        }
      }
      callback();
    },
    // --------------------------------------------校验 end
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "id") {
        this.idTrue = value;
      } else if (name === "name") {
        this.nameTrue = value;
      }
    },
    edit(data, model) {
      // 接收  功能类型级下标
      let record = {};
      this.type = 1;
      this.title = "修改功能";
      if (Object.prototype.toString.call(model) === "[object Object]") {
        // 模型存在
        this.thingModel = JSON.parse(JSON.stringify(model)); //保存模型
      } else {
        console.log("模型数据结构异常");
      }
      this.oldId = data.id;
      if (data.classification == 0) {
        // 属性    修改时 根据标识符  查找数据  并保存下标
        this.nowIndex = this.thingModel.properties.findIndex(
          (item) => item.id == data.id
        );
        record = JSON.parse(JSON.stringify(model.properties[this.nowIndex]));
        record.data = this.formatMdeol(record.data);
      } else if (data.classification == 1) {
        // 事件
        this.nowIndex = this.thingModel.events.findIndex(
          (item) => item.id == data.id
        );
        record = JSON.parse(JSON.stringify(model.events[this.nowIndex]));
        record.out.map((item) => {
          item.data = this.formatMdeol(item.data);
          return item;
        });
      } else if (data.classification == 2) {
        // 服务
        this.nowIndex = this.thingModel.services.findIndex(
          (item) => item.id == data.id
        );
        record = JSON.parse(JSON.stringify(model.services[this.nowIndex]));
        this.form.isSync = record.type; ////  服务 type 字段与事件type字段重复   新建字段区分
        record.out = record.out.map((item) => {
          item.data = this.formatMdeol(item.data);
          return item;
        });
        record.in = record.in.map((item) => {
          item.data = this.formatMdeol(item.data);
          return item;
        });
      }
      this.$nextTick(() => {
        this.form = lodashMerge(this.form, record);
      });
      this.open();
    },
    add(model) {
      // 设置模型数据
      if (
        Object.prototype.toString.call(model) === "[object Object]" &&
        Object.keys(model).length > 0
      ) {
        this.thingModel = JSON.parse(JSON.stringify(model));
      }
      this.type = 0;
      this.title = "添加功能";
      this.open();
    },
    open() {
      this.getUnit();
      this.dialogVisible = true;
    },
    close() {
      this.nameTrue = true;
      this.idTrue = true;
      this.dialogVisible = false;
    },
    handleClose() {
      // 清空数据
      this.handleReset();
      try {
        this.$refs.form.resetFields(); //resetFields 不能使用  表单存在未使用的值会报错 只能手动清除值  clearValidate
      } catch {
        //
      }
      // console.log(this.form);
      this.close();
    },
    // 回显处理数据   主要是复合类型的解构
    formatMdeol(data) {
      let object = data || {};
      let newData = {};
      if (object.type == "enum") {
        object.enumList = [];
        for (let i in object.rules) {
          object.enumList.push({
            key: i,
            value: object.rules[i],
            isIntFocus: false,
          });
        }
      } else if (object.type == "object") {
        object.jsonData = [];
        for (let j = 0; j < object.rules.length; j++) {
          object.rules[j].data = this.formatMdeol(object.rules[j].data);
          object.jsonData.push(object.rules[j]);
        }
      } else if (object.type == "array") {
        object.arrayData = [];
        object.rules.arrayType = object.rules.item.type;
        if (object.rules.item.type == "object") {
          for (let k = 0; k < object.rules.item.rules.length; k++) {
            object.rules.item.rules[k].data = this.formatMdeol(
              object.rules.item.rules[k].data
            );
            object.arrayData.push(object.rules.item.rules[k]);
          }
        }
      }
      newData = object;
      return newData;
    },
    // 提交处理数据
    formaterData(data) {
      let object = JSON.parse(JSON.stringify(data));
      let newData = new Object();
      if (!object.type) {
        return {};
      }
      let { min, max, unit, unitName, step, length, size } = object.rules;
      // console.log("size", size);
      newData["type"] = object.type;
      newData["rules"] = {};
      if (
        object.type === "int" ||
        object.type === "float" ||
        object.type === "double"
      ) {
        //
        newData["rules"]["min"] = min;
        newData.rules["max"] = max;
        newData.rules["unit"] = unit;
        newData.rules["unitName"] = unitName;
        newData.rules["step"] = step;
      } else if (object.type === "enum") {
        //
        if (object.enumList && object.enumList.length > 0) {
          for (let i = 0; i < object.enumList.length; i++) {
            if (object.enumList[i].key) {
              newData.rules[object.enumList[i].key] = object.enumList[i].value;
            }
          }
        }
      } else if (object.type === "bool") {
        //
        newData.rules["0"] = object.rules["0"];
        newData.rules["1"] = object.rules["1"];
      } else if (object.type === "text") {
        //
        newData.rules["length"] = length;
      } else if (object.type === "time") {
        //
        newData.rules = {};
      } else if (object.type === "object") {
        //
        newData.rules = [];
        for (let m = 0; m < object.jsonData.length; m++) {
          newData.rules.push({
            name: object.jsonData[m].name,
            id: object.jsonData[m].id,
            data: this.formaterData(object.jsonData[m].data),
          });
        }
      } else if (object.type === "array") {
        newData.rules.item = {};
        newData.rules.item["type"] = object.rules.arrayType;
        if (object.rules.arrayType !== "object") {
          newData.rules["item"].type = object.rules.arrayType;
          newData.rules["size"] = size.toString() || "100";
          newData.rules["item"].rules = {};
        } else {
          newData.rules["item"].rules = [];
          newData.rules["size"] = size.toString() || "100"; //object.arrayData.length.toString();
          for (let j = 0; j < object.arrayData.length; j++) {
            newData.rules["item"].rules.push({
              name: object.arrayData[j].name,
              id: object.arrayData[j].id,
              data: this.formaterData(object.arrayData[j].data),
            });
          }
        }
      }
      return newData;
    },
    verifySubmit() {
      this.objectTips = "";
      this.arrayTips = "";
      this.inTips = "";
      this.outTips = "";
      if (this.form.classification == 0) {
        if (this.form.data.type == "enum") {
          // enum 校验
          if (!this.checkEnum()) return false; // 不通过 退出
        } else if (this.form.data.type == "bool") {
          // bool 校验
          if (!this.checkBool()) return false; // 不通过 退出
        } else if (this.form.data.type == "object") {
          if (this.form.data.jsonData.length == 0) {
            //
            this.objectTips = "至少输入一项";
            return false;
          }
        } else if (this.form.data.type == "array") {
          if (this.form.data.rules.arrayType == "object") {
            if (this.form.data.arrayData.length == 0) {
              //
              this.arrayTips = "至少输入一项";
              return false;
            }
          }
        }
      } else if (this.form.classification == 1) {
        //
        if (this.form.out.length == 0) {
          this.outTips = "至少输入一项";
          return false;
        }
      } else if (this.form.classification == 2) {
        //  2022-3-14   新增服务 入参 出参允许为空
        // if (this.form.in.length == 0) {
        //   this.inTips = "至少输入一项";
        //   return false;
        // }
        // if (this.form.out.length == 0) {
        //   this.outTips = "至少输入一项";
        //   return false;
        // }
      }
      return true;
    },
    handleSubmit() {
      // 提交整个物模型   将修改的 属性 / 事件  / 服务 重新组装成物模型
      // console.log(JSON.parse(JSON.stringify(this.form)));
      this.$refs.form.validate((valid) => {
        if (!this.verifySubmit()) return;
        if (valid) {
          let params = {
            tenantId: this.tenant_id,
            productKey: this.productKey,
          };
          //设置productKey
          this.thingModel.profile.productKey = this.productKey;
          // 处理功能
          let formatData = {};
          formatData["id"] = this.form.id;
          formatData["name"] = this.form.name;
          formatData["desc"] = this.form.desc || "";
          if (this.form.classification == 0) {
            // 属性
            formatData["mode"] = this.form.mode;
            formatData["data"] = this.formaterData(this.form.data);
            if (this.type) {
              // 修改  通过id 标识符 查找替换
              this.thingModel.properties[this.nowIndex] = formatData;
            } else {
              this.thingModel.properties.push(formatData);
            }
            //2022-6-18 start    事件/服务 中保存的属性数据 不允许存在mode 字段  服务中的 属性设置 不允许设置只读属性
            // 转存一份属性数据   不包含 mode 参数   给事件和服务使用
            let copyProperties = JSON.parse(
              JSON.stringify(this.thingModel.properties)
            );
            // 转存一份属性数据 不包含mode 且 不包含mode === 'r' (只读) 的属性数据
            let noModeData = copyProperties.filter((item) => item.mode !== "r");
            // 删除 mode 属性
            copyProperties = copyProperties.map((item) => {
              delete item.mode;
              delete item.classification;
              return item;
            });
            noModeData = noModeData.map((item) => {
              delete item.mode;
              delete item.classification;
              return item;
            });
            //2022-6-18 end

            // 更新默认事件   属性上报    post
            let defaultPostIndex = this.thingModel.events.findIndex(
              (item) => item.id === "post"
            );
            // 设置默认服务    get  set
            let defaultGetIndex = this.thingModel.services.findIndex(
              (item) => item.id == "get"
            );
            let defaultSetIndex = this.thingModel.services.findIndex(
              (item) => item.id == "set"
            );
            if (defaultPostIndex != -1) {
              // 已有默认事件
              this.thingModel.events[defaultPostIndex].out = copyProperties;
            } else {
              // 不存在
              this.thingModel.events.push({
                id: "post",
                name: "属性上报",
                type: "info",
                action: "thing.event.property.post",
                out: copyProperties,
              });
            }
            let servicesIn = copyProperties.map((item) => item.id);
            if (defaultGetIndex != -1) {
              this.thingModel.services[defaultGetIndex].in = servicesIn;
              this.thingModel.services[defaultGetIndex].out = copyProperties;
            } else {
              this.thingModel.services.push({
                id: "get",
                name: "属性获取",
                type: "async",
                action: "thing.service.property.get",
                in: servicesIn,
                out: copyProperties,
              });
            }
            if (defaultSetIndex != -1) {
              // 已有默认set
              this.thingModel.services[defaultSetIndex].in = noModeData;
              // this.thingModel.services[defaultSetIndex].out =
              //   this.thingModel.properties;
            } else {
              this.thingModel.services.push({
                id: "set",
                name: "属性设置",
                type: "async",
                action: "thing.service.property.set",
                in: noModeData,
                out: [], //this.thingModel.properties
              });
            }
          } else if (this.form.classification == 1) {
            // 事件
            formatData["type"] = this.form.type;
            formatData["action"] = `thing.event.${this.form.id}.post`; //固定写法
            formatData["out"] = this.form.out.map((item) => {
              item.data = this.formaterData(item.data);
              return item;
            });

            if (this.type) {
              // 修改  通过id 标识符 查找替换
              this.thingModel.events[this.nowIndex] = formatData;
            } else {
              this.thingModel.events.push(formatData);
            }
          } else if (this.form.classification == 2) {
            // 服务
            formatData["action"] = `thing.service.${this.form.id}.call`; //固定写法
            formatData["type"] = this.form.isSync; //固定写法
            formatData["out"] = this.form.out.map((item) => {
              item.data = this.formaterData(item.data);
              return item;
            });
            formatData["in"] = this.form.in.map((item) => {
              item.data = this.formaterData(item.data);
              return item;
            });
            if (this.type) {
              // 修改  通过id 标识符 查找替换
              this.thingModel.services[this.nowIndex] = formatData;
            } else {
              this.thingModel.services.push(formatData);
            }
          }
          //  编码设计原因  在此清除  classification  属性   物模型结构 不存在此字段  -------------后续优化   优先将此字段 抽离
          // 临时处理  将此字段手动删除
          this.thingModel.events = this.thingModel.events.map((item) => {
            delete item.classification;
            return item;
          });
          this.thingModel.properties = this.thingModel.properties.map(
            (item) => {
              delete item.classification;
              return item;
            }
          );
          this.thingModel.services = this.thingModel.services.map((item) => {
            delete item.classification;
            return item;
          });
          //
          params["thingModel"] = JSON.stringify(this.thingModel);
          productAbilityAdd(params, {
            uniqueCode: this.UniqueCode,
          }).then((res) => {
            if (res.code == 7002) {
              this.$newNotify.error({
                message: res.message,
              });
            } else if (res.code == 200) {
              if (this.type) {
                // 编辑
                this.$newNotify.success({
                  message: "修改模型成功",
                });
              } else {
                this.$newNotify.success({
                  message: "新增模型成功",
                });
              }
              this.handleClose();
              this.$emit("ok");
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
        } else {
          return false;
        }
      });
    },
    //  关闭弹窗清空     resetRules 为切换清空
    handleReset() {
      this.form.in = [];
      this.form.out = [];
      this.form.desc = ``;
      this.form.isSync = "async";
      this.form.data.type = "int";
      // 关闭时需重置rules  rules 在提交时结构会出现变化 由于此处为清空导致下次的基本类型 出现异常   问题编号 1345
      this.resetRules();
      this.form.data.enumList = [
        {
          isIntFocus: false,
          key: "",
          value: "",
        },
      ];
      this.form.data.jsonData = [];
      this.form.data.arrayData = [];
      this.form.classification = 0;
    },
  },
};
</script>

<style lang="scss" scoped>
.add {
  .content {
    height: 70vh;
    overflow: auto;
    padding: 22px 32px;
    border-radius: 0px 0px 2px 2px;
    .form {
      .form-item {
        .form-item-label {
          color: #515151;
          font-size: 14px;
          line-height: 14px;
          padding: 6px 0 8px 0;
          span {
            color: #ff2836;
          }
        }
        /deep/ {
          .el-radio-button__inner {
            width: 110px;
          }
          .el-input__inner {
            border-radius: 0;
            height: 34px;
          }
          .el-textarea__inner {
            border-radius: 0;
            height: 100px;
            color: #515151;

            font-family: H_Medium;
          }
          .el-textarea__inner::placeholder {
            font-family: H_Regular;
            font-weight: normal;
          }
        }
      }
      /deep/ .el-form-item {
        margin-bottom: 17px;
      }
    }
  }
  .footer {
    height: 58px;
    align-items: center;
    justify-content: flex-end;
    background: #fbfbfb;
    border-radius: 0px 0px 2px 2px;
    padding: 0 32px;
    p {
      width: 96px;
      height: 34px;
      border: 1px solid #eeeff1;
      text-align: center;
      line-height: 34px;
      font-size: 14px;
      margin-left: 18px;
      color: #333333;
      cursor: pointer;
      background: #ffffff;
    }
    .submit {
      border: none;
      color: #ffffff;
      background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
    }
  }
  /deep/ {
    .el-dialog__header {
      height: 58px;
      border-bottom: 1px solid #eeeff1;
      font-family: H_Medium;
      padding: 16px 20px 14px;
      .el-dialog__close {
        font-size: 18px;
        color: #515151;
        font-weight: bold;
      }
    }
    .el-dialog__body {
      padding: 0;
    }
    .el-dialog__footer {
      display: none;
    }
  }
}

//
.el-form-tips {
  font-size: 12px;
  font-weight: 400;
  color: #999999;
  margin-top: -16px;
  // margin-bottom: 9px;
}
</style>
