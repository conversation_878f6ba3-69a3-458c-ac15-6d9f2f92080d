<template>
  <div class="update-secret">
    <iot-form>
      <template #default>
        <el-form class="secret-form"
                 ref="secretForm"
                 :label-position="'top'"
                 :model="infoForm"
                 :rules="rules"
                 label-width="80px">
          <el-form-item label="输入旧密码"
                        prop="oldSecret">
            <el-input v-show="false"
                      name="oldSecret"
                      type="text"></el-input>
            <el-input name="oldSecret"
                      v-model="infoForm.oldSecret"
                      placeholder="输入旧密码"
                      show-password
                      autocomplete="off"></el-input>
          </el-form-item>
          <el-form-item label="输入新密码"
                        prop="newSecret">
            <div class="form-item">
              <!-- <p><span>*</span>设置密码</p> -->
              <el-tooltip class="item"
                          effect="light"
                          placement="right"
                          popper-class="update-info-tooltip">
                <div slot="content"
                     class="tips-password">
                  <p class="flex">
                    <img v-if="passwordTips.length"
                         src="~@/assets/images/index/right-icon.png"
                         alt="" />
                    <img v-else
                         src="~@/assets/images/index/wrong-icon.png"
                         alt="" />
                    <span>密码长度至少6位,最多14位；</span>
                  </p>
                  <p class="flex">
                    <img v-if="passwordTips.repeat"
                         src="~@/assets/images/index/right-icon.png"
                         alt="" />
                    <img v-else
                         src="~@/assets/images/index/wrong-icon.png"
                         alt="" />
                    <span> 密码不能与用户名或旧密码相同；</span>
                  </p>
                  <p class="flex">
                    <img v-if="passwordTips.verify"
                         src="~@/assets/images/index/right-icon.png"
                         alt="" />
                    <img v-else
                         src="~@/assets/images/index/wrong-icon.png"
                         alt="" />
                    <span>密码只能包含数字、字母和符号（除空格）；</span>
                  </p>
                  <p class="flex">
                    <img v-if="passwordTips.double"
                         src="~@/assets/images/index/right-icon.png"
                         alt="" />
                    <img v-else
                         src="~@/assets/images/index/wrong-icon.png"
                         alt="" />
                    <span>字母、数字和符号至少包含两种；</span>
                  </p>
                </div>
                <!-- <el-input
									v-show="false"
									name="newSecret"
									type="password"
								></el-input> -->
                <el-input name="newSecret"
                          v-model="infoForm.newSecret"
                          placeholder="输入新密码"
                          show-password
                          autocomplete="new-password"></el-input>
              </el-tooltip>
            </div>
          </el-form-item>
          <el-form-item label="确认新密码"
                        prop="confirmSecret">
            <div class="form-item">
              <!-- <p><span>*</span>确认密码</p> -->
              <el-tooltip class="item"
                          effect="light"
                          content=" · 需与密码一致"
                          placement="right"
                          popper-class="update-info-tooltip">
                <el-input v-model="infoForm.confirmSecret"
                          placeholder="确认新密码"
                          show-password></el-input>
              </el-tooltip>
            </div>
          </el-form-item>
          <iot-button class="btn-style"
                      text="确认修改"
                      @search="fn_confirm_update"></iot-button>
        </el-form>
      </template>
    </iot-form>
    <confirm ref="confirm"
             :isConfirm="false"
             title="成功修改密码" />
  </div>
</template>
<script>
import IotForm from '@/components/iot-form'
import IotButton from '@/components/iot-button'
import { mofidyPassword } from '@/api/user'
import { mapGetters } from 'vuex'
import md5 from 'js-md5'
import confirm from '@/views/reset/components/confirm'
export default {
  name: 'updateSecret',
  components: {
    IotForm,
    IotButton,
    confirm,
  },
  data() {
    const reg3 = /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)/ //.{6,}$
    const reg4 = new RegExp('[\\u4E00-\\u9FFF]+', 'g')
    const reg1 = /^.{6,14}$/ //至少6位
    const oldPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请输入旧密码'))
      } else {
        if (!reg1.test(value)) {
          callback(new Error('旧密码输入有误，请输入正确的旧密码'))
        } else if (!reg3.test(value) || reg4.test(value)) {
          callback(new Error('旧密码输入有误，请输入正确的旧密码'))
        } else {
          callback()
        }
      }
    }
    const password = (rule, value, callback) => {
      let flag = true
      // let reg1 = /^.{6,14}$/; //至少6位
      // let reg2 = /s/; ///^\S/; //
      // let reg3 = /^(?![0-9]+$)(?![a-z]+$)(?![A-Z]+$)(?!([^(0-9a-zA-Z)])+$)/; //.{6,}$
      // let reg4 = /.*[\u4e00-\u9fa5]+.*$/;
      // let reg4 = new RegExp("[\\u4E00-\\u9FFF]+", "g");

      if (value === '') {
        this.passwordTips.length = false
        this.passwordTips.verify = false
        this.passwordTips.double = false
        this.passwordTips.repeat = false
        callback(new Error('请输入密码'))
      } else {
        if (value == this.userName || value == this.infoForm.oldSecret) {
          this.passwordTips.repeat = false
        } else {
          this.passwordTips.repeat = true
        }
        if (!reg1.test(value)) {
          // 长度正则
          this.passwordTips.length = false
        } else {
          this.passwordTips.length = true
        }
        if (value.indexOf(' ') >= 0) {
          // !reg2.test(value)
          this.passwordTips.verify = false
        } else {
          this.passwordTips.verify = true
        }
        if (!reg3.test(value) || reg4.test(value)) {
          // 不能是纯数组  / 纯字母  / 纯字符
          this.passwordTips.double = false
        } else {
          this.passwordTips.double = true
        }
        for (let i in this.passwordTips) {
          if (!this.passwordTips[i]) {
            flag = false
          }
        }
        if (flag) {
          callback()
        } else {
          callback(new Error('密码输入不正确，请输入符合要求的密码'))
        }
      }
    }
    const verifyPassword = (rule, value, callback) => {
      if (value === '') {
        callback(new Error('请再次输入密码'))
      } else if (value != this.infoForm.newSecret) {
        callback(new Error('两次密码不一致'))
      } else {
        callback()
      }
    }
    return {
      infoForm: {
        oldSecret: '',
        newSecret: '',
        confirmSecret: '',
      },
      rules: {
        oldSecret: [
          { required: true, trigger: 'blur', validator: oldPassword },
        ],
        newSecret: [
          { required: true, trigger: 'change', validator: password },
          {
            min: 6,
            max: 14,
            trigger: 'change',
            message: '密码最少6位,最多14位',
          },
        ],
        confirmSecret: [
          {
            required: true,
            trigger: 'blur',
            validator: verifyPassword,
          },
        ],
      },
      passwordTips: {
        length: false,
        repeat: false,
        verify: false,
        double: false,
      },
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    userName() {
      return this.userInfo.account
    },
  },
  watch: {},
  methods: {
    // 确认修改密码
    fn_confirm_update() {
      this.$refs.secretForm.validate((valid) => {
        if (valid) {
          let data = {
            oldPassword: md5(this.infoForm.oldSecret),
            newPassword: md5(this.infoForm.newSecret),
            newPassword1: md5(this.infoForm.confirmSecret),
          }
          mofidyPassword(data).then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              })
              window.localStorage.clear()
              this.$refs.confirm.open()
            } else if (res.code != 200) {
              this.$newNotify.error({
                message: res.message,
              })
            }
          })
        } else {
          return false
        }
      })
    },
  },
}
</script>
<style lang="scss" scoped>
.update-secret {
  .secret-form {
    :deep(.el-form-item) {
      margin-bottom: 17px;
      width: 50%;
      &:first-child {
        margin-top: 11px;
      }
    }
    .el-upload__tip {
      color: #999999;
      font-size: 12px;
    }
    .form-item {
      align-items: center;
      p {
        font-size: 14px;
        line-height: 16px;
        color: #262626;
        padding-bottom: 8px;
        span {
          color: #f53e3e;
        }
      }
    }
    .btn-style {
      margin-top: 15px;
    }

    :deep(.el-input__inner) {
      font-family: 'Courier New', Courier, monospace;
    }

    :deep(.el-form-item__error) {
      color: #f53e3e;
      font-size: 14px;
    }
  }
}
</style>
<style lang="scss">
.update-info-tooltip {
  border-radius: 0;
  border: 1px solid #e4e7ec !important;
  backdrop-filter: blur(4px) !important;
  background: #ffffff;
  padding: 14px 18px;
  .tips-password {
    p {
      // align-items: center;
      padding-bottom: 8px;
      // font-family: H_Medium;
      img {
        width: 14px;
        height: 14px;
        margin-top: 1px;
      }
      span {
        margin-left: 8px;
        color: #515151;
        font-size: 13px;
      }
      &:last-child {
        padding-bottom: 0;
      }
    }
    :deep(.el-input__inner) {
      font-family: 'Courier New', Courier, monospace;
    }
  }
}
</style>
