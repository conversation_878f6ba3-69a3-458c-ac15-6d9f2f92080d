<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-02-21 14:45:54
-->
<template>
  <div class="produce">
    <div class="product_bg">
      <h3 class="bg_title">产品建模</h3>
      <span class="bg_desc">产品是一组具有相同功能定义的设备集合，平台提供设备接入，通过产品建模（物模型）方式，快速构建这类产品（设备集合）功能定义，包括产品基本描述、属性、事件、服务。</span>
    </div>
    <div class="produce-top">
      <div class="top-right">
        <!-- 搜索栏 -->
        <form-search
          defaultId="1"
          :options="searchOptions"
          @search="handleSearch"
          @clear="fn_clear_search_info"
          :selectHolder="selectHolder"
          :inputHolders="inputHolders"
          style="margin-right: -18px;"
        />
      </div>
      <div class="top-left" :data-aaa="$route">
        <iot-button text="重置" type="grey" @search="fn_clear_search_info" style="margin: 0px 12px 0px 20px ;"/>
        <iot-button text="创建产品" @search="fn_open" />
      </div>
    </div>

    <div class="produce-table">
      <!-- 表格 -->
      <iot-table :columns="columns" :data="tableData" :loading="loading">
        <template slot="projectName" slot-scope="{ row }">
          <div class="table-longText">
            <p v-if="calcul_long_text(row.projectName) <= 29">
              {{ row.projectName }}
            </p>
            <el-popover
              v-if="calcul_long_text(row.projectName) > 29"
              placement="top-start"
              width="400"
              trigger="hover"
              :content="row.projectName"
              popper-class="event-tooltip"
            >
              <p slot="reference">
                {{ row.projectName }}
              </p>
            </el-popover>
          </div>
        </template>
        <template slot="name" slot-scope="{ row }">
          <div class="table-longText">
            <p v-if="calcul_long_text(row.name) <= 25">
              {{ row.name }}
            </p>
            <el-popover
              v-if="calcul_long_text(row.name) > 25"
              placement="top-start"
              width="400"
              trigger="hover"
              :content="row.name"
              popper-class="event-tooltip"
            >
              <p slot="reference">
                {{ row.name }}
              </p>
            </el-popover>
          </div>
        </template>
        <template slot="deviceGatewayTypeId" slot-scope="{ row }">
          <span>{{
            row.deviceGatewayTypeId | deviceGatewayTypeName(that)
          }}</span>
        </template>
        <!-- <template slot="status" slot-scope="{ row }">
                    <div class="table-status">
                        <div class="status flex" v-if="row.status == 1">
                        <div class="red"></div>
                        <div>离线</div>
                        </div>
                        <div class="status flex" v-if="row.status == 5">
                        <div class="green"></div>
                        <div>在线</div>
                        </div>
                        <div class="status flex" v-if="row.status == 4">
                        <div class="yellow"></div>
                        <div>未激活</div>
                        </div>
                    </div>
          </template> -->
        <template slot="operation" slot-scope="{ row }">
          <div class="flex table-edit">
            <p @click="fn_toDetail(row)" class="color2">详情</p>
            <p class="table-line"></p>
            <p class="color2" @click="fn_toDeviceDetail(row)">管理设备</p>
            <p class="table-line"></p>
            <p @click="fn_del(row.id)" class="color2">删除</p>
          </div>
        </template>
      </iot-table>
    </div>

    <div class="produce-bottom" v-if="tableData.length">
      <!-- 分页 -->
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      :showLoading="true"
      :top="'10vh'"
      @callbackSure="fn_sure"
      @close="fn_close"
    >
      <template #body>
        <!-- 创建产品 -->
        <iot-form v-if="type == 1">
          <template #default>
            <el-form
              class="produceForm"
              ref="produceForm"
              :label-position="'top'"
              :model="produceForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <!-- 2022-4-20  暂时隐藏  上级要求 -->
              <!-- <el-form-item
                label="所属项目"
                prop="projectCode"
                key="projectCode"
              >
                <el-select
                  v-model="produceForm.projectCode"
                  placeholder="请选择所属项目"
                  key="produceForm.projectCode"
                  :popper-append-to-body="false"
                >
                  <template slot="empty">
                    <div class="empty-project">
                      <span>没有创建项目，请先去创建项目</span>
                    </div>
                  </template>
                  <el-option
                    v-for="item in projectList"
                    :key="item.code"
                    :label="item.name"
                    :value="item.code"
                  ></el-option>
                </el-select>
              </el-form-item> -->

              <el-form-item label="产品名称" prop="name">
                <el-input v-model="produceForm.name"></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="nameTrue">
                支持中文、英文字母、日文、数字、和特殊字符_-@(),长度限制
                4~30个字符,中文及日文算 2 个字符
              </div>

              <el-form-item
                label="产品品类"
                prop="classifiedId"
                key="classifiedId"
              >
                <el-cascader
                  ref="cascader"
                  v-model="produceForm.classifiedId"
                  :options="dictionaryList"
                  :props="params"
                  @change="handleChange"
                  :key="cascaderKey"
                  :append-to-body="false"
                ></el-cascader>
              </el-form-item>

              <el-form-item label="节点类型" prop="deviceGatewayTypeId">
                <el-radio-group
                  @change="fn_deviceGatewayType"
                  v-model="produceForm.deviceGatewayTypeId"
                >
                  <el-radio-button
                    v-for="item in dictnetList"
                    :key="item.id"
                    :label="item.id"
                    >{{ item.dictValue }}</el-radio-button
                  >
                  <!-- <el-radio-button label="2">网关子设备</el-radio-button> -->
                  <!-- <el-radio-button label="3">直连设备</el-radio-button> -->
                </el-radio-group>
              </el-form-item>

              <el-form-item label="连网方式" prop="networkWayId">
                <el-radio-group v-model="produceForm.networkWayId">
                  <el-radio-button
                    :key="item.id"
                    v-for="item in netList"
                    :label="item.id"
                    >{{ item.dictValue }}</el-radio-button
                  >
                  <!-- <el-radio-button label="2">以太网</el-radio-button>
                  <el-radio-button label="3">蜂窝网络</el-radio-button>
                  <el-radio-button label="4">LoRaWAN</el-radio-button>
                  <el-radio-button label="5">其他网络</el-radio-button>-->
                </el-radio-group>
              </el-form-item>

              <el-form-item label="产品描述" prop="description">
                <el-input
                  :maxlength="200"
                  type="textarea"
                  v-model="produceForm.description"
                ></el-input>
              </el-form-item>
              <div class="el-form-tips" v-if="descTrue">
                最多不超过200个字符
              </div>
            </el-form>
          </template>
        </iot-form>
        <!-- 删除产品 -->
        <div v-if="type == 2">
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    删除该产品后，产品下所有设备数据都将被删除且不能恢复，请确认
                    是否删除该产品？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
        <!-- 编辑产品 -->
        <!-- <iot-form v-if="type == 3">
          <template #default>
            <el-form
              ref="produceForm"
              :label-position="'top'"
              :model="produceForm"
              :rules="rules"
              @validate="fn_validate"
              label-width="80px"
            >
              <el-form-item label="产品名称" prop="name">
                <el-input v-model="produceForm.name"></el-input>
              </el-form-item>
              <div
                class="el-form-tips"
                v-if="nameTrue"
              >支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制 4~30个字符，中文及日文算 2 个字符</div>

              <el-form-item label="产品品类" prop="name">
                <el-input :disabled="true" v-model="produceForm.name"></el-input>
              </el-form-item>

              <el-form-item label="节点类型" prop="name">
                <el-input :disabled="true" v-model="produceForm.name"></el-input>
              </el-form-item>

              <el-form-item label="数据协议" prop="name">
                <el-input :disabled="true" v-model="produceForm.name"></el-input>
              </el-form-item>

              <el-form-item label="连网方式" prop="name">
                <el-input :disabled="true" v-model="produceForm.name"></el-input>
              </el-form-item>

              <el-form-item label="产品描述">
                <el-input :maxlength="200" type="textarea" v-model="produceForm.desc"></el-input>
              </el-form-item>
              <div class="el-form-tips">最多不超过200个字符</div>
            </el-form>
          </template>
        </iot-form>-->
      </template>
    </iot-dialog>
  </div>
</template>

<script>
import FormSearch from "@/components/form-search";
import IotForm from "@/components/iot-form";
import IotButton from "@/components/iot-button";
import IotPagination from "@/components/iot-pagination";
import IotDialog from "@/components/iot-dialog";
import IotTable from "@/components/iot-table";

import { listProject } from "@/api/equipment";
import {
  getProductList,
  getProductSave,
  getProductRemove,
} from "@/api/product.js";
import { searchOptions } from "./dictionary";
import { getDicList, getDictNetList } from "@/api/dictionary";
import { reg_two, reg_seven } from "@/util/util.js";
export default {
  name: "produce",
  components: {
    FormSearch,
    IotForm,
    IotButton,
    IotPagination,
    IotDialog,
    IotTable,
  },
  data() {
    return {
      that: this,
      params: {
        label: "dictValue",
        value: "id",
        children: "children",
        emitPath: false,
      },
      dialogWidth: "718px",
      tableData: [],
      columns: [
        // {
        //   label: "所属项目",
        //   prop: "projectName",
        //   width: "220",
        //   slotName: "projectName",
        // },
        { label: "产品名称", prop: "name", width: "220", slotName: "name" },
        { label: "ProductKey", prop: "productKey" },
        {
          label: "节点类型",
          prop: "deviceGatewayTypeId",
          slotName: "deviceGatewayTypeId",
        },
        // { label: "状态", prop: "status",slotName:'status'},
        { label: "创建时间", prop: "createTime" },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      searchOptions: searchOptions,
      searchParams: {
        name: "",
        productKey: "",
      },
      selectHolder: "全部产品",
      inputHolders: ["请输入产品名称", "请输入产品key"],
      inputMaxLength: 32,
      projectList: [],
      visible: false,
      delIds: "",
      produceForm: {
        name: "",
        description: "",
        projectCode: "",
        networkWayId: "",
        deviceGatewayTypeId: "",
        classifiedId: "",
      },
      rules: {
        projectCode: [
          {
            required: true,
            message: "须选择所属项目",
            trigger: "change",
          },
        ],
        name: [
          {
            required: true,
            // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',
            trigger: "blur",
            validator: this.checkName,
          },
        ],
        classifiedId: [
          {
            required: true,
            message: "须选择产品品类",
            trigger: "change",
          },
        ],
        // deviceGatewayTypeId: [
        //   {
        //     required: true,
        //     // message: '支持中文、英文、数字、下划线的组合，最多不超过32个字符',
        //     trigger: 'blur',
        //     validator: this.checkName
        //   }
        // ],
        description: [
          {
            required: false,
            // message: '最多不超过200个字符',
            trigger: "blur",
            validator: this.checkLength,
          },
        ],
      },
      nameTrue: true,
      descTrue: true,
      title: "",
      type: 1, //1 创建 修改 2 删除
      // value: [],
      dictionaryList: [], //产品品类
      dictnetList: [], //节点类型
      netList: [], //连网方式
      loading: true,
      cascaderKey: `cascaderKey1`, //产品品类动态key
    };
  },
  filters: {
    deviceGatewayTypeName(val, that) {
      return (
        that.dictnetList.find((item) => item.id === val) &&
        that.dictnetList.find((item) => item.id === val).dictValue
      );
    },
  },
  created() {
    // this.fn_get_table_data();
    // this.fn_get_project_list();
    // this.fn_get_dictionary_list();
    // this.fn_get_dictnet_list();
  },
  // keepalive 生命周期      //组件激活时触发
  activated() {
    let data = {
      ...this.searchParams,
      current: this.pagination.current,
      size: this.pagination.size,
    };
    this.fn_get_table_data(data);
    this.fn_get_project_list();
    this.fn_get_dictionary_list();
    this.fn_get_dictnet_list();
  },
  //  跳转非详情   清除 keep-alive 缓存数组中的缓存视图
  beforeRouteLeave(to, from, next) {
    if (to.path != "/productDetail") {
      // 取消缓存
      this.$clearKeepAlive(this, from.path);
    }
    next();
  },
  methods: {
    calcul_long_text(val) {
      return val.replace(/[\u0391-\uFFE5\u0800-\u4e00 ]/g, "aa").length;
    },
    // 节点类型改变
    fn_deviceGatewayType(val) {
      this.netList = this.dictnetList.find((item) => item.id === val).children;
      this.produceForm.networkWayId = this.netList[0].id;
    },
    // 名称校验
    checkName(rule, value, callback) {
      if (this.fn_notNull(value)) {
        return callback(new Error("请输入产品名称"));
      } else if (!reg_two(value)) {
        return callback(
          new Error(
            "支持中文、英文字母、日文、数字、和特殊字符_-@()，长度限制 4~30个字符，中文及日文算 2 个字符"
          )
        );
      } else {
        callback();
      }
    },
    // 长度检验
    checkLength(rule, value, callback) {
      if (!reg_seven(value, 201)) {
        return callback(new Error("最多不超过200个字符"));
      } else {
        callback();
      }
    },
    fn_notNull(val) {
      return val !== 0 && !val;
    },
    // 节点类型
    fn_get_dictnet_list() {
      getDictNetList().then((res) => {
        if (res.code == 200) {
          this.dictnetList = res.data;
          this.netList = res.data[0].children;
          this.produceForm.deviceGatewayTypeId = res.data[0].id;
          this.produceForm.networkWayId = res.data[0].children[0].id;
        } else {
          // this.$newNotify.error({
          //   message: res.message,
          // });
        }
      });
    },
    // 字典列表下拉
    fn_get_dictionary_list() {
      getDicList().then((res) => {
        if (res.code == 200) {
          this.dictionaryList = res.data;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 项目列表下拉
    fn_get_project_list() {
      listProject().then((res) => {
        if (res.code == 200) {
          this.projectList = res.data;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 产品列表
    fn_get_table_data(params = {}) {
      let others = {
        descs: true,
        ...params,
      };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      getProductList(others)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 新增
    fn_get_add_product_info(data) {
      if (this.produceForm.deviceGatewayTypeId) {
        data.netType = this.dictnetList.find(
          (item) => item.id == this.produceForm.deviceGatewayTypeId
        ).dictKey;
      }

      let loading = this.$loading({
        lock: true,
        text: "loading...",
        spinner: "el-icon-loading",
        background: "rgba(255, 255, 255, 0.9)",
        target: document.querySelector(".iot-dialog-content"),
      });
      getProductSave(data)
        .then((res) => {
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.pagination.current = 1;
            this.fn_get_table_data({ size: this.pagination.size, current: 1 });
            this.visible = false;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          loading.close();
        });
      // this.$loading && this.$loading.close()
    },
    // 搜索的下拉和输入的参数及操作
    handleSearch(params) {
      if (params.id === "1") {
        this.searchParams.name = params.value;
        this.searchParams.productKey = "";
      } else if (params.id === "2") {
        this.searchParams.productKey = params.value;
        this.searchParams.name = "";
      }
      this.fn_get_table_data(this.searchParams);
    },
    
    fn_clear_search_info() {
      this.searchParams = {
        productKey: "",
        name: "",
      };
      this.fn_get_table_data();
    },
    // 当前页总条数
    handleSizeChange(val) {
      // console.log(`每页 ${val} 条`)
      this.pagination.size = val;
      let params = {
        size: this.pagination.size,
        current: 1,
        ...this.searchParams,
      };
      this.fn_get_table_data(params);
    },
    // 当前页
    handleCurrentChange(val) {
      // console.log(`当前页: ${val}`)
      this.pagination.current = val;
      let params = {
        current: this.pagination.current,
        size: this.pagination.size,
        ...this.searchParams,
      };
      this.fn_get_table_data(params);
    },
    // 查看
    fn_toDetail(row) {
      this.$router.push({
        path: "productDetail",
        query: {
          id: row.id,
          // title: escape(row.name),
          // productKey: row.productKey
        },
      });
    },
    // 管理设备
    fn_toDeviceDetail(row) {
      this.$router.push({
        path: "/device",
        query: {
          id: row.id,
        },
      });
    },
    handleChange(value) {
      console.log(value);
    },
    // 再次确认
    fn_del_again() {
      // this.http_del_produce()
      this.$notify.error({
        title: "错误",
        duration: 5000,
      });
    },
    // 修改
    // fn_edit(row) {
    //   this.title = '修改产品'
    //   this.type = 3
    //   this.visible = true
    //   this.produceForm = {
    //     name: row.name,
    //     desc: row.address
    //   }
    // },
    // 删除
    fn_del(id) {
      // console.log(id)
      this.delIds = id;
      this.title = "确定删除该产品？";
      this.type = 2;
      this.dialogWidth = "550px";
      this.visible = true;
      // this.$newNotify.warning({
      //   message: '确定删除该产品',
      //   duration: 5000
      // })
    },
    // dialog  确定按钮
    fn_sure() {
      // this.visible = false

      // 删除
      if (this.type === 2) {
        let loading = this.$loading({
          lock: true,
          text: "loading...",
          spinner: "el-icon-loading",
          background: "rgba(255, 255, 255, 0.9)",
          target: document.querySelector(".iot-dialog-content"),
        });
        getProductRemove({ ids: this.delIds })
          .then((res) => {
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.fn_get_table_data();
              this.visible = false;
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          })
          .finally(() => {
            loading.close();
          });
      } // 新增(编辑功能在产品管理里边)
      else if (this.type === 1) {
        this.$refs["produceForm"].validate((valid) => {
          if (valid) {
            this.fn_get_add_product_info(this.produceForm);
          } else {
            return false;
          }
        });
      }
    },
    fn_close() {
      if (this.type == 1) {
        this.nameTrue = true;
      }
    },
    // 表单验证触发
    fn_validate(name, value) {
      if (name === "name") {
        this.nameTrue = value;
      }
      if (name === "description") {
        this.descTrue = value;
      }
    },
    // 创建产品弹窗
    fn_open() {
      this.title = "创建产品";
      this.type = 1;
      this.dialogWidth = "718px";
      this.visible = true;
      // this.produceForm = {
      //   name: '',
      //   description: '',
      //   projectCode: ''
      // }
    },
    // 确认请求
    // http_create_produce() {
    // this.$refs['produceForm'].validate(valid => {
    //   if (valid) {
    //     this.visible = false
    //   } else {
    //     return false
    //   }
    // })
    // },
    // 删除请求
    // http_del_produce() {
    //   this.$refs['produceForm'].validate(valid => {
    //     if (valid) {
    //       this.visible = false
    //     } else {
    //       return false
    //     }
    //   })
    // }
  },
  watch: {
    visible(val) {
      if (!val && this.type == 1) {
        this.cascaderKey = `cascaderKey${Math.random() * 10}`;
        this.$refs.cascader.$refs.panel.clearCheckedNodes();
        this.$refs["produceForm"] && this.$refs["produceForm"].resetFields();
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.produce {
  .product_bg{
    width: 100%;
    height: 136px;
    background: url('~@/assets/images/product/product_bg.jpg') no-repeat 0px 0px ;
    background-size: 100%;
    padding: 35px 0px 0px 32px;
    .bg_title{
      font-weight: 500;
      font-size: 22px;
      line-height: 30px;
      color: #333333;
    }
    .bg_desc{
      font-weight: 400;
      font-size: 16px;
      line-height: 30px;
      color: #77797C;
    }
  }
  .produce-top {
    padding: 0px 32px 0 32px;
    display: flex;
    justify-content: end;
    align-items: center;
    margin: 18px 0px 18px 0px;
    .top-left {
      align-items: center;
    }
    .top-right {
      align-items: center;
    }
  }
  .produce-table {
    padding: 0px 32px 0 32px;
    .table-longText {
      width: 100%;
      p {
        width: 240px;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        letter-spacing: 1px;
      }
    }
    .table-edit {
      display: flex;
      align-items: center;
      p {
        cursor: pointer;
      }
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }
    }
    .table-status {
                .status {
                    .red {
                    background: #ff4d4f;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                    .green {
                    background: #00c250;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                    .yellow {
                    background: #e6a23c;
                    width: 8px;
                    height: 8px;
                    border-radius: 4px;
                    margin-top: 8px;
                    margin-right: 6px;
                    }
                }
            }
  }
  .produce-bottom {
    text-align: right;
    margin-top: 14px;
  }
  .del-tips {
    width: 420px;
  }
  .product-name:hover {
    cursor: pointer;
    color: #018aff;
  }
  .produceForm {
    /deep/ .el-form-item {
      margin-bottom: 17px;
    }
    .el-form-tips {
      margin-top: -17px;
    }
  }
  /deep/ .el-radio-button__inner {
    width: 100%;
  }
}
/deep/ {
  .el-cascader {
    line-height: 34px;
  }

  .el-cascader-menu__wrap {
    height: auto;
  }
  .el-cascader .el-input .el-icon-arrow-down {
    height: 32px;
    line-height: 32px !important;
  }
}
</style>
