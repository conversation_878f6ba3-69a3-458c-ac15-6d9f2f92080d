<template>
  <iotDialog
    title="查看数据"
    :visible.sync="visible"
    width="800px"
    :footer="false"
  >
    <template #body>
      <div class="select">
        <div class="flex">
          <el-select
            v-model="selectVal"
            placeholder="请选择"
            @change="selectChange"
          >
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-date-picker
            align="center"
            v-if="selectVal == 4"
            v-model="rangeDate"
            type="datetimerange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            :picker-options="pickerOptions"
            @change="pickerChange"
          >
          </el-date-picker>
          <el-button class="refresh-btn" v-throttle="500" @click="handleRefresh">
            <img src="~@/assets/images/index/refresh.svg" />
          </el-button>
        </div>
        <!--  -->
        <div class="table">
          <iot-table
            :columns="columns"
            :data="source"
            :loading="loading"
            :style="{ height: toVW(400) }"
            max-height="400px"
          />
          <div class="pagination flex">
            <iot-pagination
              :pagination="pagination"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </div>
    </template>
  </iotDialog>
</template>

<script>
import iotDialog from "@/components/iot-dialog";
import iotTable from "@/components/iot-table";
import iotPagination from "@/components/iot-pagination";
import { getPropertiesData } from "@/api/device";
import { fn_util__date_format } from "@/util/util";
import toVW from "@/util/toVW.js";
export default {
  data() {
    return {
      visible: false,
      loading: false,
      propertiesId: "", //属性id
      options: [
        {
          value: 1,
          label: "1小时",
        },
        {
          value: 2,
          label: "24小时",
        },
        {
          value: 3,
          label: "7天",
        },
        {
          value: 4,
          label: "自定义",
        },
      ],
      rangeDate: [],
      selectVal: 1,
      columns: [
        {
          prop: "time",
          label: "时间",
        },
        {
          prop: "initValue",
          label: "原始值",
        },
      ],
      source: [],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [8, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 8,
        pagerCount: 7
      },
      pickerOptions: {
        disabledDate(time) {
          let curDate = new Date().getTime();
          let three = 6 * 30 * 24 * 3600 * 1000;
          let threeMonths = curDate - three;
          return time.getTime() > Date.now() || time.getTime() < threeMonths;
        },
      },
      startTime: "",
      endTime: "",
    };
  },
  props: {
    deviceName: {
      type: String,
    },
    productKey: {
      type: String,
    },
  },
  components: { iotDialog, iotTable, iotPagination },
  methods: {
    toVW,
    handleRefresh() {
      this.pagination.current = 1;
      this.formatTime(this.selectVal)
    },
    formatTime(type) {
      let format;
      let { yy, MM, dd, hh, mm, ss, timestamp } = fn_util__date_format();
      this.endTime = `${yy}-${MM}-${dd} ${hh}:${mm}:${ss}`;
      // timestamp 13位时间戳
      if (type == 1) {
        // 1小时
        let time = timestamp - 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 2) {
        // 24小时
        let time = timestamp - 24 * 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 3) {
        // 7天
        let time = timestamp - 7 * 24 * 3600 * 1000;
        format = fn_util__date_format(time);
      } else if (type == 4) {
        // 自定义
        if (this.rangeDate && this.rangeDate.length > 0) {
          format = fn_util__date_format(this.rangeDate[0]);
          let endFormat = fn_util__date_format(this.rangeDate[1]);
          this.endTime = `${endFormat.yy}-${endFormat.MM}-${endFormat.dd} ${endFormat.hh}:${endFormat.mm}:${endFormat.ss}`;
        } else return;
      }
      this.startTime = `${format.yy}-${format.MM}-${format.dd} ${format.hh}:${format.mm}:${format.ss}`;
      this.getData();
    },
    open(id) {
      this.selectVal = 1;
      this.visible = true;
      this.propertiesId = id;
      this.formatTime(this.selectVal);
    },
    getData() {
      let params = {
        deviceName: this.deviceName,
        productKey: this.productKey,
        propertiesId: this.propertiesId,
        startTime: this.startTime,
        endTime: this.endTime,
        pageSize: this.pagination.size,
        currentPage: this.pagination.current,
      };
      this.loading = true;
      getPropertiesData(params)
        .then((res) => {
          this.pagination.total = res.data.total;
          this.source = res.data.records;
          if(this.pagination.total > 1000) {
            this.pagination.pagerCount = 5
          }
        })
        .finally(() => {
          this.loading = false;
        });
    },
    handleSizeChange(data) {
      this.pagination.current = 1;
      this.pagination.size = data;
      this.formatTime(this.selectVal);
    },
    handleCurrentChange(data) {
      this.pagination.current = data;
      this.formatTime(this.selectVal);
    },
    selectChange() {
      this.pagination.current = 1;
      this.formatTime(this.selectVal);
    },
    pickerChange() {
      this.pagination.current = 1;
      this.formatTime(this.selectVal);
    },
  },
};
</script>

<style lang="scss" scoped>
.select {
  height: 551px;
  .table {
    padding: 26px 0;
    .pagination {
      justify-content: flex-end;
      padding-top: 26px;
    }
  }
  .refresh-btn {
    height: 34px;
    width: 34px;
    padding: 0;
    border-radius: 0;
    margin-left: auto;
    img {
      width: 14px;
      height: 14px;
    }
  }
  /deep/ {
    .el-select {
      width: 240px;
      height: 34px;
      margin-right: 14px;
      .el-input__inner {
        border-radius: 0;
      }
      .el-input__icon {
        font-weight: 400;
      }
    }
    .el-date-editor {
      width: calc(100% - 14px - 240px - 94px - 34px);
      height: 34px;
      border-radius: 0;
      line-height: 26px;
      .el-range__icon,
      .el-input__icon {
        line-height: 26px !important;
        font-weight: 400;
      }
      .el-range-separator {
        line-height: 26px;
      }
    }
  }
  .pagination {
    /deep/ {
      .el-select {
        width: auto;
      }
    }
  }
}
</style>
