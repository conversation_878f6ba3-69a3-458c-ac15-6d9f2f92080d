<template>
  <div class="login">
    <top-bar :isLogin="false" />
    <div class="content flex">
      <div class="pic">
        <img
          src="~@/assets/images/index/poster.png"
          alt=""
          ondragstart="return false;"
        />
      </div>
      <div class="login-form flex">
        <h4>登录您的账号</h4>
        <p class="login-tips flex" :style="{ opacity: loginTips ? '1' : '0' }">
          <img src="~@/assets/images/index/wrong-icon.png" alt="" />
          <span>{{ loginTips }}</span>
        </p>
        <el-form ref="form" :model="form" :rules="rules">
          <el-form-item label="" prop="username">
            <div class="form-item">
              <!-- <p>用户名称</p> -->
              <el-input
                ref="username"
                v-model="form.username"
                clearable
                placeholder="手机号码/用户名"
                @focus="nameFocus = true"
                @blur="nameFocus = false"
              />
            </div>
          </el-form-item>
          <el-form-item label="" prop="password">
            <div class="form-item form-password">
              <!-- <p>登录密码</p> -->
              <el-input
                v-model="form.password"
                clearable
                :show-password="true"
                placeholder="密码"
                @focus="passwordFocus = true"
                @blur="passwordFocus = false"
              ></el-input>
            </div>
          </el-form-item>
        </el-form>
        <!-- @click="handleSubmit" -->
        <!-- v-throttle自定义防抖函数 -->
        <div class="login-button" v-throttle="500" @click="handleLogin">
          <span v-if="flag">登录</span>
          <span v-else>登录中...</span>
        </div>
        <div class="login-action flex">
          <span @click="handleRegister">立即注册</span>
          <span @click="handleReset">忘记密码</span>
        </div>
      </div>
    </div>
    <copyright class="copyright" />
  </div>
</template>

<script>
import conf from "./conf";
export default conf;
</script>

<style lang="scss" scoped>
.login {
  min-height: 100vh;
  padding-top: 52px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .content {
    width: 1020px;
    height: 480px;
    margin: 148px auto 0;
    border-radius: 4px;
    box-shadow: 0px 48px 48px 0px rgba(0, 0, 0, 0.05);
    .pic {
      width: 540px;
      height: 480px;
      flex-shrink: 0;
      img {
        width: 100%;
      }
    }
    .login-form {
      width: calc(100% - 540px);
      padding: 72px 90px 0;
      flex-direction: column;
      background: #ffffff;
      border-top-right-radius: 4px;
      border-bottom-right-radius: 4px;
      position: relative;
      h4 {
        text-align: center;
        color: #333333;
        font-weight: 500;
        font-size: 28px;
        line-height: 16px;
        padding-bottom: 58px;
      }
      .login-tips {
        width: 300px;
        height: 36px;
        align-items: center;
        background: rgba(255, 237, 237, 0.8);
        border: 1px solid rgba(255, 77, 79, 0.24);
        box-sizing: border-box;
        backdrop-filter: blur(8px);
        color: #f53e3e;
        font-size: 14px;
        line-height: 16px;
        padding: 0px 20px;
        position: absolute;
        left: 90px;
        top: 104px;
        transition: all 0.3s;
        img {
          width: 14px;
          height: 14px;
          flex-shrink: 0;
        }
        span {
          padding-left: 10px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
      }
      .form-item {
        position: relative;

        p {
          color: #333333;
          font-size: 14px;
          line-height: 16px;
          padding-bottom: 8px;
          font-family: H_Medium;
        }

        :deep(.el-input) {
          input {
            color: #333333;
            outline: none;
            border: 1px solid #e4e7ec;
          }
          input:hover {
            border: 1px solid #018aff;
          }
          input:focus {
            border: 1px solid #018aff;
            box-shadow: 0px 0px 0px 2px #f2f9ff;
          }
          input::placeholder {
            color: #bfbfbf;
          }
          .el-input__clear {
            font-family: "tenant" !important;
            font-size: 18px;
            color: #888888;
          }
          .el-icon-circle-close::before {
            content: "\e644" !important;
          }
          .el-icon-view::before {
            content: "\e642" !important;
          }
          .el-input__icon:after {
            // content: "\e643";
          }
        }
      }
      :deep(.el-form) {
        .el-form-item {
          margin-bottom: 34px;
        }
        .el-input__inner {
          border-radius: 0;
          height: 42px !important;
          font-family: "Courier New", Courier, monospace;
        }
        .el-form-item__error {
          color: #f53e3e;
          font-size: 14px;
        }
      }
      .login-button {
        height: 42px;
        line-height: 42px;
        text-align: center;
        cursor: pointer;
        background: linear-gradient(180deg, #0088fe 0%, #006eff 100%);
        span {
          color: #ffffff;
          font-size: 16px;
        }
      }
      .login-action {
        justify-content: center;
        align-items: center;
        font-size: 14px;
        line-height: 16px;
        padding-top: 42px;
        span {
          color: #0088fe;
          padding: 0 18px;
          position: relative;
          cursor: pointer;
        }
        span::before {
          content: "";
          position: absolute;
          top: 50%;
          transform: translateY(-50%);
          right: 0;
          width: 1px;
          height: 14px;
          background: #ededed;
        }
        span:last-child::before {
          display: none;
        }
      }
    }
  }
  // .copyright {
  //   position: absolute;
  //   bottom: 32px;
  //   left: 50%;
  //   transform: translateX(-50%);
  //   padding: 0;
  //   span {
  //     text-align: center;
  //     font-style: normal;
  //     font-weight: normal;
  //     font-size: 12px;
  //     line-height: 14px;
  //     color: #b2b5bc;
  //   }
  // }
}
</style>
