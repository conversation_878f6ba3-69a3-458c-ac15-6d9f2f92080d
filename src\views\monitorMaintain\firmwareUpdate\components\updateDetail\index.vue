<template>
  <div class="update-detail">
    <div class="detail-top flex">
      <div class="top-item">
        <div class="top-title">
          <span>任务信息</span>
        </div>
        <div class="top-content">
          <div class="item">
            <span>固件名称</span>
            <span>{{ jobFrom.name }}</span>
          </div>
          <div class="item">
            <span>所属产品</span>
            <span>{{ jobFrom.productName }}</span>
          </div>
          <div class="item">
            <span>添加时间</span>
            <span>{{ jobFrom.createTime }}</span>
          </div>
          <div class="item">
            <span>固件版本号</span>
            <span>{{ jobFrom.version }}</span>
          </div>
          <div class="item">
            <span>签名算法</span>
            <span>{{ jobFrom.signMethod }}</span>
          </div>
          <div class="item">
            <span>固件描述</span>
            <span>{{ jobFrom.description }}</span>
          </div>
        </div>
      </div>
      <div class="top-item">
        <div class="top-title">
          <span>任务统计</span>
        </div>
        <div
          v-if="!chartsEmpty"
          ref="jobStatus"
          class="ecahrts-postCss top-content"
        ></div>
        <div v-else class="census-content">
          <div class="census-empty flex"></div>
          <div class="census-content-value flex">
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>待推送 <b>0</b> 个</span>
              </div>
            </div>
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>已取消 <b>0</b> 个</span>
              </div>
            </div>
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>已推送 <b>0</b> 个</span>
              </div>
            </div>
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>升级中 <b>0</b> 个</span>
              </div>
            </div>
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>升级成功 <b>0</b> 个</span>
              </div>
            </div>
            <div class="content-item">
              <div class="item-title flex">
                <p></p>
                <span>升级失败 <b>0</b> 个</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="detail-content">
      <div class="content-select flex">
        <div class="content-title">
          <span>设备详情</span>
        </div>
        <div class="right">
          <form-search
            :options="options"
            @search="handleSearch"
            @clear="fn_clear_search_info"
            :inputHolder="inputHolder"
            :selectHolder="selectHolder"
          />
        </div>
      </div>
      <div class="content-table">
        <iot-table :columns="columns" :data="tableData" :loading="loading">
          <template slot="status" slot-scope="{ row }">
            <div class="state-edit">
              <div class="status flex" v-if="row.status == 1">
                <div class="success"></div>
                <div>升级成功</div>
              </div>
              <div class="status flex" v-if="row.status == 2">
                <div class="fail"></div>
                <div>升级失败</div>
              </div>
              <div class="status flex" v-if="row.status == 3">
                <div class="toPush"></div>
                <div>待推送</div>
              </div>
              <div class="status flex" v-if="row.status == 4">
                <div class="pushed"></div>
                <div>已推送</div>
              </div>
              <div class="status flex" v-if="row.status == 5">
                <div class="upgrading"></div>
                <div>升级中</div>
              </div>
              <div class="status flex" v-if="row.status == 6">
                <div class="canceled"></div>
                <div>已取消</div>
              </div>
            </div>
          </template>
          <template slot="statusDescription" slot-scope="{ row }">
            <div class="desc-edit">
              <div v-if="row.statusDescription != '升级中'">
                <p>{{ row.statusDescription }}</p>
              </div>
              <div class="progress flex" v-else>
                <div class="progress-bar">
                  <p :style="{ width: `${row.upgradeStep}%` }"></p>
                </div>
                <div class="step">
                  <p>{{ `${row.upgradeStep}%` }}</p>
                </div>
              </div>
            </div>
          </template>
          <template slot="operation" slot-scope="{ row }">
            <div class="table-edit flex">
              <p
                @click="handleUpdateCancel(row)"
                class="color2"
                v-if="row.status != 6 && row.status != 1"
              >
                取消
              </p>
              <p v-else class="cancel">取消</p>
            </div>
          </template>
        </iot-table>
      </div>
      <div class="content-bottom" v-if="tableData.length > 0">
        <iot-pagination
          :pagination="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>
    <iot-dialog
      :visible.sync="visible"
      :title="title"
      :width="dialogWidth"
      @callbackSure="fn_sure"
    >
      <template #body>
        <div>
          <iot-form>
            <template #default>
              <el-form>
                <el-form-item>
                  <div class="del-tips">
                    该设备升级任务取消后不可恢复，请确认是否取消任务？
                  </div>
                </el-form-item>
              </el-form>
            </template>
          </iot-form>
        </div>
      </template>
    </iot-dialog>
  </div>
</template>
<script>
import FormSearch from "@/components/form-search";
import IotTable from "@/components/iot-table";
import IotForm from "@/components/iot-form";
import IotDialog from "@/components/iot-dialog";
import IotPagination from "@/components/iot-pagination";

import {
  getFirmwareJobDetail,
  getUpgradeStatistic,
  getJobDeviceList,
  putJobDeviceCancel,
} from "@/api/firmwareUpdate";
export default {
  name: "updateDetail",
  components: {
    FormSearch,
    IotTable,
    IotPagination,
    IotDialog,
    IotForm,
  },
  data() {
    return {
      inputHolder: "输入设备名称",
      selectHolder: "选择升级状态",
      options: [
        {
          id: "999",
          name: "全部状态",
        },
        {
          id: "1",
          name: "升级成功",
        },
        {
          id: "2",
          name: "升级失败",
        },
        {
          id: "3",
          name: "待推送",
        },
        {
          id: "4",
          name: "已推送",
        },
        {
          id: "5",
          name: "升级中",
        },
        {
          id: "6",
          name: "已取消",
        },
      ],
      searchParams: {},
      columns: [
        { label: "设备名称", prop: "deviceName" },
        { label: "当前版本号", prop: "currentVersion" },
        { label: "最后更新时间", prop: "statusUpdateTime" },
        { label: "升级状态", prop: "status", slotName: "status" },
        {
          label: "状态详情",
          prop: "statusDescription",
          slotName: "statusDescription",
        },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      jobFrom: {
        createTime: "2021-12-30 12:19:06",
        description: "12312344",
        firmwareSign: "75920954bc9f6c102be10201d20554c4",
        name: "test_123",
        productKey: "1476078991694442498",
        signMethod: "MD5",
        version: "test_1",
      },
      tableData: [],
      loading: false,
      firmwareId: "",
      jobId: "",
      jobCategory: [[], []],
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 4,
      },
      visible: false,
      title: "添加新固件",
      dialogWidth: "732px",
      delIds: "",
      chartsEmpty: true, //echarts数据是否为空
    };
  },
  created() {
    this.jobId = this.$route.query.id;
    this.firmwareId = this.$route.query.firmwareId;
  },
  mounted() {
    this.fn_getFirmwareJobDetail();
    this.fn_getUpgradeStatistic();
    this.fn_get_table_data();
    window.addEventListener("resize", () => {
      if (this.jobStatus) {
        console.log("-*-*");
        this.jobStatus.resize();
      }
    });
  },
  beforeDestroy() {
    window.removeEventListener("resize", () => {});
  },
  methods: {
    fn_getFirmwareJobDetail() {
      getFirmwareJobDetail(this.jobId).then((res) => {
        if (res.code == 200) {
          this.jobFrom = res.data;
          //路由信息
          let data = {
            // id: res.data.id,
            title: res.data.name,
          };
          this.$store.dispatch("setLayoutInfo", data);
        }
      });
    },
    fn_getUpgradeStatistic() {
      getUpgradeStatistic(this.firmwareId, this.jobId).then((res) => {
        if (res.code == 200) {
          if (res.data.total > 0) {
            this.chartsEmpty = false;
            this.jobCategory = [
              ["待推送", "已取消", "已推送", "升级中", "升级成功", "升级失败"],
              [
                res.data.toPush | 0,
                res.data.canceled | 0,
                res.data.pushed | 0,
                res.data.upgrading | 0,
                res.data.success | 0,
                res.data.fail | 0,
              ],
            ];

            //测试数据
            // this.jobCategory[1] = [1, 2, 3, 4, 5, 6]
            // // if (
            // // 	(((((res.data.toPush === res.data.canceled) ===
            // // 		res.data.pushed) ===
            // // 		res.data.upgrading) ===
            // // 		res.data.success) ===
            // // 		res.data.fail) ===
            // // 	0
            // // ) {
            // // 	this.jobStatusNone = this.$echarts.init(
            // // 		this.$refs.jobStatusNone
            // // 	)
            // // 	this.fn_create__echarts_job_none()
            // // } else {
            // // 	this.jobStatus = this.$echarts.init(
            // // 		this.$refs.jobStatus
            // // 	)
            // // 	this.fn_create__echarts_job()
            // // }

            // this.jobStatusNone = this.$echarts.init(
            // 	this.$refs.jobStatusNone
            // )
            // this.fn_create__echarts_job_none()

            this.$nextTick(() => {
              this.jobStatus = this.$echarts.init(this.$refs.jobStatus);
              this.fn_create__echarts_job();
            });
          } else {
            // 数据为空
            this.chartsEmpty = true;
          }
        }
      });
    },

    fn_get_table_data(params = { jobId: this.jobId }) {
      let others = { ...params };
      if (!params.size) {
        others.size = 10;
        others.current = 1;
      }
      getJobDeviceList(others)
        .then((res) => {
          if (res.code == 200) {
            setTimeout(() => {
              this.loading = false;
            }, 300);
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    fn_sure() {
      putJobDeviceCancel(this.delIds).then((res) => {
        if (res.code == 200) {
          this.$newNotify.success({
            message: res.message,
          });
          this.visible = false;
          this.fn_get_table_data({
            size: this.pagination.size,
            current: 1,
            jobId: this.jobId,
          });
          this.fn_getUpgradeStatistic();
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // // 当前页总条数
    // handleSizeChange(val) {
    //   console.log(val);
    //   this.fn_get_table_data(this.searchParams);
    // },
    // // 当前页
    // handleCurrentChange(val) {
    //   console.log(val);
    //   this.fn_get_table_data(this.searchParams);
    // },

    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.current = 1;
      this.pagination.size = val;

      this.fn_get_table_data({
        ...this.searchParams,
        jobId: this.jobId,
        size: this.pagination.size,
        current: this.pagination.current,
      });
    },
    // 当前页
    handleCurrentChange(val) {
      console.log(this.pagination);
      this.pagination.current = val;

      this.fn_get_table_data({
        ...this.searchParams,
        jobId: this.jobId,
        size: this.pagination.size,
        current: this.pagination.current,
      });
    },
    handleUpdateCancel(row) {
      this.visible = true;
      this.title = "是否取消任务？";
      this.delIds = row.id;
    },
    handleSearch(val) {
      this.searchParams = {
        deviceName: val.value ? val.value : "",
        upgradeStatus: val.id ? (val.id === "999" ? "" : val.id) : "",
      };
      this.fn_get_table_data({
        ...this.searchParams,
        jobId: this.jobId,
      });
    },
    fn_clear_search_info() {
      this.searchParams = {};
      this.fn_get_table_data({
        jobId: this.jobId,
      });
    },
    fn_create__echarts_job() {
      let $this = this;
      const options = {
        color: [
          "#F4AE07",
          "#CCCCCC",
          "#9D5EF0",
          "#18A0FB",
          "#24C661",
          "#FF5E68",
          "#E9F2FB",
        ],
        title: {
          text: "",
          textStyle: {
            color: "rgba(235, 245, 245, 1)",
            fontFamily: "H_Bold",
            fontStyle: "normal",
            fontWeight: "normal",
            fontSize: 14,
          },
        },
        grid: {
          top: 0,
          bottom: 0,
          left: 20,
          width: 200,
        },
        tooltip: {
          trigger: "item",
          textStyle: {
            fontFamily: "H_Light",
          },
          borderColor: "rgba(253, 253, 253, 0.42)",
          // backgroundColor: 'rgba(172,172,172, 0.12)',
          extraCssText:
            "border-radius: 0;backdrop-filter: blur(22px);background: rgba(172, 172, 172, 0.12)",
          borderWidth: 1,
          renderMode: "html",
          padding: 10,
          formatter: function (params) {
            const marker =
              "<span style='display:inline-block;margin:0 5px 2px 0;border-radius:6px;width:6px;height:6px;background-color:" +
              params.color +
              ";'></span>";
            let str = "";
            str +=
              "<div >" +
              "<div >" +
              "<span style='color:rgba(51, 51, 51, 1);'>" +
              marker +
              "" +
              params.name +
              "</span><br />";
            str +=
              "<span style='color:rgba(51, 51, 51, 1);font-size: 12px;'>" +
              params.value[1] +
              "个";
            "</span>" + "</div>" + "</div>";
            return str;
          },
        },
        legend: {
          type: "scroll",
          orient: "horizontal",
          selected: [],
          bottom: "12",
          icon: "circle",
          itemGap: 30,
          itemWidth: 9,
          itemHeight: 9,
          padding: 2,
          textStyle: {
            color: "#999999",
            fontFamily: "H_Regular",
          },
          formatter: function (name) {
            let index = $this.jobCategory[0].indexOf(name);
            return name + " " + $this.jobCategory[1][index] + "个";
          },
        },
        dataset: {
          source: this.jobCategory,
        },
        series: [
          {
            name: "",
            type: "pie",
            hoverAnimation: false,
            seriesLayoutBy: "row",
            center: ["50%", "45%"],
            radius: "75",
            roseType: false,
            avoidLabelOverlap: false,
            label: {
              position: "inside",
            },
            labelLine: {
              show: false,
            },
            itemStyle: {
              normal: {
                label: {
                  show: false,
                },
              },
            },
          },
        ],
      };
      this.jobStatus.clear();
      this.jobStatus.setOption(options);
    },
  },
};
</script>
<style lang="scss" scoped>
@mixin fontStyle400 {
  font-size: 14px;
  font-weight: 400;
}
@mixin fontStyle500 {
  font-size: 16px;
  font-weight: 500;
}
@mixin BoxShadow {
  box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
}
$blue: #018aff;
$green: #00c250;
$purple: #8f01ff;
$red: #ff4d4f;
$yellow: #e6a23c;
.update-detail {
  .detail-top {
    margin-top: 18px;
    .top-item {
      width: calc(50% - 9px);
      background: #ffffff;
      box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
      border-radius: 2px;
      padding: 20px 32px 10px;
      &:first-child {
        margin-right: 18px;
      }
      .top-title {
        margin-bottom: 18px;
      }
      .top-content {
        height: calc(100% - 40px);
        width: 740px;
        border-radius: 2px;
        // box-shadow: 0px 5px 18px rgba(0, 0, 0, 0.08);
        // padding: 14px 0;
        .item {
          width: 100%;
          font-size: 14px;
          font-weight: 400;
          padding: 10px 0 10px 0;
          display: inline-flex;
          span {
            // height: 22px;
            line-height: 22px;
            &:first-child {
              flex-shrink: 0;
              width: 80px;
              text-align: right;
              color: #999999;
              margin-right: 48px;
            }
            &:last-child {
              color: #515151;
            }
          }
        }
      }
      .census-content {
        height: 298px;
        width: 816px;
        @include BoxShadow;
        margin-top: 18px;
        .census-empty {
          align-items: center;
          justify-content: center;
          padding: 40px 0;
        }
        .census-empty::after {
          content: "";
          display: block;
          width: 150px;
          height: 150px;
          background: #e9f2fb;
          border-radius: 50%;
        }
        .census-content-value {
          padding: 0 80px;
        }
        .content-item {
          flex: 1;
          padding: 20px 0 0 0px;
          .item-title {
            align-items: center;
            justify-content: center;
            p {
              @include fontStyle400;
              width: 9px;
              height: 9px;
              border-radius: 50%;
              margin: 0px 8px 0 0;
            }
            span {
              color: #999999;
              font-size: 12px;
              line-height: 16px;
              b {
                font-weight: normal;
                color: #515151;
                font-size: 16px;
              }
            }
          }
          .num {
            margin: 17px;
            span {
              font-size: 24px;
              font-weight: 500;
            }
          }
          &:nth-child(1) {
            .item-title {
              p {
                background: #f4ae07;
              }
            }
          }
          &:nth-child(2) {
            .item-title {
              p {
                background: #cccccc;
              }
            }
          }
          &:nth-child(3) {
            .item-title {
              p {
                background: #9d5ef0;
              }
            }
          }
          &:nth-child(4) {
            .item-title {
              p {
                background: #18a0fb;
              }
            }
          }
          &:nth-child(5) {
            .item-title {
              p {
                background: #24c661;
              }
            }
          }
          &:nth-child(6) {
            .item-title {
              p {
                background: #ff5e68;
              }
            }
          }
        }
      }
    }
  }
  .detail-content {
    margin-top: 18px;
    box-shadow: 0px 2px 8px rgba(0, 0, 0, 0.1);
    border-radius: 2px;
    padding: 20px;
    .content-select {
      margin-bottom: 18px;
      justify-content: space-between;
    }
    .content-bottom {
      text-align: right;
      margin-top: 14px;
    }
    .content-table {
      margin-top: 18px;
      .table-edit {
        display: flex;
        align-items: center;
        p {
          cursor: pointer;
        }
        .table-line {
          margin: 0px 12px;
          width: 1px;
          height: 13px;
          border: 1px solid #ededed;
        }
        .cancel {
          color: #bfbfbf;
        }
      }
      .state-edit {
        .status {
          .success {
            background: #24c661;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }
          .fail {
            background: #ff5e68;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }

          .toPush {
            background: #f4ae07;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }

          .pushed {
            background: #18a0fb;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }

          .upgrading {
            background: #24c661;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }

          .canceled {
            background: #cccccc;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 7px;
            margin-right: 6px;
          }
        }
      }
      .desc-edit {
        .progress {
          align-items: center;
        }
        .progress-bar {
          width: 130px;
          height: 6px;
          background: #eef1f7;

          p {
            height: 100%;
            background: #24c661;
          }
        }
        .step {
          padding-left: 8px;
          p {
            color: #515151;
            font-size: 14px;
          }
        }
      }
    }
  }
}
</style>
