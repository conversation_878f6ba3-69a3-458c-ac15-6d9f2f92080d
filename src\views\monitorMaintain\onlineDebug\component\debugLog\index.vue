<template>
  <div class="debug-log">
    <div class="debug-top flex">
      <div class="debug-left flex">
        <span>调试日志</span>
        <div class="device-content" v-if="deviceName != ''">
          <div class="device-status flex" v-if="!online">
            <div class="red"></div>
            <div>离线</div>
          </div>
          <div class="device-status flex" v-if="online">
            <div class="green"></div>
            <div>在线</div>
          </div>
        </div>
      </div>
      <div class="debug-right">
        <el-switch
          v-model="isRealTime"
          :disabled="!onlineFlag"
          active-color="#13CE66"
        ></el-switch>
        <span>开始实时日志</span>
        <span @click="refreshLog">刷新日志</span>
        <span @click="clearLog">清空日志</span>
      </div>
    </div>
    <div class="debug-content" ref="debugContent">
      <template v-if="online && logList.length">
        <div class="content-top flex">
          <span>时间</span>
          <span>内容</span>
        </div>
        <div class="content-center">
          <content-item
            v-for="(item, index) in logList"
            :key="index"
            :logItem="item"
          ></content-item>
        </div>
      </template>
      <div class="code-empty flex" v-else>
        <img src="~@/assets/images/empty/emptyData.png" alt />
        <p>暂无数据</p>
      </div>
    </div>
  </div>
</template>
<script>
import { getDebugList } from "@/api/onlineDubug";
import ContentItem from "./components/contentItem.vue";
import { mapGetters } from "vuex";
export default {
  name: "debugLog",
  components: {
    ContentItem,
  },
  props: {
    deviceName: {
      type: String,
      default: "",
    },
    online: {
      type: Boolean,
      default: false,
    },
    logList: {
      type: Array,
      default: () => [],
    },
    productKey: {
      type: String,
      default: "",
    },
  },
  computed: {
    ...mapGetters(["onlineFlag", "logDataCopy"]),
  },
  mounted() {
    this.isRealTime = this.onlineFlag;
  },
  data() {
    return {
      isRealTime: false,
    };
  },
  watch: {
    logList() {
      if (this.$refs.debugContent) {
        this.$scrollRef("debugContent");
      }
    },
    isRealTime(newVal) {
      if (newVal) {
        this.$store.dispatch(
          "fn_logstatus__subscribe",
          `${this.productKey}_${this.deviceName}`
        );
      } else {
        this.$store.dispatch(
          "fn_logstatus__unsubscribe",
          `/iot/${this.productKey}_${this.deviceName}/log`
        );
      }
    },
    onlineFlag(newVal) {
      this.isRealTime = newVal;
    },
  },
  created() {},
  methods: {
    // 刷新日志
    refreshLog() {
      if (!this.onlineFlag) return;
      if (!this.logDataCopy.length) return;
      this.http_getDebugList();
    },
    // 日志列表请求
    http_getDebugList() {
      const time = this.logDataCopy[0].time || "";
      const data = {
        deviceName: this.deviceName,
        productKey: this.productKey,
        time: time,
      };
      getDebugList(data).then((res) => {
        if (res.code == 200) {
          const result = res.data.map((item) => {
            let obj = {};
            obj.title = item.typeName;
            obj.time = item.time;
            obj.character = item.content;
            return obj;
          });
          this.$store.commit("MUTATIONS_ONLINE__DEBUGLOGDATA", result);
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    // 清空日志
    clearLog() {
      if (!this.logList.length) return;
      this.$store.commit("MUTATIONS_ONLINE__DEBUGLOGCLEAR");
    },
    handleReset() {},
  },
};
</script>
<style lang="scss" scoped>
.debug-log {
  width: 912px;
  .debug-top {
    margin-bottom: 18px;
    height: 34px;
    line-height: 34px;
    justify-content: space-between;
    .debug-left {
      font-size: 16px;
      font-weight: 500;
      .device-content {
        .device-status {
          margin-left: 14px;
          height: 34px;
          line-height: 34px;
          font-size: 14px;
          font-weight: 400;
          .red {
            background: #ff4d4f;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 14px;
            margin-right: 6px;
          }
          .green {
            background: #00c250;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 14px;
            margin-right: 6px;
          }
        }
      }
    }
    .debug-right {
      font-weight: 400;
      /deep/ {
        .el-switch {
          height: 34px;
          margin-top: -4px;
        }
      }
      span {
        font-size: 14px;
        font-weight: 400;
        color: #018aff;
        margin-left: 24px;
        cursor: pointer;
        &:nth-child(2) {
          color: #515151;
          margin-left: 12px;
          cursor: default;
        }
      }
    }
  }
  .debug-content {
    border: 1px solid #eeeff1;
    height: 618px;
    overflow-y: auto;
    box-shadow: inset 0px 0px 8px rgba(0, 0, 0, 0.08);
    .content-top {
      height: 48px;
      background: #f7f7f7;
      span {
        font-size: 14px;
        font-weight: 500;
        color: #515151;
        height: 48px;
        line-height: 48px;
        margin-left: 18px;
        &:first-child {
          width: 150px;
        }
      }
    }
  }
}
.code-empty {
  height: 100%;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  img {
    width: 90px;
    height: 108px;
  }
  p {
    font-size: 14px;
    line-height: 16px;
    padding-top: 28px;
    color: #888888;
    span {
      color: #018aff;
    }
  }
}
</style>
