<template>
  <div>
    <p class="title">执行条件{{ index + 1 }}</p>
    <div class="flex">
      <!-- 执行条件 -->
      <el-form-item
        :prop="`${prop}.condition`"
        :ref="`${prop}.condition`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.condition"
          placeholder="请选择执行条件"
          @change="conditionChange"
        >
          <el-option value="status" label="设备状态"></el-option>
          <el-option value="timeRange" label="时间范围"></el-option>
        </el-select>
      </el-form-item>

      <!-- 设备状态判断 -->
      <template v-if="attr.condition == 'status'">
        <!-- 产品 -->
        <el-form-item
          :prop="`${prop}.productKey`"
          :ref="`${prop}.productKey`"
          :key="`${prop}.productKey`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
        >
          <el-select
            v-model="attr.productKey"
            placeholder="请选择产品"
            filterable
            @change="productChange"
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :value="item.productKey"
              :label="item.name"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item
          :prop="`${prop}.deviceName`"
          :ref="`${prop}.deviceName`"
          :key="`${prop}.deviceName`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
        >
          <el-select
            v-model="attr.deviceName"
            :disabled="!attr.productKey"
            placeholder="请选择设备"
            v-el-loadmore="getDeviceData"
            filterable
            clearable
            :filter-method="remoteDevice"
            @clear="remoteDevice('')"
          >
            <el-option
              v-for="item in deviceList"
              :key="`${item.id}condition`"
              :value="item.deviceName"
              :label="item.deviceName"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
      </template>

      <!-- 时间范围 -->
      <div class="flex" v-if="attr.condition == 'timeRange'">
        <!-- 在线离线 -->
        <el-form-item
          :prop="`${prop}.beginDate`"
          :ref="`${prop}.beginDate`"
          :key="`${prop}.beginDate`"
          :rules="[
            {
              validator: dateValidate,
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <el-date-picker
            v-model="attr.beginDate"
            type="datetime"
            placeholder="起始时间YYYY-MM-DD hh:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item
          :prop="`${prop}.endDate`"
          :ref="`${prop}.endDate`"
          :key="`${prop}.endDate`"
          :rules="[
            {
              validator: dateValidate,
              trigger: ['blur', 'change'],
            },
          ]"
        >
          <el-date-picker
            v-model="attr.endDate"
            type="datetime"
            placeholder="结束时间YYYY-MM-DD hh:mm:ss"
          >
          </el-date-picker>
        </el-form-item>
      </div>
    </div>

    <div class="flex" v-if="attr.condition == 'status'">
      <!-- 属性  只有属性  -->
      <el-form-item
        :prop="`${prop}.propertyName`"
        :ref="`${prop}.propertyName`"
        :key="`${prop}.propertyName`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.propertyName"
          placeholder="请选择属性/事件"
          :disabled="!attr.productKey"
          @change="propChange"
        >
          <el-option
            v-for="item in propertyList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
          <template slot="empty">
            <div class="empty-select">
              <span>暂无数据</span>
            </div>
          </template>
        </el-select>
      </el-form-item>

      <!-- 具体事件 输出列表 -->
      <el-form-item
        v-if="propetyMode == 'event' && attr.propertyName && attr.deviceName"
        :prop="`${prop}.eventCode`"
        :ref="`${prop}.eventCode`"
        :key="`${prop}.eventCode`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.eventCode"
          placeholder="请选择输出参数"
          @change="eventCodeChange"
        >
          <el-option
            v-for="item in eventList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>

      <!-- 比较方式  根据属性变化获取下拉选项 -->
      <el-form-item
        v-if="attr.propertyName && attr.deviceName"
        :prop="`${prop}.compareType`"
        :ref="`${prop}.compareType`"
        :key="`${prop}.compareType`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select v-model="attr.compareType" placeholder="请选择比较模式">
          <el-option
            v-for="item in compareList"
            :key="item.symbol"
            :value="item.value"
            :label="item.symbol"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- 具体值 -->
      <template
        v-if="
          attr.propertyName && attr.deviceName && compareValueType != 'object'
        "
      >
        <el-form-item
          :prop="`${prop}.compareValue`"
          :ref="`${prop}.compareValue`"
          :rules="[
            {
              validator:
                compareValueType != 'enum' &&
                compareValueType != 'bool' &&
                compareValueType != 'time'
                  ? compareValue
                  : selectValidate,
              trigger:
                compareValueType != 'enum' &&
                compareValueType != 'bool' &&
                compareValueType != 'time'
                  ? 'blur'
                  : 'change',
            },
          ]"
        >
          <el-input
            v-model="attr.compareValue"
            v-if="
              compareValueType != 'enum' &&
              compareValueType != 'bool' &&
              compareValueType != 'time'
            "
            :disabled="!attr.compareType"
            placeholder="请输入比较值"
          >
          </el-input>
          <el-date-picker
            v-else-if="compareValueType == 'time'"
            v-model="attr.compareValue"
            value-format="yyyy-MM-dd HH:mm:ss"
            type="datetime"
            placeholder="请选择时间"
          >
          </el-date-picker>
          <el-select
            v-else
            v-model="attr.compareValue"
            :disabled="!attr.compareType"
            placeholder="请选择比较值"
          >
            <el-option
              v-for="item in compareValueList"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
      <template
        v-if="
          attr.propertyName && attr.deviceName && compareValueType == 'object'
        "
      >
        <div>
          <custom-item
            v-for="(item, index) in attr.objectList"
            :key="item.id"
            :attr="item"
            :index="index"
            :prop="`${prop}.objectList.${index}`"
          ></custom-item>
        </div>
      </template>
    </div>
  </div>
</template>

<script>
import { compareData, fn_util__date_format } from "@/util/util";
import { productAbilityDetail } from "@/api/product";
import { getDeviceList } from "@/api/device";
import {
  checkDefault,
  checkValue,
  arrayValidate,
  formatModel,
} from "../ruleEdit/rules.js";
import customItem from "../customItem";
export default {
  data() {
    return {
      deviceList: [], //设备列表
      compareList: [], ////比较模式数据
      propertyList: [], //属性下拉列表
      propetyMode: "", ////当前选择的 ？ 属性-prop / 服务-event
      eventList: [], /// out 事件列表
      modelData: {}, //物模型结构
      propertyObject: {}, //存放当前选择的属性规则
      compareValueType: "", // 用于区分 select  与input 的输入方式
      devicePageAll: 1,
      devicePage: 0, //设备分页页码
      productId: "", //产品 id
    };
  },
  components: { customItem },
  props: {
    attr: {
      type: Object,
    },
    index: {
      type: [String, Number],
    },
    tenant_id: {
      type: String,
    },
    prop: {
      type: String,
    },
    productList: {
      type: Array,
    },
  },
  watch: {
    productList() {
      this.defaultSelectData();
    },
  },
  methods: {
    defaultSelectData() {
      // 判断设备列表
      if (this.attr.productKey) {
        //
        // 判断产品是否删除
        let product = this.productList.find(
          (item) => item.productKey == this.attr.productKey
        );

        if (!product) {
          // 如果产品不存在  清空后续
          this.reserItem([
            "productKey",
            "deviceName",
            "propertyName",
            "compareType",
            "compareValue",
            "beginDate",
            "endDate",
            "objectList",
            "compareValueType",
            "propMode",
          ]);
          this.$newNotify.warning({
            message: "执行条件中所选产品不存在",
          });
          return;
        }
        let e = this.attr.productKey;
        this.productId = this.productList.filter(
          (item) => item.productKey == e
        )[0].id;
        //
        this.checkDevice(this.attr.deviceName);
        this.getDeviceData(); //产品id
        this.getModelData(e); // 产品key
      }
    },
    // 下拉选择框验证   输入框特殊处理
    selectValidate(rule, value, callback) {
      // 执行条件选了  就进入验证
      if (this.attr.condition) {
        let key = rule.field.split(".")[2] || "";
        if (value == undefined || value == "") {
          callback(this.callbackTips(key, value));
        } else callback();
      } else {
        callback();
      }
    },
    // 日期选择验证
    dateValidate(rule, value, callback) {
      if (rule.field.includes("beginDate")) {
        //开始时间
        if (value) {
          if (this.attr.endDate) {
            let beginDate = fn_util__date_format(value).timestamp;
            let endDate = fn_util__date_format(this.attr.endDate).timestamp;
            if (beginDate > endDate) {
              callback("开始时间不能大于结束时间");
              return;
            }
          }
          callback();
        } else {
          callback("请选择开始时间");
          return;
        }
      } else {
        // 结束时间
        if (value !== "") {
          if (this.attr.beginDate) {
            let endDate = fn_util__date_format(value).timestamp;
            let beginDate = fn_util__date_format(this.attr.beginDate).timestamp;
            if (beginDate > endDate) {
              callback("开始时间不能大于结束时间");
              return;
            }
          }
          callback();
        } else {
          callback("请选择结束时间");
        }
      }
    },
    callbackTips(key) {
      let result = "";
      switch (key) {
        case "productKey":
          result = "请选择产品";
          break;
        case "deviceName":
          result = "请选择设备";
          break;
        case "propertyName":
          result = "请选择属性";
          break;
        case "compareType":
          result = "请选择比较模式";
          break;
        case "beginDate":
          result = "请选择开始时间";
          break;
        case "endDate":
          result = "请选择结束时间";
          break;
      }
      return result;
    },
    // 清空子项   deviceName  triggerShape propertyName eventCode compareType compareValue
    reserItem(list) {
      if (!Array.isArray(list) && list.length == 0) return;
      for (let i = 0; i < list.length; i++) {
        if (list[i] == "compareValueType") {
          this.compareValueType = "";
          continue;
        }
        if (list[i] == "objectList") {
          this.$emit("formSet", {
            key: `${this.prop}.objectList`,
            value: [],
          });
          continue;
        }
        if (list[i] == "propMode") {
          this.$emit("formSet", {
            key: `${this.prop}.propMode`,
            value: "",
          });
          continue;
        }
        if (list[i] == "deviceName") {
          this.devicePage = 0;
          this.devicePageAll = 1;
          this.deviceList = [];
        }
        if (list[i] == "propertyName") {
          //  属性名 / 服务名
          this.propertyObject = {}; // 属性规则对象
          this.propertyList = [];
          this.compareValueType = "";
        }
        if (list[i] == "compareType") {
          this.compareList = [];
        }
        if (list[i] == "productKey") {
          this.productId = "";
        }
        // this.attr[list[i]] &&    // 执行条件没有 上下线的特例  0   // 可能存在隐式转换无法通过判断
        if (this.attr[list[i]] && this.$refs[`${this.prop}.${list[i]}`]) {
          // 清空子项
          //  回显时 无法 使用form 的重置方法    因为回显时会吧form 的默认值修改
          // 非表单项清空
          // this.$refs[`${this.prop}.${list[i]}`].resetField();
          this.attr[list[i]] = "";
          this.$nextTick(() => {
            if (
              this.$refs[`${this.prop}.${list[i]}`] &&
              this.$refs[`${this.prop}.${list[i]}`].clearValidate
            ) {
              this.$refs[`${this.prop}.${list[i]}`].clearValidate(
                `${this.prop}.${list[i]}`
              );
            }
          });
        }
      }
    },
    conditionChange() {
      // 后续项清空
      this.reserItem([
        "productKey",
        "deviceName",
        "propertyName",
        "compareType",
        "compareValue",
        "beginDate",
        "endDate",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
    },
    // 产品下拉变化
    productChange(e) {
      // 产品变化   需清空设备下拉列表 属性列表     并重新获取 产品物模型  产品设备列表
      this.reserItem([
        "deviceName",
        "propertyName",
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.productId = this.productList.filter(
        (item) => item.productKey == e
      )[0].id;
      this.getDeviceData(); //产品id
      this.getModelData(e); // 产品key
    },
    // 设备搜索
    remoteDevice(val) {
      this.devicePage = 0;
      this.devicePageAll = 1;
      this.deviceList = [];
      if (typeof val != "string" || val == "") {
        this.getDeviceData();
      } else {
        this.getDeviceData(val);
      }
    },
    checkDevice(deviceName = "") {
      if (deviceName == "_all") return;
      getDeviceList({
        productId: this.productId,
        deviceName: deviceName != "_all" ? deviceName : "",
        current: this.devicePage,
        size: 10,
      }).then((res) => {
        if (res.code == 200) {
          let deviceInfo = res.data.records.find(
            (item) => item.deviceName == this.attr.deviceName
          );
          if (!deviceInfo) {
            // 未找到   设备已被删除
            // 清空设备项    后续与设备项无关 无需清除
            this.reserItem(["deviceName"]);

            this.$newNotify.warning({
              message: "执行条件中所选设备不存在",
            });
            return;
          }
        }
      });
    },
    // 获取设备列表
    getDeviceData(deviceName = "") {
      this.devicePage++;
      if (this.devicePage > this.devicePageAll) return;
      getDeviceList({
        productId: this.productId,
        deviceName,
        current: this.devicePage,
        size: 10,
      }).then((res) => {
        this.deviceList = this.deviceList.concat(res.data.records);
        this.devicePageAll = res.data.pages;
        this.$forceUpdate();
      });
    },
    // 获取产品物模型
    getModelData(productKey) {
      productAbilityDetail({
        // tenantId: this.tenant_id,
        productKey,
      }).then((res) => {
        if (res.code == 200 && typeof res.data == "string") {
          let model = JSON.parse(res.data);
          this.modelData = model;
          let eventList = model.events.filter((item) => item.id != "post");
          this.propertyList = model.properties.concat(eventList);
        } else {
          this.modelData = {
            properties: [],
            events: [],
            services: [],
          };
        }
        // 回显  -------------------------
        if (this.attr.propertyName) {
          //// 处理后续之前 判断 属性是否存在于当前 属性列表
          let property = this.propertyList.find(
            (item) => item.id == this.attr.propertyName
          );
          if (!property) {
            this.reserItem([
              "propertyName",
              "eventCode",
              "compareType",
              "compareValue",
              "objectList",
              "compareValueType",
              "propMode",
            ]);
            this.$newNotify.warning({
              message: "执行条件中所选属性/事件不存在",
            });
            return;
          }
          this.propFormat(this.attr.propertyName);
        }
      });
    },
    propChange(e) {
      // 需清空后续项
      this.reserItem([
        "compareType",
        "compareValue",
        "eventCode",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.propFormat(e);
    },
    propFormat(e) {
      let result = this.propertyList.find((item) => item.id == e);
      if (result.action) {
        // 事件
        this.propetyMode = "event";
        this.eventList = result.out || [];
        // 回显   已选择事件 ------------------------
        if (this.attr.eventCode) {
          this.BasicPropChange(this.eventList, this.attr.eventCode);
        }
      } else {
        // 属性
        this.propetyMode = "prop";
        this.BasicPropChange(this.propertyList, e);
      }
    },
    // 事件  out 下参数变化
    eventCodeChange(e) {
      this.reserItem([
        "compareType",
        "compareValue",
        "objectList",
        "compareValueType",
        "propMode",
      ]);
      this.BasicPropChange(this.eventList, e);
    },
    // 属性变化   基本类型  (源数组，基准值)   处理比较类型数据及设置规则
    BasicPropChange(list, e) {
      let result = list.find((item) => item.id == e);
      this.propertyObject = result;
      this.compareValueType = result.data.type;
      this.$emit("formSet", {
        key: `${this.prop}.propMode`,
        value: this.compareValueType,
      });
      this.compareList = compareData.find(
        (item) => item.type == result.data.type
      ).condition;
      if (this.compareValueType == "bool" || this.compareValueType == "enum") {
        let list = [];
        for (let i in result.data.rules) {
          list.push({
            id: i,
            label: `${result.data.rules[i]} - ${i}`,
          });
        }
        this.compareValueList = list;
      } else if (this.compareValueType == "object") {
        // 对象
        let list = formatModel(result.data.rules);
        list = list.map((item) => {
          if (this.attr.objectList) {
            // 服务数据值回显
            let historyData = this.attr.objectList.find(
              (item2) => item2.id == item.id
            );
            if (historyData) {
              item.value = historyData.value;
            }
          }
          return item;
        });
        this.$emit("formSet", {
          key: `${this.prop}.objectList`,
          value: list,
        });
      }
    },
    // 输入值验证
    compareValue(rule, value, callback) {
      // this.propertyObject  存放当前输入项的规则
      if (value !== "" && value !== undefined) {
        let { rules, type } = this.propertyObject.data;
        if (type != "array" && type != "object") {
          // 验证基本类型
          let result = checkDefault(type, value, rules);
          if (result.flag) {
            callback(result.text);
          } else {
            callback();
          }
        } else if (type == "object") {
          let flag = checkValue(type, value, rules);
          flag ? callback("对象格式输入有误") : callback();
        } else if (type == "array") {
          // 数组 或 对象
          arrayValidate(
            value,
            rules,
            callback,
            `输入格式有误，请输入array(${rules.item.type})格式`
          );
        }
        callback();
      } else {
        callback("请输入比较值");
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #515151;
  font-size: 14px;
  padding-bottom: 8px;
}
:deep(.el-form-item) {
  margin-right: 14px;
}
:deep(.el-input__inner) {
  border-radius: 0;
  width: 224px;
}
:deep(.el-form-item__content) {
  line-height: 34px;
}
:deep(.el-form-item__error) {
  white-space: nowrap;
}
</style>
