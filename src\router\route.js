/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-02-12 14:33:38
 */
export default [
  // {
  //     path: '/',
  //     name: 'index',
  //     component: () => import('@/layout'),
  //     childrem:[]
  // }
  {
    path: "/",
    name: "home",
    redirect: "/product",
    component: () => import("@/views/layout"),
    children: [
      // {
      //   path: "/overview",
      //   name: "overview",
      //   meta: {
      //     title: "概览",
      //     isLogin: true,
      //     isShow: true,
      //     icon: "gailan",
      //     crumb: [{ name: "概览", sort: 0 }],
      //   },
      //   component: () => import("@/views/overview"),
      // },
      {
        path: "/equipment",
        name: "equipment",
        meta: {
          title: "设备管理",
          isLogin: true,
          isShow: true,
          isKeepActive: false,
          icon: "shebeiguan<PERSON>",
          crumb: [{ name: "设备管理", sort: 0, url: "/equipment" }],
        },

        children: [
          // 2022-4-20  暂时隐藏  上级要求
          // {
          //   path: "/project",
          //   name: "project",
          //   meta: {
          //     title: "项目",
          //     isLogin: true,
          //     isShow: true,
          //     isKeepActive: true,
          //     crumb: [
          //       { name: "设备管理", sort: 0, url: "/equipment" },
          //       { name: "项目", url: "/project", sort: 1 },
          //     ],
          //   },

          //   component: () => import("@/views/equipment/project"),
          // },
          {
            path: "/product",
            name: "product",
            meta: {
              title: "产品管理",
              isLogin: true,
              isShow: true,
              isKeepActive: true,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "产品管理", url: "/product", sort: 1 },
              ],
            },

            component: () => import("@/views/equipment/product"),
          },
          {
            path: "/device",
            name: "device",
            meta: {
              title: "设备管理",
              isLogin: true,
              isShow: true,
              isKeepActive: true,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "设备管理", url: "/device", sort: 1 },
              ],
            },

            component: () => import("@/views/equipment/device"),
          },
          {
            path: "/group",
            name: "group",
            meta: {
              title: "设备分组",
              isLogin: true,
              isShow: false,
              isKeepActive: true,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "设备分组", url: "/group", sort: 1 },
              ],
            },

            component: () => import("@/views/equipment/deviceGroup"),
          },
          {
            path: "/groupDetail",
            name: "groupDetail",
            meta: {
              title: "分组详情",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "设备分组", url: "/group", sort: 1 },
                { name: "分组详情", url: "/groupDetail", sort: 2 },
              ],
            },

            component: () => import("@/views/equipment/deviceGroupDetail"),
          },
          {
            path: "/productDetail",
            name: "productDetail",
            meta: {
              title: "title",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "产品", url: "/product", sort: 1 },
                { name: "产品详情", url: "/product", sort: 2 },
              ],
            },
            component: () => import("@/views/equipment/productDetail"),
          },
          {
            path: "/deviceDetail",
            name: "deviceDetail",
            meta: {
              title: "设备",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "设备", url: "/device", sort: 1 },
                { name: "设备详情", url: "/deviceDetail", sort: 2 },
              ],
            },
            component: () => import("@/views/equipment/deviceDetail"),
          },
          {
            path: "/deviceChildDetail",
            name: "deviceChildDetail",
            meta: {
              title: "设备",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "设备管理", sort: 0, url: "/equipment" },
                { name: "设备", url: "/device", sort: 1 },
                { name: "设备详情", url: "/device", sort: 2 },
                { name: "子设备详情", url: "/device", sort: 3 },
              ],
            },
            component: () => import("@/views/equipment/deviceDetail"),
          },
        ],
        component: () => import("@/pages/router"),
      },
      {
        path: "/ruleEngine",
        name: "ruleEngine",
        meta: {
          title: "规则引擎",
          isLogin: true,
          isShow: true,
          isKeepActive: false,
          icon: "guizeyinqing",
          crumb: [{ name: "规则引擎", sort: 0, url: "/ruleEngine" }],
        },
        component: () => import("@/pages/router"),
        children: [
          {
            path: "/cloudSub",
            name: "cloudSub",
            meta: {
              title: "云端订阅",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              crumb: [
                { name: "规则引擎", sort: 0, url: "/ruleEngine" },
                { name: "云端订阅", url: "/cloudSub", sort: 1 },
              ],
            },

            component: () => import("@/views/ruleEngine/cloudSub"),
          },
          {
            path: "/sceneLink",
            name: "sceneLink",
            meta: {
              title: "场景联动",
              isLogin: true,
              isShow: true,
              isKeepActive: true,
              crumb: [
                { name: "规则引擎", sort: 0, url: "/ruleEngine" },
                { name: "场景联动", url: "/sceneLink", sort: 1 },
              ],
            },

            component: () => import("@/views/ruleEngine/sceneLink"),
          },
          {
            path: "/ruleEdit",
            name: "ruleEdit",
            meta: {
              title: "场景联动详情",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "规则引擎", sort: 0, url: "/ruleEngine" },
                { name: "场景联动", url: "/sceneLink", sort: 1 },
                { name: "场景联动详情", url: "/ruleEdit", sort: 2 },
              ],
            },

            component: () =>
              import("@/views/ruleEngine/sceneLink/components/ruleEdit"),
          },
          {
            path: "/logDetail",
            name: "logDetail",
            meta: {
              title: "运行日志",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "规则引擎", sort: 0, url: "/ruleEngine" },
                { name: "场景联动", url: "/sceneLink", sort: 1 },
                { name: "运行日志", url: "/logDetail", sort: 2 },
              ],
            },

            component: () =>
              import("@/views/ruleEngine/sceneLink/components/logDetail"),
          },
        ],
      },
      {
        path: "/monitorMaintain",
        name: "monitorMaintain",
        meta: {
          title: "监控运维",
          isLogin: true,
          isShow: true,
          isKeepActive: false,
          icon: "yunweijiankong",
          crumb: [{ name: "监控运维", sort: 0, url: "/monitorMaintain" }],
        },
        component: () => import("@/pages/router"),
        children: [
          {
            path: "/onlineDebug",
            name: "onlineDebug",
            meta: {
              title: "在线调试",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "在线调试", url: "/onlineDebug", sort: 1 },
              ],
            },

            component: () => import("@/views/monitorMaintain/onlineDebug"),
          },
          {
            path: "/firmwareUpdate",
            name: "firmwareUpdate",
            meta: {
              title: "固件升级",
              isLogin: true,
              isShow: true,
              isKeepActive: true,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "固件升级", url: "/firmwareUpdate", sort: 1 },
              ],
            },

            component: () => import("@/views/monitorMaintain/firmwareUpdate"),
          },
          {
            path: "/firmwareUpdateDetail",
            name: "firmwareUpdateDetail",
            meta: {
              title: "任务升级详情",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "固件升级", url: "/firmwareUpdate", sort: 1 },
                { name: "任务升级详情", url: "/firmwareUpdateDetail", sort: 2 },
              ],
            },
            component: () =>
              import(
                "@/views/monitorMaintain/firmwareUpdate/components/updateDetail"
              ),
          },
          {
            path: "/firmwareUpdateInfo",
            name: "firmwareUpdateInfo",
            meta: {
              title: "固件升级详情",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "固件升级", url: "/firmwareUpdate", sort: 1 },
                { name: "固件升级详情", url: "/firmwareUpdateInfo", sort: 2 },
              ],
            },
            component: () =>
              import(
                "@/views/monitorMaintain/firmwareUpdate/components/updateInfo"
              ),
          },
          {
            path: "/alarmCenter",
            name: "alarmCenter",
            meta: {
              title: "告警中心",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              from:'alarmCenter',
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "告警中心", url: "/alarmCenter", sort: 1 },
              ],
            },

            component: () => import("@/views/monitorMaintain/alarmCenter"),
          },
          {
            path: "/alarmeventHanding",
            name: "alarmeventHanding",
            meta: {
              title: "事件详情",
              isLogin: true,
              isShow: false,
              isKeepActive: true,
              from:'alarmeventHanding',
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "告警中心", url: "/alarmCenter", sort: 1 },
                { name: "告警列表", url: "/alarmList", sort: 2 },
                { name: "事件详情", url: "/alarmeventHanding", sort: 3 },
              ],
            },

            component: () => import("@/views/monitorMaintain/alarmEventHanding"),
          },
          {
            path: "/gatewayMaintenance",
            name: "gatewayMaintenance",
            meta: {
              title: "网关运维",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
              ],
            },
            component: () => import("@/views/monitorMaintain/gatewayMaintenance"),
          },
          {
            path: "/gatewaytodayevent",
            name: "gatewaytodayevent",
            meta: {
              title: "网关事件列表",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
                { name: "网关事件列表", url: "/gatewaytodayevent", sort: 2 },
              ],
            },
            component: () => import("@/views/monitorMaintain/gatewayTodayEvents"),
          },
          {
            path: "/gatewaydetail",
            name: "gatewaydetail",
            meta: {
              title: "网关详情",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
                { name: "网关详情", url: "/gatewaydetail", sort: 2 },
              ],
            },
            component: () => import("@/views/monitorMaintain/gatewayDetail"),
          },
          {
            path: "/abnormalevents",
            name: "abnormalevents",
            meta: {
              title: "事件监控列表",
              isLogin: true,
              isShow: false,
              isKeepActive: false,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
                { name: "网关详情", url: "/gatewaydetail", sort: 2 },
                { name: "事件监控列表", url: "/abnormalevents", sort: 3 },
              ],
            },

            component: () => import("@/views/monitorMaintain/abnormalevents"),
          },
          {
            path: "/eventHanding",
            name: "eventHanding",
            meta: {
              title: "事件详情",
              isLogin: true,
              isShow: false,
              isKeepActive: true,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
                { name: "网关详情", url: "/gatewaydetail", sort: 2 },
                { name: "事件详情", url: "/eventHanding", sort: 3 },
              ],
            },

            component: () => import("@/views/monitorMaintain/eventHanding"),
          },
          {
            path: "/todayEventHanding",
            name: "todayEventHanding",
            meta: {
              title: "事件详情",
              isLogin: true,
              isShow: false,
              isKeepActive: true,
              crumb: [
                { name: "监控运维", sort: 0, url: "/monitorMaintain" },
                { name: "网关运维", url: "/gatewayMaintenance", sort: 1 },
                { name: "网关事件列表", url: "/gatewaytodayevent", sort: 2 },
                { name: "事件详情", url: "/todayEventHanding", sort: 3 },
              ],
            },

            component: () => import("@/views/monitorMaintain/todayEventHanding"),
          },
        ],
      },
      // {
      //   path: "/basicservices",
      //   name: "basicservices",
      //   meta: {
      //     title: "基础服务",
      //     isLogin: true,
      //     isShow: true,
      //     isKeepActive: false,
      //     icon: "jichufuwu",
      //     crumb: [{ name: "基础服务", sort: 0}],
      //   },
      //   children:[
      //     {
      //       path: "/appEmpower",
      //       name: "appEmpower",
      //       meta: {
      //         title: "应用授权",
      //         isLogin: true,
      //         isShow: true,
      //         isKeepActive: true,
      //         crumb: [
      //           { name: "基础服务", sort: 0, url: "/basicservices" },
      //           { name: "应用授权", sort: 1 , url: "/appEmpower"},
      //         ],
      //       },

      //       component: () => import("@/views/Basicservices/appEmpower"),
      //     },
      //     {
      //       path: "/datasubscription",
      //       name: "datasubscription",
      //       meta: {
      //         title: "数据订阅",
      //         isLogin: true,
      //         isShow: true,
      //         isKeepActive: true,
      //         crumb: [
      //           { name: "基础服务", sort: 0, url: "/basicservices" },
      //           { name: "数据订阅", sort: 1 , url: "/datasubscription"},
      //         ],
      //       },

      //       component: () => import("@/views/Basicservices/dataSubscription"),
      //     },
      //   ],
      //   component: () => import("@/pages/router"),
      // },
      {
        path: "/accountManage",
        name: "accountManage",
        meta: {
          title: "系统管理",
          isLogin: true,
          isShow: true,
          isKeepActive: false,
          icon: "xitonggongneng",
          crumb: [{ name: "系统管理", sort: 0, url: "/accountManage" }],
        },
        component: () => import("@/pages/router"),
        children: [
          {
            path: "/AccessKey",
            name: "AccessKey",
            meta: {
              title: "AccessKey",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              crumb: [
                { name: "系统管理", sort: 0, url: "/accountManage" },
                { name: "AccessKey", url: "/AccessKey", sort: 1 },
              ],
            },

            component: () => import("@/views/accountManage/AccessKey"),
          },
          {
            path: "/accountInfo",
            name: "accountInfo",
            meta: {
              title: "账号信息",
              isLogin: true,
              isShow: true,
              isKeepActive: false,
              crumb: [
                { name: "系统管理", sort: 0, url: "/accountManage" },
                { name: "账号信息", url: "/accountInfo", sort: 1 },
              ],
            },

            component: () => import("@/views/accountManage/accountInfo"),
          },
        ],
      },
      //隐藏边缘管理菜单
      // {
      //   path: "/edgeManagement",
      //   name: "edgeManagement",
      //   meta: {
      //     title: "边缘管理",
      //     isLogin: true,
      //     isShow: true,
      //     isKeepActive: false,
      //     icon: "xitonggongneng",
      //     crumb: [{ name: "边缘管理", sort: 0, url: "/edgeManagement" }],
      //   },
      //   component: () => import("@/pages/router"),
      //   children:[
      //     {
      //       path: "/edgeNode",
      //       name: "edgeNode",
      //       meta: {
      //         title: "边缘节点",
      //         isLogin: true,
      //         isShow: true,
      //         isKeepActive: false,
      //         crumb: [
      //           { name: "边缘管理", sort: 0, url: "/edgeManagement" },
      //           { name: "边缘节点", url: "/edgeNode", sort: 1 },
      //         ],
      //       },

      //       component: () => import("@/views/edgeManagement/edgeNode"),
      //     },
      //     {
      //       path: "/edgeDevice",
      //       name: "edgeDevice",
      //       meta: {
      //         title: "边缘节点",
      //         isLogin: true,
      //         isShow: false,
      //         isKeepActive: true,
      //         crumb: [
      //           { name: "边缘管理", sort: 0, url: "/edgeManagement" },
      //           { name: "边缘节点", url: "/edgeNode", sort: 1 },
      //         ],
      //       },
      //       component: () => import("@/views/edgeManagement/edgeDevice"),
      //     },
      //     {
      //       path: "/edgeDeviceDetail",
      //       name: "edgeDeviceDetail",
      //       meta: {
      //         title: "边缘节点",
      //         isLogin: true,
      //         isShow: false,
      //         isKeepActive: false,
      //         crumb: [
      //           { name: "边缘管理", sort: 0, url: "/edgeManagement" },
      //           { name: "边缘节点", url: "/edgeNode", sort: 1 },
      //         ],
      //       },
      //       component: () => import("@/views/edgeManagement/edgeDeviceDetail"),
      //     }
         
      //   ]
      // },
    ],
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login"),
    meta: {
      title: "登录",
      isLogin: false,
      isKeepActive: false,
    },
  },
  {
    path: "/register",
    name: "register",
    component: () => import("@/views/register"),
    meta: {
      title: "注册",
      isLogin: false,
      isKeepActive: false,
    },
  },
  {
    path: "/reset",
    name: "reset",
    component: () => import("@/views/reset"),
    meta: {
      title: "重置密码",
      isLogin: false,
      isKeepActive: false,
    },
  },
  {
    path: "/agreement",
    name: "agreement",
    component: () => import("@/views/agreement"),
    meta: {
      title: "协议",
      isLogin: false,
      isKeepActive: false,
    },
  },
  {
    path: "/404",
    component: () => import("@/views/empty/404"),
  },
  {
    path: "*",
    redirect: "/404",
    component: () => import("@/views/empty/404"),
  },
];
