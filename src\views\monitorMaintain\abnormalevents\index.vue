<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: --
 * @Date: 2023-8-26 16:54
 * @LastEditors: --
 * @LastEditTime: 2023-8-26 16:54
-->

<template>
  <div id="main">
    <div class="searchNav">
      <el-select v-model="params.alarmType" @change="clear_optionsData($event)">
        <el-option
          v-for="item in searchOptonsList"
          :key="item.value"
          :label="item.name"
          :value="item.value"
        >
        </el-option>
      </el-select>
      <div class="searh_optionsBox">
        <el-input
          v-if="params.alarmType == 0"
          class="alarm-input"
          v-model="params.keyword"
          @keyup.enter.native="handleSearch"
          @clear="handleClear"
          :disabled="params.alarmType == 0"
          placeholder="输入关键词搜索"
        >
        </el-input>

        <el-select
          v-model="params.keyword"
          placeholder="请选择事件等级"
          v-if="params.alarmType == 4"
        >
          <el-option
            v-for="item in alarmLevelList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="params.keyword"
          placeholder="请选择事件类型"
          v-if="params.alarmType == 3"
        >
          <el-option
            v-for="item in alarmClassList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
        <el-select
          v-model="params.keyword"
          placeholder="请选择事件状态"
          v-if="params.alarmType == 5"
        >
          <el-option
            v-for="item in alarmStatusList"
            :key="item.value"
            :label="item.name"
            :value="item.value"
          >
          </el-option>
        </el-select>
      </div>

      <el-date-picker
        v-model="dateArr"
        type="datetimerange"
        :picker-options="pickerOptions"
        range-separator="－"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        align="right"
        prefix-icon="riqi"
        class="custom-datepicker"
      >
      </el-date-picker>
      <iot-button
        text="查询"
        @search="handleSearch"
        style="margin: 0px 12px 0px 0px"
      />
      <iot-button text="重置" type="white" @search="fn_reset" />
    </div>

    <iot-table :columns="columns" :data="tableData" :loading="loading">
      <template slot="alarmType" slot-scope="scope">
        <div class="table-status">
          <div class="status flex" v-if="scope.row.alarmType == 1">
            <div>设备告警</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmType == 2">
            <div>业务告警</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmType == 3">
            <div>系统告警</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmType == 4">
            <div>其他告警</div>
          </div>
        </div>
      </template>
      <template slot="status" slot-scope="scope">
        <div class="table-status">
          <div class="status flex" v-if="scope.row.alarmLevel == 1">
            <div>紧急</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmLevel == 2">
            <div>重要</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmLevel == 3">
            <div>次重要</div>
          </div>
          <div class="status flex" v-if="scope.row.alarmLevel == 4">
            <div>提示</div>
          </div>
        </div>
      </template>
      <template slot="eventStatus" slot-scope="scope">
        <div class="table-status">
          <div class="status flex" v-if="scope.row.eventStatus == 0">
            <div class="red"></div>
            <div>待处理</div>
          </div>
          <div class="status flex" v-if="scope.row.eventStatus == 1">
            <div class="ccc"></div>
            <div>已关闭</div>
          </div>
        </div>
      </template>
      <template slot="operation" slot-scope="scope">
        <div class="flex table-edit">
          <p class="color2" @click="fn_toDetail(scope.row)">详情</p>
        </div>
      </template>
    </iot-table>
    <!-- 分页 -->
    <div class="produce-bottom" v-if="tableData.length">
      <iot-pagination
        :pagination="pagination"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
  </div>
</template>

<script>
import { getEventsDatail } from "@/api/monitorMaintain";
import IotButton from "@/components/iot-button";
import IotTable from "@/components/iot-table";
import IotPagination from "@/components/iot-pagination";
export default {
  components: {
    IotButton,
    IotTable,
    IotPagination,
  },
  created() {
    this.params.deviceName = this.$route.query.deviceName;
    this.params.productKey = this.$route.query.productKey;
  },
  mounted() {
    this.getList();
  },
  watch: {
    dateArr(newVal) {
      //处理时间选择器中获取到的时间
      if (Array.isArray(newVal)) {
        const [start, end] = newVal.map((time) => {
          const d = new Date(time);
          const year = d.getFullYear();
          const month = (d.getMonth() + 1).toString().padStart(2, "0");
          const day = d.getDate().toString().padStart(2, "0");
          const hours = d.getHours().toString().padStart(2, "0");
          const minutes = d.getMinutes().toString().padStart(2, "0");
          const seconds = d.getSeconds().toString().padStart(2, "0");
          return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
        });
        this.params.beginTime = start;
        this.params.endTime = end;
      }
    },
  },
  data() {
    return {
      params: {
        deviceName: "",
        productKey: "",
        alarmType: "0",
        beginTime: "",
        endTime: "",
        keyword: "",
      },
      dateArr: [],
      tableData: [],

      //   下拉框选项
      searchOptonsList: [
        { name: "全部", value: "0" },
        { name: "事件类型", value: "3" },
        { name: "事件等级", value: "4" },
        { name: "事件状态", value: "5" },
      ],
      alarmLevelList: [
        { name: "紧急", value: "1" },
        { name: "重要", value: "2" },
        { name: "次重要", value: "3" },
        { name: "提示", value: "4" },
      ],
      alarmClassList: [
        { name: "设备事件", value: "1" },
        { name: "业务事件", value: "2" },
        { name: "系统事件", value: "3" },
        { name: "其他事件", value: "4" },
      ],
      alarmStatusList: [
        { name: "待处理", value: "0" },
        { name: "已关闭", value: "1" },
      ],

      //时间选择器快速选择选项
      pickerOptions: {
        shortcuts: [
          {
            text: "昨天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - 1);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近7天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "近30天",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },
      columns: [
        { label: "DeviceName", prop: "deviceName" },
        { label: "设备名称", prop: "aliasName" },
        { label: "所属产品", prop: "productKey", width: 220 },
        { label: "事件类型", prop: "alarmType", slotName: "alarmType" },
        { label: "内容", prop: "updateMessage", width: 250 },
        { label: "事件等级", prop: "alarmLevel", slotName: "status" },
        { label: "状态", prop: "eventStatus", slotName: "eventStatus" },
        { label: "发生时间", prop: "createTime", width: 160 },
        { label: "关闭时间", prop: "closeTime", width: 160 },
        { label: "操作", prop: "operation", slotName: "operation" },
      ],
      loading: true,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
    };
  },
  methods: {
    getList() {
      //获取表格信息
      let data = {
        deviceName: this.params.deviceName,
        productKey: this.params.productKey,
        queryType: this.params.alarmType,
        keyword: this.params.keyword,
        beginTime: this.params.beginTime,
        endTime: this.params.endTime,
        ...this.pagination,
      };
      getEventsDatail(data)
        .then((res) => {
          if (200 == res.code) {
            res.data.records.map((item) => {
              item.updateMessage = item.message + "-" + item.content;
            });
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    // 查询
    handleSearch() {
      let params = {
        deviceName: this.params.deviceName,
        productKey: this.params.productKey,
        queryType: this.params.alarmType,
        keyword: this.params.keyword,
        beginTime: this.params.validBeginTime,
        endTime: this.params.validEndTime,
        ...this.pagination,
      };
      this.getList(params);
    },

    handleClear() {
      this.params.alarmType = "0";
      this.params.keyword = "";
      this.dateArr = [];
      this.getList();
    },
    //清除查询关键字
    clear_optionsData() {
      this.params.keyword = "";
    },
    //重置
    fn_reset() {
      (this.params.productKey = "P377jx5fX6WCIlMC"),
        (this.params.alarmType = "0");
      this.params.keyword = "";
      this.dateArr = [];
      this.getList();
    },
    // 当前页总条数
    handleSizeChange(val) {
      this.pagination.size = val;

      this.getList();
    },
    // 当前页
    handleCurrentChange(val) {
      this.pagination.current = val;

      this.getList();
    },
    fn_toDetail(row) {
      console.log(row);
      let data = {
        id: row.id,
        title: row.deviceName,
        status: row.eventStatus,
        // key: row.nodeTypeKey,
      };
      this.$store.dispatch("setLayoutInfo", data);
      this.$router.push({
        path: "eventHanding",
        query: {
          //封装成对象传参会导致无法保存数据，需优化
          alarmLevel: row.alarmLevel,
          alarmType: row.alarmType,
          aliasName: row.aliasName,
          closeTime: row.closeTime,
          content: row.content,
          createTime: row.createTime,
          createUser: row.createUser,
          createDept: row.createDept,
          deviceName: row.deviceName,
          eventIdentifier: row.eventIdentifier,
          eventStatus: row.eventStatus,
          id: row.id,
          isDeleted: row.isDeleted,
          message: row.message,
          productKey: row.productKey,
          status: row.status,
          tenantId: row.tenantId,
          updateTime: row.updateTime,
          updateUser: row.updateUser,
          processingDetails: row.processingDetails,
          processingPlan: row.processingPlan,
          processingSource: row.processingSource,
          productName: row.productName,
          projectName: row.projectName,
        },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
#main {
  padding: 0px 32px 0 32px;
  .searchNav {
    // background-color: rebeccapurple;
    display: flex;
    justify-content: end;
    align-items: center;
    padding-top: 16px;
    padding-bottom: 16px;
    .el-select {
      width: 188px;
      border-radius: 0;
      margin-right: 14px;
    }
    .searh_optionsBox {
      .el-input {
        width: 188px !important;
      }
      .el-range-editor {
        border-radius: 0;
        /deep/ .el-input__inner {
          padding: 0;
        }
        /deep/ .el-input__icon {
          height: auto;
        }
        /deep/ .el-range-separator {
          height: 32px;
        }
      }
      .el-select {
        width: 188px;
        border-radius: 0;
        margin-right: 14px;
        .el-select-dropdown__item {
          height: 38px;
          line-height: 38px;
        }
      }
      /deep/ .el-input__inner {
        border-radius: 0;
      }
      .alarm-input {
        width: 240px;
        margin-right: 14px;
      }
    }
    /deep/ {
      .el-input__inner {
        /* 移除边框的圆角 */
        border-radius: 0;
        font-family: H_Medium;
      }
      .el-option {
        margin: 0px;
      }
      .custom-datepicker {
        margin: 0px 12px 0px 0px;
      }
      .custom-datepicker .el-input__icon {
        /* 调整图标位置的样式 */
        // margin-right: 20px;
        margin-bottom: 9px;
      }
    }
  }
  .table-status {
    .status {
      .red {
        background: #ff4d4f;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-top: 8px;
        margin-right: 6px;
      }
      .green {
        background: #00c250;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-top: 8px;
        margin-right: 6px;
      }
      .yellow {
        background: #e6a23c;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-top: 8px;
        margin-right: 6px;
      }
      .ccc {
        background: #ccc;
        width: 8px;
        height: 8px;
        border-radius: 4px;
        margin-top: 8px;
        margin-right: 6px;
      }
    }
  }
  .table-edit {
    display: flex;
    align-items: center;
    p {
      cursor: pointer;
    }
    .table-line {
      margin: 0px 12px;
      width: 1px;
      height: 13px;
      border: 1px solid #ededed;
    }
  }
  .produce-bottom {
    text-align: right;
    margin-top: 16px;
    padding-bottom: 10px;
  }
}
</style>