<template>
  <div>
    <p class="title">执行动作{{ index + 1 }}</p>
    <div class="flex">
      <el-form-item
        :prop="`${prop}.actionMode`"
        :ref="`${prop}.actionMode`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.actionMode"
          placeholder="请选择执行动作"
          @change="actionChange"
        >
          <!-- 设备输出  属性 setProperty  -->
          <el-option value="device" label="设备输出"></el-option>
          <el-option value="scene" label="规则输出"></el-option>
          <!-- 告警输出  固定携带  triggerAlarm -->
          <el-option
            v-if="!isShowAlarm"
            value="event"
            label="告警输出"
          ></el-option>
        </el-select>
      </el-form-item>
      <template v-if="attr.actionMode == 'scene'">
        <!-- 规则id -->
        <el-form-item
          :prop="`${prop}.ruleId`"
          :ref="`${prop}.ruleId`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
          :key="`${prop}.ruleId`"
        >
          <el-select
            v-model="attr.ruleId"
            placeholder="请选择规则"
            v-el-loadmore="getRuleList"
            filterable
            clearable
            :filter-method="remoteMethod"
            @clear="remoteMethod"
          >
            <el-option
              v-for="item in ruleList"
              :key="item.id"
              :value="item.id"
              :label="item.name"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>

        <!-- 规则 操作   -->
        <el-form-item
          :prop="`${prop}.switchStatus`"
          :ref="`${prop}.switchStatus`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
          :key="`${prop}.switchStatus`"
        >
          <el-select v-model="attr.switchStatus" placeholder="执行操作">
            <el-option value="0" label="触发"></el-option>
            <el-option value="1" label="启用"></el-option>
            <el-option value="2" label="停止"></el-option>
          </el-select>
        </el-form-item>
      </template>
      <template v-if="attr.actionMode == 'device'">
        <!-- 产品 -->
        <el-form-item
          :prop="`${prop}.productKey`"
          :ref="`${prop}.productKey`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
          :key="`${prop}.productKey`"
        >
          <el-select
            v-model="attr.productKey"
            placeholder="请选择产品"
            filterable
            @change="productChange"
          >
            <el-option
              v-for="item in productList"
              :key="item.id"
              :value="item.productKey"
              :label="item.name"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
        <el-form-item
          :prop="`${prop}.deviceName`"
          :ref="`${prop}.deviceName`"
          :rules="[{ validator: selectValidate, trigger: 'change' }]"
          :key="`${prop}.deviceName`"
        >
          <!-- v-el-loadmore="getDeviceList" -->

          <!-- 远程搜索 remote -->
          <el-select
            v-model="attr.deviceName"
            :disabled="!attr.productKey"
            placeholder="请选择设备"
            v-el-loadmore="getDeviceData"
            filterable
            clearable
            :filter-method="remoteDevice"
            @clear="remoteDevice('')"
          >
            <!-- <el-option value="all" label="全部设备"></el-option> -->
            <el-option
              v-for="item in deviceList"
              :key="`${item.id}action`"
              :value="item.deviceName"
              :label="item.deviceName"
            ></el-option>
            <template slot="empty">
              <div class="empty-select">
                <span>暂无数据</span>
              </div>
            </template>
          </el-select>
        </el-form-item>
      </template>
    </div>

    <div class="flex" v-if="attr.actionMode == 'device'">
      <el-form-item
        :prop="`${prop}.propertyName`"
        :ref="`${prop}.propertyName`"
        :key="`${prop}.propertyName`"
        :rules="[{ validator: selectValidate, trigger: 'change' }]"
      >
        <el-select
          v-model="attr.propertyName"
          :disabled="!attr.productKey"
          placeholder="请选择属性/服务"
          @change="propertyChange"
        >
          <el-option
            v-for="item in propertyList"
            :key="item.id"
            :value="item.id"
            :label="item.name"
          ></el-option>
        </el-select>
      </el-form-item>
      <!-- && propMode != 'object' -->
      <template v-if="attr.propertyName">
        <!-- propertyName 为属性 -->
        <el-form-item
          v-if="propetyMode == 'prop'"
          :prop="`${prop}.propertyValue`"
          :ref="`${prop}.propertyValue`"
          :key="`${prop}.propertyValue`"
          :rules="[{ validator: compareValue, trigger: 'blur' }]"
        >
          <el-input
            v-if="propMode != 'bool' && propMode != 'enum'"
            v-model="attr.propertyValue"
            placeholder="请输入值"
          ></el-input>
          <!--  && propMode != 'time' -->
          <!-- <el-date-picker
            v-else-if="propMode == 'time'"
            v-model="attr.propertyValue"
            value-format="timestamp"
            type="datetime"
            placeholder="请选择时间"
          >
          </el-date-picker> -->
          <el-select v-else v-model="attr.propertyValue" placeholder="请选择">
            <el-option
              v-for="item in compareValueList"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            ></el-option>
          </el-select>
        </el-form-item>
      </template>
      <!-- <template v-else-if="attr.propertyName && propMode == 'object'">
        <div>
          <custom-item
            v-for="(item, index) in attr.objectList"
            :key="item.id"
            :attr="item"
            :index="index"
            :prop="`${prop}.objectList.${index}`"
          ></custom-item>
        </div>
      </template> -->
    </div>
    <!-- propertyName 为服务 -->
    <div
      class="service"
      v-if="attr.actionMode == 'device' && propetyMode == 'service'"
    >
      <el-form-item
        v-for="(item, idx) in attr.serviceInList"
        :key="item.id"
        :prop="`${prop}.serviceInList.${idx}.propertyValue`"
        :ref="`${prop}.serviceInList.${idx}.propertyValue`"
        :rules="[
          {
            validator: compareValue,
            trigger: ['blur', 'change'],
          },
        ]"
      >
        <!-- validator: item.data.type != 'enum' && item.data.type != 'bool' ? compareValue : selectValidate, -->
        <!-- trigger: item.data.type != 'enum' && item.data.type != 'bool' ? ['blur', 'change'] : 'change', -->
        <div class="flex">
          <p class="item-title">{{ item.name }}</p>
          <el-input
            v-if="item.data.type != 'enum' && item.data.type != 'bool'"
            v-model="item.propertyValue"
            placeholder="请输入值"
            :key="item.id"
            @input="onInput()"
          ></el-input>
          <el-select
            v-else
            v-model="item.propertyValue"
            placeholder="请选择"
            @input="onInput()"
          >
            <el-option
              v-for="item in item.compareValueList"
              :key="item.id"
              :value="item.id"
              :label="item.label"
            ></el-option>
          </el-select>
        </div>
      </el-form-item>
    </div>
  </div>
</template>

<script>
import { productAbilityDetail } from "@/api/product";
import { getDeviceList } from "@/api/device";
import { getRuleList } from "@/api/sceneLink";
import { checkDefault, checkValue, arrayValidate } from "../ruleEdit/rules.js";
// import customItem from "../customItem";
export default {
  data() {
    return {
      deviceList: [], //设备列表
      propertyList: [], //属性下拉列表
      modelData: {}, //物模型数据
      propetyMode: "", //当前选择的 ？ 属性-prop / 服务-service
      propertyObject: {}, //当前选择的 属性/服务  规则
      serviceInList: [], //服务入参数组
      ruleList: [],
      propMode: "",
      compareValueList: [],
      rulePage: 0, //下拉加载 规则 分页
      rulePageAll: 1,
      devicePage: 0, //设备下拉分页 页码
      devicePageAll: 1, //设备下拉分页 页树
    };
  },
  // components: { customItem },
  props: {
    attr: {
      type: Object,
    },
    index: {
      type: [String, Number],
    },
    tenant_id: {
      type: String,
    },
    prop: {
      type: String,
    },
    productList: {
      type: Array,
    },
    isShowAlarm: {
      type: Boolean,
      default: true,
    },
    ruleID: {
      type: [String, Number],
    },
  },
  watch: {
    productList() {
      this.rulePage = 0;
      this.devicePage = 0;
      this.defaultSelectData();
    },
  },
  methods: {
    defaultSelectData() {
      // 默认告警   的配置   在数据中已进行处理
      // 此处 需对scene  与 device 配置默认下拉数据
      if (this.attr.actionMode == "scene") {
        this.ruleList = [];
        // this.attr.ruleId  如果不传  可能无法回显  因为数据可能再后续分页中
        this.getRuleList(this.attr.ruleId);
      } else if (this.attr.actionMode == "device") {
        // 判断设备列表
        if (this.attr.productKey) {
          //正常情况下 productKey  一定存在  ， 不存在时  通过下拉变化更新后续数据
          // 判断产品是否删除
          let product = this.productList.find(
            (item) => item.productKey == this.attr.productKey
          );
          if (!product) {
            // 如果产品不存在  清空后续
            this.reserItem([
              "productKey",
              "deviceName",
              "propertyName",
              "propertyValue",
              "ruleId",
              "switchStatus",
            ]);
            this.$newNotify.warning({
              message: "执行动作中所选产品不存在",
            });
            return;
          }
          let e = this.attr.productKey;
          this.productId = this.productList.filter(
            (item) => item.productKey == e
          )[0].id;
          this.checkDevice(this.attr.deviceName);
          this.getDeviceData(); //产品id
          this.getModelData(e); // 产品key
        }
      }
    },
    onInput() {
      this.$forceUpdate();
    },
    // 下拉选择框验证   输入框特殊处理
    selectValidate(rule, value, callback) {
      // 执行条件选了  就进入验证
      if (this.attr.actionMode) {
        let key = rule.field.split(".")[2] || "";
        if (value == undefined || value == "") {
          callback(this.callbackTips(key, value));
        } else callback();
      } else {
        callback("请选择执行动作");
      }
    },
    compareValue(rule, value, callback) {
      let list = rule.field.split(".");
      let index = list[list.length - 2];
      let key = list[list.length - 1];
      if (this.propetyMode == "service") {
        let newValue = this.attr.serviceInList[index][key];
        let { type, rules } = this.attr.serviceInList[index].data;
        if (newValue != "" && newValue != undefined) {
          //
          if (type != "array" && type != "object") {
            // 验证基本类型
            let result = checkDefault(type, newValue, rules);
            if (result.flag) {
              callback(result.text);
            } else {
              callback();
            }
          } else if (type == "object") {
            let flag = checkValue(type, newValue, rules);
            flag ? callback("对象格式输入有误") : callback();
          } else if (type == "array") {
            // 数组 或 对象
            arrayValidate(
              newValue,
              rules,
              callback,
              `输入格式有误，请输入array(${rules.item.type})格式`
            );
          }
          callback();
        } else {
          // 不必填
          // callback("请输入值")
          callback();
        }
      } else {
        // 属性
        let newValue = this.attr.propertyValue;
        if (newValue != "" && newValue != undefined) {
          let { type, rules } = this.propertyObject.data;
          let result = { flag: true };
          if (type != "object" && type != "array") {
            result = checkDefault(type, newValue, rules, true);
          } else if (type == "array") {
            arrayValidate(
              newValue,
              rules,
              callback,
              `输入格式有误，请输入array(${rules.item.type})格式`
            );
          } else {
            result.flag = checkValue(type, newValue, rules);
            result.text = "对象格式输入有误";
          }
          if (result.flag) {
            callback(result.text);
          } else {
            callback();
          }
        } else {
          callback("请输入值");
        }
      }
    },
    callbackTips(key) {
      let result = "";
      switch (key) {
        case "productKey":
          result = "请选择产品";
          break;
        case "deviceName":
          result = "请选择设备";
          break;
        case "propertyName":
          result = "请选择属性/服务";
          break;
        case "ruleId":
          result = "请选择规则";
          break;
        case "switchStatus":
          result = "请选择执行操作";
          break;
      }
      return result;
    },
    // 清空子项   deviceName  triggerShape propertyName eventCode compareType compareValue
    reserItem(list) {
      if (!Array.isArray(list) && list.length == 0) return;
      for (let i = 0; i < list.length; i++) {
        if (list[i] == "deviceName") {
          this.deviceList = [];
          this.devicePage = 0;
          this.devicePageAll = 1;
        }
        if (list[i] == "ruleId") {
          this.ruleList = [];
          this.rulePage = 0;
        }
        if (list[i] == "propMode") {
          this.$emit("formSet", {
            key: `${this.prop}.propMode`,
            value: "",
          });
          continue;
        }
        if (list[i] == "propertyName") {
          //  属性名 / 服务名
          this.propetyMode = ""; // 标识清空
          this.propertyObject = {}; // 属性规则对象
          // this.propertyList = [];
          this.$emit("formSet", {
            key: `${this.prop}.serviceInList`,
            value: [],
          });
        }
        // this.attr[list[i]] &&   存在 0   隐式转换无法通过判断  此处只判断form 的数据
        if (this.attr[list[i]] && this.$refs[`${this.prop}.${list[i]}`]) {
          // 清空子项
          //  回显时 无法 使用form 的重置方法    因为回显时会吧form 的默认值修改
          // this.$refs[`${this.prop}.${list[i]}`].resetField();
          this.attr[list[i]] = "";
          this.$nextTick(() => {
            if (
              this.$refs[`${this.prop}.${list[i]}`] &&
              this.$refs[`${this.prop}.${list[i]}`].clearValidate
            ) {
              this.$refs[`${this.prop}.${list[i]}`].clearValidate(
                `${this.prop}.${list[i]}`
              );
            }
          });
        }
      }
    },
    actionChange(e) {
      this.reserItem([
        "productKey",
        "deviceName",
        "propertyName",
        "ruleId",
        "switchStatus",
        "propMode",
      ]);
      if (!this.isShowAlarm) {
        // 为true   当前项选择了 告警  由此项决定是否释放告警选项
        let flag = false;
        if (this.attr.actionMode == "event") {
          // 已选择 告警输出  继续占用
          flag = true;
        } else {
          // 释放占用
          flag = false;
        }
        // 传递是否被占用   占用项的下标
        this.$emit("actionModeChange", { flag, index: this.index });
      }
      if (e == "scene") {
        this.ruleList = [];
        this.getRuleList();
      }
    },
    remoteMethod(val) {
      this.rulePage = 0;
      this.rulePageAll = 1;
      this.ruleList = [];
      if (typeof val != "string") {
        this.getRuleList();
      } else {
        this.getRuleList("", val);
      }
    },
    getRuleList(id = "", name = "") {
      this.rulePage++;
      if (this.rulePage > this.rulePageAll) return;
      let params = {
        current: this.rulePage,
        size: 10,
      };
      if (id) params["id"] = id;
      if (name) params["name"] = name;
      getRuleList(params).then((res) => {
        if (res.code == 200) {
          this.ruleList = this.ruleList
            .concat(res.data.records)
            .filter((item) => item.id != this.ruleID);
          this.rulePageAll = res.data.pages; //设置最大页数
          if (this.attr.ruleId) {
            let ruleInfo = this.ruleList.find(
              (item) => item.id == this.attr.ruleId
            );
            // 检测规则是否被删除
            if (!ruleInfo) {
              this.reserItem(["ruleId", "switchStatus"]);
            }
          }
        }
      });
    },
    productChange(e) {
      this.reserItem(["deviceName", "propertyName"]);
      this.productId = this.productList.filter(
        (item) => item.productKey == e
      )[0].id;
      this.getDeviceData(); //产品id
      this.getModelData(e); // 产品key      产品变换 ->   物模型更新 ->  属性数组更新
    },
    propertyChange(e) {
      this.reserItem(["propertyValue", "propMode"]);
      let result = this.propertyList.find((item) => item.id == e);
      this.propertyObject = result;
      if (result.action) {
        // 服务
        this.propetyMode = "service";
        let list = result.in.map((item) => {
          if (this.attr.serviceInList) {
            // 服务数据值回显
            let historyData = this.attr.serviceInList.find(
              (item2) => item2.id == item.id
            );
            if (historyData) {
              item.propertyValue = historyData.propertyValue;
            }
          }
          if (item.data.type == "bool" || item.data.type == "enum") {
            item.compareValueList = Object.keys(item.data.rules).map(
              (item2) => {
                return {
                  id: item2,
                  label: item.data.rules[item2],
                };
              }
            );
          }
          return item;
        });
        // this.attr.serviceInList = list;
        this.$emit("formSet", {
          key: `${this.prop}.serviceInList`,
          value: list,
        });
      } else {
        console.log("****---**");
        // 属性
        this.propetyMode = "prop";
        this.propMode = result.data.type;
        if (this.propMode == "bool" || this.propMode == "enum") {
          this.compareValueList = Object.keys(result.data.rules).map((item) => {
            return {
              id: item,
              label: result.data.rules[item],
            };
          });
        }
        this.$emit("formSet", {
          key: `${this.prop}.serviceInList`,
          value: [],
        });
        this.$emit("formSet", {
          key: `${this.prop}.propMode`,
          value: this.propMode,
        });
      }
    },
    remoteDevice(val) {
      this.devicePage = 0;
      this.devicePageAll = 1;
      this.deviceList = [];
      if (typeof val != "string" || val == "") {
        this.getDeviceData();
      } else {
        this.getDeviceData(val);
      }
    },
    checkDevice(deviceName = "") {
      if (deviceName == "_all") return;
      getDeviceList({
        productId: this.productId,
        deviceName: deviceName != "_all" ? deviceName : "",
        current: this.devicePage,
        size: 10,
      }).then((res) => {
        if (res.code == 200) {
          let deviceInfo = res.data.records.find(
            (item) => item.deviceName == this.attr.deviceName
          );
          if (!deviceInfo) {
            // 未找到   设备已被删除
            // 清空设备项    后续与设备项无关 无需清除
            this.reserItem(["deviceName"]);
            this.$newNotify.warning({
              message: "执行动作中所选设备不存在",
            });
            return;
          }
        }
      });
    },
    // 获取设备列表
    getDeviceData(deviceName = "") {
      this.devicePage++;
      if (this.devicePage > this.devicePageAll) return;
      getDeviceList({
        productId: this.productId,
        current: this.devicePage,
        size: 10,
        deviceName,
      }).then((res) => {
        this.deviceList = this.deviceList.concat(res.data.records);
        this.devicePageAll = res.data.pages;
        this.$forceUpdate();
      });
    },
    // 获取产品物模型
    getModelData(productKey) {
      productAbilityDetail({
        // tenantId: this.tenant_id,
        productKey,
      }).then((res) => {
        if (res.code == 200 && typeof res.data == "string") {
          let model = JSON.parse(res.data);
          this.modelData = model;
          let propertyList = model.properties;
          let eventList = model.services.filter(
            (item) => item.id !== "get" && item.id !== "set"
          );
          this.propertyList = propertyList.concat(eventList);
        } else {
          this.modelData = {
            properties: [],
            events: [],
            services: [],
          };
        }
        // 回显-------------------------------
        // 属性下拉数据回显   通过物模型设置输入项的规则
        if (this.attr.propertyName) {
          //// 处理后续之前 判断 属性是否存在于当前 属性列表
          let property = this.propertyList.find(
            (item) => item.id == this.attr.propertyName
          );
          if (!property) {
            this.reserItem(["propertyName", "propertyValue"]);
            this.$newNotify.warning({
              message: "执行动作中所选属性/服务不存在",
            });
            return;
          }
          this.propertyChange(this.attr.propertyName);
        }
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  color: #515151;
  font-size: 14px;
  padding-bottom: 8px;
}
.item-title {
  width: 224px;
  height: 34px;
  line-height: 34px;
  margin-right: 14px;
  flex-shrink: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
/deep/ {
  .el-form-item {
    margin-right: 14px;
  }
  .el-input__inner {
    border-radius: 0;
    width: 224px;
  }
  .el-form-item__content {
    line-height: 34px;
  }
  .el-form-item__error {
    white-space: nowrap;
  }
}
.service {
  /deep/ .el-form-item__error {
    left: 238px;
  }
}
</style>
