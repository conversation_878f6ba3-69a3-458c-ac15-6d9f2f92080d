<template>
  <div>
    <div
      class="file-button"
      :style="{
        background: uploading ? '#EBEBEB' : '#EBF6FF',
        width: btnWidth + 'px',
      }"
    >
      <span :style="{ color: uploading ? '#BFBFBF' : '#0088FE' }">{{
        uploadLabel
      }}</span>

      <!--  -->
      <input
        ref="uploadInput"
        type="file"
        :style="{ zIndex: uploading ? -1 : 1 }"
        :multiple="false"
        @change="fileChange"
      />
    </div>
    <p class="tips">
      {{ uploadTips }}
    </p>
    <ul class="file-list">
      <transition name="el-fade-in-linear">
        <li
          class="flie-item flex"
          :class="isError ? 'flie-item-error' : ''"
          v-for="(item, index) in fileList"
          :key="item.key"
        >
          <div class="file-item-name flex">
            <span>{{ item.name }}</span>
            <i>{{ item.format }}</i>
            <b>{{ errorMsg }}</b>
          </div>
          <div class="progress" v-if="partUploading">
            <p>
              <span :style="{ width: progressWidth + '%' }"></span>
            </p>
            <div class="percentage">{{ progressWidth }}%</div>
          </div>
          <p class="">
            <i class="success" v-if="isSuccess"></i>
            <span class="loader" v-if="uploading">
              <svg class="circular" viewBox="25 25 50 50">
                <circle
                  class="path"
                  cx="50"
                  cy="50"
                  r="20"
                  fill="none"
                  stroke-width="2"
                  stroke-miterlimit="10"
                ></circle>
              </svg>
            </span>
            <i class="close" v-if="!uploading" @click="handleDelete(index)"></i>
          </p>
        </li>
      </transition>
    </ul>
  </div>
</template>

<script>
/**
 *
 *   仅满足当前平台 固件上传 ------------------------------------!!!!!!!!!!!!!!!----------------------------------------------------------------------
 *    选择文件  -> 文件生成MD5  -> 是否切片  ->  分片  收集切片请求  发送合并请求
 *                                         ->  直接上传
 */
import axios from "axios";
import { md5File } from "@/util/sliceUpdate";
export default {
  data() {
    return {
      uploadLabel: "上传固件",
      fileList: [],
      isSuccess: false, //是否上传成功
      isError: false,
      uploading: false, //上传中
      partUploading: false, //片上传中
      promiseAll: [],
      queueTask: [],
      partMaxNum: 1,
      complateNum: 0,
      progressWidth: 0,
      maxRequest: 0,
      stopUpload: false, //  判断是否中断上传  ：中断的是文件处理过程中未进行的操作 并不是直接中断请求  已进行的操作暂未拦截
      cancelToken: null, // 请求取消token
    };
  },
  props: {
    label: {
      type: String,
      default: "上传固件",
    },
    btnWidth: {
      type: String,
      default: "160",
    },
    action: {
      type: String,
      default: "",
    },
    header: {
      type: Object,
      default: () => {},
    },
    formData: {
      type: Object,
      default: () => {},
    },
    // multiple: {
    //   type: Boolean,
    //   default: false,
    // },   暂不支持多选     多选  需考虑  并发切片请求数量限制   chrome 最大6个请求数    上传总状态  单文件进度条、上传状态 loading 等情况
    autoUpload: {
      type: Boolean,
      default: true,
    },
    customHttp: {
      type: Function,
    },
    uploadTips: {
      type: String,
      default: "仅支持 bin, tar, gz, zip 类型的文件,文件大小不能超过2048MB",
    },
    isSlice: {
      type: Boolean,
      default: false,
    },
    beforeUpload: {
      type: Function,
      default: () => {},
    },
    error: {
      type: Function,
      default: () => {},
    },
    delete: {
      type: Function,
      default: () => {},
    },
    success: {
      type: Function,
      default: () => {},
    },
    mergeUrl: {
      type: String,
      default: "",
    },
    partUrl: {
      type: String,
      default: "",
    },
    partSize: {
      type: Number,
      default: 1024 * 1024 * 10,
    },
    errorMsg: {
      type: String,
      default: "",
    },
  },
  watch: {
    label() {
      this.uploadLabel = this.label;
    },
    "fileList.length"() {
      this.$emit("fileChange", this.fileList.length);
    },
  },
  mounted() {
    this.uploadLabel = this.label;
  },
  methods: {
    fileChange(event) {
      setTimeout(() => {
        // 拿到file文件
        let files = event.target.files;
        if (files.length == 0) return;
        if (this.fileList.length > 0) {
          this.uploadLabel = this.label;
          this.fileList = [];
        }
        this.isSuccess = false;
        this.uploading = false;
        this.isError = false;
        for (let i in files) {
          if (typeof files[i] === "object") {
            this.fileList.push({
              file: files[i],
              name: files[i].name,
              size: files[i].size,
              error: false, // 是否出现错误
              key: Math.random() * 200,
            });
            this.$emit("select");
            // 选择完文件后按= 存入filelist  置空input的value  供再次选择相同文件
            this.$refs.uploadInput.value = null; // 重置input 选择的内容
          }
        }
        if (this.autoUpload) {
          this.planUpload();
        } else {
          this.uploadLabel = `重新${this.label}`;
        }
      }, 100);
    },
    planUpload() {
      // 上传前   验证文件是否满足上传条件
      if (this.isError) return;
      if (this.fileList.length <= 0) {
        this.$newNotify.error({
          message: "请选择文件",
        });
        return;
      }
      const before = this.beforeUpload && this.beforeUpload(this.fileList);
      if (!before) {
        this.isError = true;
        return false;
      }
      this.uploading = true;
      // 是否分片
      if (this.isSlice) {
        this.fileSlice();
      } else {
        for (let i = 0; i < this.fileList.length; i++) {
          this.request(this.fileList[i]);
        }
      }
    },
    // 文件上传（不分片）
    request(object) {
      let formData = new FormData();
      formData.append("file", object.file);
      if (Object.keys(this.formData).length > 0) {
        for (let i in this.formData) {
          formData.append(i, this.formData[i]);
        }
      }
      this.cancelToken = axios.CancelToken.source();
      axios({
        url: `${this.action}`,
        method: "post",
        headers: this.header || {
          Authorization: localStorage.getItem("token"),
        },
        cancelToken: this.cancelToken.token,
        data: formData,
      })
        .then((res) => {
          if (res.code == 200) {
            this.isSuccess = true;
            this.progressWidth = 0;
            this.success(res);
          } else {
            this.isError = true;
            this.error && this.error(res);
          }
        })
        .catch((err) => {
          this.handleError();
          this.error && this.error(err);
        })
        .finally(() => {
          this.uploading = false;
          this.partUploading = false;
        });
    },
    // 分割文件
    async fileSlice() {
      for (let i = 0; i < this.fileList.length; i++) {
        //   分割文件  并为切片生成MD5
        console.log("开始为文件生成MD5");
        console.time("切片前文件生成md5时间");
        // this.$emit("encryption");
        this.uploadLabel = "加密中";
        this.fileList[i].md5 = await md5File(this.fileList[i].file);
        console.timeEnd("切片前文件生成md5时间");
        if (this.stopUpload) break;
        console.log("开始切片并准备上传");
        console.time("切片上传时间：");
        this.partUploading = true;
        // this.$emit("partUpload");
        this.uploadLabel = "上传中";
        try {
          this.fileList[i].fileChunkList = await this.createFileChunk(
            this.fileList[i].file,
            this.partSize,
            this.fileList[i].md5
          );
        } catch (err) {
          break;
        }
        console.timeEnd("切片上传时间：");
        console.log("切片请求发送完成");
        // 防止弹窗关闭 切片请求出现错误 后仍然发送合并请求
        if (this.stopUpload) break;
        console.log("开始发送合并请求");
        this.mergeRequest(this.fileList[i], i);
      }

      //
    },
    // 文件生成MD5
    async fileMD5(list) {
      let result = [];
      for (let i = 0; i < list.length; i++) {
        list[i].md5 = await md5File(list[i].file);
        result.push(list[i]);
      }
      return result;
    },
    // 文件分割的方法   切割后  切片格式为blob
    async createFileChunk(file, size, allMD5) {
      let fileChunkList = [];
      let count = 0;
      let num = 1;
      let stopWhile = false;
      this.partMaxNum = Math.ceil(file.size / size); //计算切片数量
      this.maxRequest = this.partMaxNum + 1;
      while (count < file.size) {
        let newFile = this.BlobToFile(
          file.slice(count, count + size),
          `${num}`
        );
        // let md5 = await md5File(newFile); //切片md5
        if (this.stopUpload) break;
        let partResult = this.partRequest(newFile, allMD5);
        // 收集4个请求存入 请求队列
        if (this.queueTask.length >= 3) {
          // 队列已满  等待队列结果
          await Promise.all(this.queueTask)
            .then((res) => {
              // 4个片 的请求结果
              // 全部成功清空队列
              this.queueTask = [];
            })
            .catch((err) => {
              console.log("对列中的请求出现错误:", err);
              stopWhile = true;
              this.handleError();
              this.error && this.error(err);
            });
          if (stopWhile) break;
        } else {
          this.queueTask.push(partResult);
        }
        // 收集所有片请求
        this.promiseAll.push(partResult);
        fileChunkList.push({
          //   default parameter
          file: newFile, // blob 转file
          partNumber: num,
          // md5, //暂时不生成 切片md5
          //   custom parameter
          sliceName: `${num}`,
          allFileName: `${file.name}`,
        });
        count += size;
        num++;
      }
      // 如果循环完之后 队列可能存在未完成请求
      if (this.queueTask.length > 0) {
        await Promise.all(this.queueTask)
          .then((res) => {
            this.queueTask = [];
          })
          .catch((err) => {
            console.log("对列中的请求出现错误:", err);
            this.handleError();
            this.error && this.error(err);
          });
      }
      return fileChunkList;
    },
    // 文件格式转换
    BlobToFile(blob, name) {
      return new File([blob], name);
    },
    // 单文件上传
    // 切片上传  切片数组  切片前总文件MD5
    partRequest(file, md5) {
      // 切片请求
      return new Promise((resolve, reject) => {
        let formData = new FormData();
        formData.append("file", file);
        formData.append("md5", md5);
        axios({
          method: "post",
          headers: this.header || {
            Authorization: localStorage.getItem("token"),
          },
          url: `${this.partUrl}`,
          data: formData,
        })
          .then((res) => {
            console.log("切片上传成功");
            if (res.code == 200) {
              this.progressComputed();
              resolve(res);
            } else {
              reject(res);
            }
          })
          .catch((err) => {
            console.log("切片上传失败");
            console.log(`错误信息如下：${err}`);
            reject(err);
          });
      });
    },
    mergeRequest(object) {
      Promise.all(this.promiseAll)
        .then((res) => {
          axios({
            method: "post",
            headers: this.header || {
              Authorization: localStorage.getItem("token"),
            },
            url: `${this.mergeUrl}`,
            data: {
              fileName: object.name,
              md5: object.md5,
            },
          })
            .then((res) => {
              if (this.stopUpload) return;
              this.progressComputed();
              //   接口请求成功回调
              // 根据业务code 判断状态
              if (res.code == 200) {
                console.log("合并成功");
                this.success(res);
                this.isSuccess = true;
                this.progressWidth = 0;
                this.queueTask = [];
                this.promiseAll = [];
                this.uploadLabel = "重新上传固件";
                //   满足当当前业务
                this.fileList = this.fileList.map((item) => {
                  item.name = `${res.data.fileName}`;
                  item.format = `(${
                    Math.floor((res.data.size / 1024) * 100) / 100
                  }KB)`;
                  return item;
                });
              } else {
                //   暂时清空上传列表     多选清空  提供错误状态  手动删除  或者根据文件 MD5 值进行删除
                this.error && this.error(res);
                this.isError = true;
              }
            })
            .catch((err) => {
              this.handleError();
            })
            .finally(() => {
              this.uploading = false;
              this.partUploading = false;
            });
        })
        .catch((err) => {
          this.error && this.error(err);
          this.handleError();
        });
    },
    progressComputed() {
      // this.partMaxNum  未 所有切片数 加  最后一个合并请求
      this.complateNum++;

      if (this.complateNum <= this.maxRequest) {
        this.progressWidth = Math.floor(
          (this.complateNum / this.maxRequest) * 100
        );
      }
    },
    handleError() {
      this.stopUpload = true;
      this.isError = true;
      this.uploadLabel = this.label;
      this.uploading = false; //
      this.partUploading = false; // 分片请求过程
      this.queueTask = []; //分片数队列容器
      this.promiseAll = []; //  所有分片请求容器
    },
    handleReset() {
      this.uploadLabel = this.label;
      this.isSuccess = false;
      this.isError = false;
      this.uploading = false; //
      this.partUploading = false; // 分片请求过程
      this.fileList = []; // 选择的文件列表
      this.queueTask = []; //分片数队列容器
      this.promiseAll = []; //  所有分片请求容器
      this.progressWidth = 0; //进度条
      this.$refs.uploadInput.value = null; // 重置input 选择的内容
      this.stopUpload = false; // 是否停止上传流程
      if (this.cancelToken) {
        this.cancelToken.cancel(); //如果单次上传存在请求  发送取消
        this.cancelToken = null;
      }
    },
    handleDelete(index) {
      // this.uploadLabel = this.label;
      // this.fileList.splice(index, 1);
      this.handleReset(); /// 单文件  选择直接重置
      this.delete();
    },
  },
};
</script>

<style lang="scss" scoped>
.file-button {
  width: 160px;
  height: 36px;
  background: #ebf6ff;
  text-align: center;
  line-height: 36px;
  position: relative;
  span {
    color: #0088fe;
    font-size: 14px;
  }
  input {
    width: 100%;
    height: 100%;
    position: absolute;
    left: 0;
    top: 0;
    line-height: 36px;
    border: none;
    outline: none;
    background: none;
    opacity: 0;
    cursor: pointer;
  }
}
.tips {
  padding: 10px 0;
  color: #999999;
  font-size: 12px;
  line-height: 14px;
}
.file-list {
  transition: all 0.3s;
  .flie-item {
    height: 40px;
    border: 1px solid #ecedee;
    background: #fbfbfc;
    align-items: center;
    justify-content: space-between;
    padding: 0 16px;
    margin-bottom: 12px;
    .file-item-name {
      color: #515151;
      font-size: 14px;
      line-height: 16px;
      span {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        padding-left: 10px;
      }
      i {
        font-style: normal;
      }
      b {
        display: none;
        font-weight: normal;
        color: #ff4d4f;
        font-size: 14px;
        line-height: 16px;
        padding-left: 10px;
      }
    }
    .file-item-name::before {
      content: "\e652";
      font-family: "tenant";
      font-size: 14px;
      color: #379cff;
      line-height: 16px;
    }
    .progress {
      flex: 1;
      flex-shrink: 0;
      display: flex;
      align-items: center;
      padding: 0 16px;
      p {
        flex: 1;
        flex-shrink: 0;
        height: 4px;
        background: #f5f5f5;
        position: relative;
        span {
          position: absolute;
          top: 0;
          left: 0;
          display: inline-block;
          height: 4px;
          background: #018aff;
          transition: all 0.3s;
        }
      }
      .percentage {
        flex-shrink: 0;
        color: #515151;
        font-size: 12px;
        line-height: 14px;
        padding-left: 10px;
      }
    }
    p {
      flex-shrink: 0;
      width: 16px;
      height: 16px;
      font-family: "tenant";
      font-size: 16px;
      position: relative;
      i {
        font-style: inherit;
        position: absolute;
        top: 0;
        left: 0;
        cursor: pointer;
      }
    }
    .success::before {
      content: "\e650";
      color: #0081ff;
    }

    .close::before {
      content: "\e64f";
    }
    .close {
      display: none;
    }
  }
  .flie-item:last-child {
    margin: 0;
  }
  .flie-item:hover {
    .success {
      display: none;
    }
    .close {
      display: block !important;
    }
  }
  .flie-item-error {
    background: #fff1f1;
    border: 1px solid #f2d0cd;
    b {
      display: inline !important;
    }
  }
}
.loader {
  position: relative;
  margin: 0 auto;
  width: 16px;
  display: inline-block;
}
.loader:before {
  content: "";
  display: block;
  padding-top: 100%;
}

.circular {
  -webkit-animation: rotate 2s linear infinite;
  animation: rotate 2s linear infinite;
  height: 100%;
  transform-origin: center center;
  width: 100%;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  margin: auto;
}

.path {
  stroke-dasharray: 1, 200;
  stroke-dashoffset: 0;
  -webkit-animation: dash 1.5s ease-in-out infinite,
    color 6s ease-in-out infinite;
  animation: dash 1.5s ease-in-out infinite, color 6s ease-in-out infinite;
  stroke-linecap: round;
}

@-webkit-keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}

@keyframes rotate {
  100% {
    transform: rotate(360deg);
  }
}
@-webkit-keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}
@keyframes dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -35px;
  }
  100% {
    stroke-dasharray: 89, 200;
    stroke-dashoffset: -124px;
  }
}
@-webkit-keyframes color {
  100%,
  0% {
    stroke: #d62d20;
  }
  40% {
    stroke: #0057e7;
  }
  66% {
    stroke: #008744;
  }
  80%,
  90% {
    stroke: #ffa700;
  }
}
@keyframes color {
  100%,
  0% {
    stroke: #d62d20;
  }
  40% {
    stroke: #0057e7;
  }
  66% {
    stroke: #008744;
  }
  80%,
  90% {
    stroke: #ffa700;
  }
}
</style>
