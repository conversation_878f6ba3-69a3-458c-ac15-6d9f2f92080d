import Vue from "vue";
import { notifySuccess } from "@/components/notify";
// 自定义指令
Vue.directive("copy", {
  inserted: function (el, binding) {
    el.addEventListener("click", () => {
      if (binding.value != undefined) {
        let input = document.createElement("input");
        document.body.appendChild(input);
        input.style.opacity = 0;
        input.value = binding.value;
        input.focus();
        input.setSelectionRange(0, input.value.length);
        document.execCommand("copy", true);
        document.body.removeChild(input);
        notifySuccess({
          message: "复制成功",
        });
      }
    });
  },
});
