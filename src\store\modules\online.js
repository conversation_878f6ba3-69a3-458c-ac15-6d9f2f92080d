/*
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-12-22 19:16:19
 * @LastEditors: Please set LastEditors
 * @LastEditTime: 2022-04-20 11:34:19
 */
import {
  MUTATIONS_ONLINE__WEBSOCKET,
  MUTATIONS_ONLINE__DEBUGLOGDATA,
  MUTATIONS_ONLINE__ONLINEFLAGE,
  MUTATIONS_ONLINE__DEBUGLOGCLEAR,
} from "../mutations_type";

import Socket from "@/socket/";
import { BASE_SERVER } from "../../conf/env";
const baseUrl = "/api";
// const url__ws = `${baseUrl}/central-iot-stomp/ws`;
const url__ws = `${baseUrl}${BASE_SERVER}/ws`;
export default {
  state: () => {
    return {
      xtLogsSocket: null,
      logData: [],
      logDataCopy: [],
      onlineFlag: false,
    };
  },
  mutations: {
    [MUTATIONS_ONLINE__WEBSOCKET](state, val = null) {
      state.xtLogsSocket = val;
    },
    [MUTATIONS_ONLINE__DEBUGLOGDATA](state, val = null) {
      state.logData = [...val, ...state.logData];
      state.logDataCopy = [...val, ...state.logData];
    },
    [MUTATIONS_ONLINE__DEBUGLOGCLEAR](state) {
      state.logData = [];
    },
    [MUTATIONS_ONLINE__ONLINEFLAGE](state, val = null) {
      state.onlineFlag = val;
    },
  },
  actions: {
    // 建立连接
    async fn_init_onlinesocket({ commit, state, rootState }) {
      if (state.xtLogsSocket) {
        return;
      }
      let userInfo = rootState.user.userInfo;
      let username = userInfo.user_name;
      let nestAuth = userInfo.access_token;
      let ws = `${url__ws}?username=${username}&Nest-Auth=${nestAuth}`;
      // 建立WS实例同时初始化IOT设备数据
      let xtSocket = new Socket({
        ws,
        headers: {
          username,
          "Nest-Auth": nestAuth,
        },
      });
      commit(MUTATIONS_ONLINE__WEBSOCKET, xtSocket);
      // 连接WS
      await xtSocket.asyncConnect();
    },
    // 事件日志订阅
    fn_logstatus__subscribe({ commit, state }, deviceId = "") {
      let xtSocket = state.xtLogsSocket;
      let ws_url__sub = `/iot/${deviceId}/log`;
      xtSocket.subscribe({
        destination: ws_url__sub,
        callback: function (data) {
          let result = {};
          result.title = data.typeName;
          result.time = data.time;
          result.character = data.content;
          try {
            commit(MUTATIONS_ONLINE__DEBUGLOGDATA, [result]);
          } catch (error) {
            console.log(error);
          }
        },
      });
    },
    // 事件设备状态订阅
    fn_deviceStatus__subscribe({ commit, state }, deviceId = "") {
      let xtSocket = state.xtLogsSocket;
      let ws_url__sub = `/iot/${deviceId}/status`;
      xtSocket.subscribe({
        destination: ws_url__sub,
        callback: function (data) {
          try {
            commit(MUTATIONS_ONLINE__ONLINEFLAGE, data.online);
          } catch (error) {
            console.log(error);
          }
        },
      });
    },
    // 取消事件订阅
    fn_logstatus__unsubscribe({ state }, destination = "") {
      state.xtLogsSocket.unsubscribe(destination);
    },
  },
};
