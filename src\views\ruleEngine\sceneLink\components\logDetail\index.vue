<template>
	<div class="log-detail">
		<div class="log-top flex">
			<el-select v-model="actionState" @change="selectStatus">
				<el-option
					v-for="item in statusOption"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				>
				</el-option>
			</el-select>
			<el-select v-model="timeState" @change="selectTimeStatus">
				<el-option
					v-for="item in timeOption"
					:key="item.value"
					:label="item.label"
					:value="item.value"
				>
				</el-option>
			</el-select>
			<el-date-picker
				v-if="timeState == '3'"
				v-model="dateRange"
				prefix-icon="el-icon-date"
				type="datetimerange"
				range-separator="至"
				start-placeholder="开始日期"
				end-placeholder="结束日期"
				:picker-options="pickerOptions"
				@change="pickerChange"
				value-format="yyyy-MM-dd HH:mm:ss"
			></el-date-picker>
		</div>
		<div class="log-content">
			<iot-table :columns="columns" :data="tableData" :loading="loading">
				<template slot="actionState" slot-scope="{ row }">
					<div class="state-edit">
						<span class="success" v-if="row.actionState == 1"
							>成功</span
						>
						<span class="fail" v-if="row.actionState == 0"
							>失败</span
						>
					</div>
				</template>
			</iot-table>
		</div>
		<div class="log-bottom" v-if="tableData.length != 0">
			<iot-pagination
				:pagination="pagination"
				@size-change="handleSizeChange"
				@current-change="handleCurrentChange"
			/>
		</div>
	</div>
</template>
<script>
import IotTable from '@/components/iot-table'
import IotPagination from '@/components/iot-pagination'
import { getLogList } from '@/api/sceneLink'
import { fn_util__date_format, fn_util__filter_null } from '@/util/util'

export default {
	name: 'logDetail',
	components: {
		IotTable,
		IotPagination,
	},
	data() {
		return {
			actionState: 999,
			// [0] 开始时间, [1] 结束时间
			dateRange: [],
			timeState: 0,
			statusOption: [
				{ value: 999, label: '全部状态' },
				{ value: 1, label: '成功' },
				{ value: 0, label: '失败' },
			],
			timeOption: [
				{ value: 0, label: '最近15分钟' },
				{ value: 1, label: '最近30分钟' },
				{ value: 2, label: '最近4小时' },
				{ value: 3, label: '自定义' },
			],
			columns: [
				{ label: '执行时间', prop: 'actionTime' },
				{
					label: '执行状态',
					prop: 'actionState',
					slotName: 'actionState',
				},
				{ label: '失败原因', prop: 'actionStep' },
			],
			tableData: [],
			loading: false,
			pagination: {
				current: 1, // 当前页
				total: 0, // 记录条数
				pages: 0, // 总页数
				sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
				size: 10,
			},
			pickerOptions: {
				disabledDate(time) {
					let curDate = new Date().getTime()
					let three = 6 * 30 * 24 * 3600 * 1000
					let threeMonths = curDate - three
					return (
						time.getTime() > Date.now() ||
						time.getTime() < threeMonths
					)
				},
			},
			startTime: '',
			endTime: '',
			id: this.$route.query.id,
		}
	},
	created() {
		this.formatTime(this.timeState)
		this.fn_get_table_data({
			ruleId: this.id,
			actionState: this.actionState,
			startTime: this.startTime,
			endTime: this.endTime,
		})
	},
	watch: {
		timeState(newVal) {
			let timeStamp = 0
			this.dateRange = []
			switch (newVal) {
				case 3:
					this.dateRange = []
					break
				case 2:
					{
						timeStamp = 4 * 60 * 60 * 1000
						const { currentTime, pastTime } =
							this.getPastTime(timeStamp)
						this.dateRange.push(currentTime, pastTime)
						// console.log(this.dateRange)
					}
					break
				case 1:
					{
						timeStamp = 30 * 60 * 1000
						const { currentTime, pastTime } =
							this.getPastTime(timeStamp)
						this.dateRange.push(currentTime, pastTime)
						// console.log(this.dateRange)
					}
					break
				case 0:
					{
						timeStamp = 15 * 60 * 1000
						const { currentTime, pastTime } =
							this.getPastTime(timeStamp)
						this.dateRange.push(currentTime, pastTime)
						// console.log(this.dateRange)
					}
					break
			}
		},
	},
	methods: {
		// 获取日志列表
		fn_get_table_data(params = {}) {
			params.actionState =
				params.actionState == 999 ? '' : params.actionState
			let others = { ...fn_util__filter_null(params) }
			others.size = this.pagination.size
			others.current = this.pagination.current
			others.descs = true
			getLogList(others)
				.then((res) => {
					if (res.code == 200) {
						this.tableData = res.data.records
						this.pagination.total = res.data.total
						// this.pagination.current = res.data.current;
						// this.pagination.pages = res.data.pages;
						// this.pagination.size = res.data.size;
					} else {
						this.$newNotify.error({
							message: res.message,
						})
					}
				})
				.finally(() => {
					setTimeout(() => {
						this.loading = false
					}, 300)
				})
		},
		formatTime(type) {
			let format
			let { yy, MM, dd, hh, mm, ss, timestamp } = fn_util__date_format()
			this.endTime = `${yy}-${MM}-${dd} ${hh}:${mm}:${ss}`
			// timestamp 13位时间戳
			if (type == 0) {
				// 15分钟
				let time = timestamp - 15 * 60 * 1000
				format = fn_util__date_format(time)
			} else if (type == 1) {
				// 30分钟
				let time = timestamp - 30 * 60 * 1000
				format = fn_util__date_format(time)
			} else if (type == 2) {
				// 4小时
				let time = timestamp - 4 * 60 * 60 * 1000
				format = fn_util__date_format(time)
			} else if (type == 3) {
				// 自定义
				if (this.dateRange && this.dateRange.length > 0) {
					format = fn_util__date_format(this.dateRange[0])
					let endFormat = fn_util__date_format(this.dateRange[1])
					this.endTime = `${endFormat.yy}-${endFormat.MM}-${endFormat.dd} ${endFormat.hh}:${endFormat.mm}:${endFormat.ss}`
				} else return
			}
			console.log('format', format)
			this.startTime = `${format.yy}-${format.MM}-${format.dd} ${format.hh}:${format.mm}:${format.ss}`
		},
		selectStatus() {
			this.pagination.current = 1
			this.formatTime(this.timeState)
			let params = {
				ruleId: this.id,
				actionState: this.actionState,
				startTime: this.startTime,
				endTime: this.endTime,
			}
			this.fn_get_table_data(params)
		},
		selectTimeStatus(value) {
			this.pagination.current = 1
			this.formatTime(this.timeState)
			let params = {
				ruleId: this.id,
				actionState: this.actionState,
				startTime: this.startTime,
				endTime: this.endTime,
			}
			if (value != 3) {
				this.fn_get_table_data(params)
			}
		},
		pickerChange() {
			this.pagination.current = 1
			this.formatTime(this.timeState)
			let params = {
				ruleId: this.id,
				actionState: this.actionState,
				startTime: this.startTime,
				endTime: this.endTime,
			}
			this.fn_get_table_data(params)
		},
		// 当前页总条数
		handleSizeChange(val) {
			console.log(`每页 ${val} 条`)
			this.pagination.current = 1
			this.pagination.size = val
			this.formatTime(this.timeState)
			this.fn_get_table_data({
				ruleId: this.id,
				actionState: this.actionState,
				startTime: this.startTime,
				endTime: this.endTime,
			})
		},
		// 当前页
		handleCurrentChange(val) {
			console.log(`当前页: ${val}`)
			this.pagination.current = val
			this.formatTime(this.timeState)
			this.fn_get_table_data({
				ruleId: this.id,
				actionState: this.actionState,
				startTime: this.startTime,
				endTime: this.endTime,
			})
		},

		// 获取时间节点
		getPastTime(timeStamp) {
			const date = new Date()
			let currentTimeStamp = date.getTime()
			let pastTimeStamp = currentTimeStamp - timeStamp
			const current = fn_util__date_format(currentTimeStamp)
			const past = fn_util__date_format(pastTimeStamp)
			let currentTime = `${current.yy}-${current.MM}-${current.dd} ${current.hh}:${current.mm}:${current.ss}`
			let pastTime = `${past.yy}-${past.MM}-${past.dd} ${past.hh}:${past.mm}:${past.ss}`
			return { currentTime, pastTime }
		},
	},
}
</script>
<style lang="scss" scoped>
.log-detail {
	.log-top {
		margin-top: 18px;
		.el-select {
			width: 240px;
			margin-right: 14px;
			:deep(.el-input__inner) {
				border-radius: 0;
			}
		}
		.el-range-editor {
			border-radius: 0;
			:deep(.el-input__inner) {
				padding: 0;
			}
			:deep(.el-input__icon) {
				height: auto;
			}
		}
	}
	.log-content {
		margin-top: 18px;
		.state-edit {
			span {
				font-size: 14px;
				font-weight: 400;
			}
			.success {
				color: #00c250;
			}
			.fail {
				color: #ff4d4f;
			}
		}
	}
	.log-bottom {
		text-align: right;
		margin-top: 14px;
	}
}
:deep(.el-range-separator) {
	margin-bottom: 6px;
}
</style>
