<template>
  <div class="edgeNode">
    <topCard
      :searchKey.sync="searchKey"
      @searchNodeName="fn_search"
      @resetNodeName="fn_reset"
    ></topCard>

    <div class="nodeListBox">
      <div class="top">
        <h4>节点列表</h4>
        <div class="btnBox">
          <IotButton text="新建节点" @search="fn_create_node"></IotButton>
          <IotButton
            :text="'批量删除' + '(' + delIdsListLength + ')'"
            type="white"
            @search="fn_delete_node"
          ></IotButton>
        </div>
      </div>

      <div class="nodeTable">
        <IotTable
          :columns="columns"
          ref="iottable"
          :data="tableData"
          :loading="loading"
          @selection-del="handleSelectiondel"
          @selection-change="handleSelectionChange"
        >
          <template slot="status" slot-scope="{ row }">
            <div class="table-status">
              <div class="status flex" v-if="row.status == 1">
                <div class="red"></div>
                <div>未安装</div>
              </div>
              <div class="status flex" v-if="row.status == 2">
                <div class="green"></div>
                <div>已安装</div>
              </div>
              <div class="status flex" v-if="row.status == 3">
                <div class="yellow"></div>
                <div>删除中</div>
              </div>
            </div>
          </template>
          <template slot="operation" slot-scope="{ row }">
            <div class="flex table-edit">
              <p @click="fn_get_script(row.id)" class="color2 btn">获取安装脚本</p>
              <p class="table-line"></p>
              <p @click="fn_toEdit(row)" class="color2 btn">管理节点</p>
              <p class="table-line"></p>
              <p @click="fn_del(row.id)" class="color2 btn">删除节点</p>
            </div>
          </template>
        </IotTable>
      </div>

      <div class="produce-bottom" v-if="tableData.length">
        <!-- 分页 -->
        <IotPagination
          :pagination="pagination"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </div>

    <!-- 新建节点弹窗 -->
    <addEdgeNodeDevice
      ref="AEND"
      @dialogSure="dialogSure"
      :dialogTitle="dialogTitle"
    >
    </addEdgeNodeDevice>

    <!-- 获取脚本弹窗 -->
    <div class="installBox">
      <IotDialog
        :visible.sync="installVisible"
        @close="close_install_dialog"
        :title="install_dialogTitle"
        :footer="false"
        width="50%"
      >
        <template #body>
          <div class="tip">
            请选择支持架构，复制安装命令。然后到边缘节点设备上执行命令，完成边缘软件部署：
          </div>
          <IotForm>
            <template #default>
              <el-form :model="installForm" ref="installForm">
                <el-form-item label="选择架构" prop="Architecture">
                  <el-select
                    v-model="installForm.Architecture"
                    placeholder="请选择架构"
                  >
                    <el-option
                      v-for="item in installOption"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    ></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item
                  label="输入安装目录"
                  prop="installDir"
                  :rules="[
                    {
                      required: true,
                      message: '安装目录不能为空',
                      trigger: 'blur',
                    },
                  ]"
                >
                  <el-input v-model="installForm.installDir"></el-input>
                </el-form-item>
              </el-form>
            </template>
          </IotForm>

          <textarea v-model="installScript" class="installScript"> </textarea>

          <div class="tip">
            <i class="el-icon-s-promotion"></i>
            安装命令30分钟内有效，如超时间，请重新获取安装命令
          </div>

          <div class="footer">
            <IotButton
              text="我知道了"
              type="warning"
              @search="fn_sure_install"
            ></IotButton>
          </div>
        </template>
      </IotDialog>
    </div>
  </div>
</template>

<script>
import IotButton from "@/components/iot-button/index.vue";
import IotDialog from "@/components/iot-dialog/index.vue";
import IotPagination from "@/components/iot-pagination";
import IotTable from "@/components/iot-table/index.vue";
import IotForm from "@/components/iot-form/index.vue";
import addEdgeNodeDevice from "../components/addEdgeNodeDevice/index";
import topCard from "./components/topCard";
import {
  getEdgeNodeList,
  createEdgeNode,
  getEdgeDeviceInstall,
  deleteEdgeNode,
} from "@/api/edgeManagement.js";
import { fn_util__filter_null } from "@/util/util.js";
export default {
  name: "edgeNode",
  components: {
    IotButton,
    IotDialog,
    IotTable,
    IotPagination,
    IotForm,
    addEdgeNodeDevice,
    topCard,
  },
  data() {
    return {
      searchKey: "",
      nodeListData: {
        current: 1,
        size: 10,
        name: "",
        productKey: "",
      },
      visible: false,
      dialogTitle: "新建节点",
      tableData: [],
      columns: [
        { type: "selection" },
        { label: "节点名称", prop: "name" },
        { label: "节点网络", prop: "deviceSecret" },
        { label: "设备名称", prop: "deviceName" },
        { label: "状态", prop: "status", slotName: "status" },
        { label: "产品key", prop: "productKey" },
        { label: "创建时间", prop: "createTime" },
        {
          label: "操作",
          prop: "operation",
          slotName: "operation",
          width: "300px",
        },
      ],
      loading: true,
      pagination: {
        current: 1, // 当前页
        total: 0, // 记录条数
        pages: 0, // 总页数
        sizes: [10, 20, 50, 100], // 每一页的数量  [10, 20, 50, 100]
        size: 10,
      },
      install_dialogTitle: "操作成功",
      installVisible: false,
      installForm: {
        Architecture: "",
        installDir: null,
      },
      delIdsListLength: 0,
      installOption: [
        {
          value: "1",
          label: "x86_64",
        },
        {
          value: "2",
          label: "arm32",
        },
        {
          value: "3",
          label: "arm64",
        },
      ],
      installScript: "测试脚本",
    };
  },
  created() {
    this.getNodeList();
  },
  methods: {
    getNodeList() {
      this.nodeListData.size = this.pagination.size;
      this.nodeListData.current = this.pagination.current;
      const data = fn_util__filter_null(this.nodeListData);
      this.loading = true;
      getEdgeNodeList(data)
        .then((res) => {
          if (res.code == 200) {
            this.tableData = res.data.records;
            this.pagination.total = res.data.total;
            this.pagination.current = res.data.current;
            this.pagination.pages = res.data.pages;
            this.pagination.size = res.data.size;
            // console.log("pagination", this.pagination);
            this.$newNotify.success({
              message: res.message,
            });
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .finally(() => {
          setTimeout(() => {
            this.loading = false;
          }, 300);
        });
    },
    fn_create_node() {
      this.$refs.AEND.visible = true;
    },
    dialogSure(dialogNodeVal) {
      const data = {
        name: dialogNodeVal,
      };
      createEdgeNode(data)
        .then((res) => {
          // console.log("res98", res);
          if (res.code == 200) {
            this.$newNotify.success({
              message: res.message,
            });
            this.getNodeList();
          } else {
            this.$newNotify.error({
              message: res.message,
            });
          }
        })
        .catch((err) => {
          this.$newNotify.error({
            message: err.message,
          });
        });
    },
    fn_toEdit(row) {
      this.$router.push({ name: "edgeDevice", params: row });
      console.log("editRow", row);
    },
    fn_del(rowId, msg = "是否删除节点？") {
      // console.log("rowId", rowId);
      this.$confirm(msg, "温馨提示")
        .then(() => {
          deleteEdgeNode({ ids: rowId }).then((res) => {
            console.log("res", res);
            if (res.code == 200) {
              this.$newNotify.success({
                message: res.message,
              });
              this.getNodeList();
            } else {
              this.$newNotify.error({
                message: res.message,
              });
            }
          });
        })
        .catch(() => {
          this.$newNotify.warning({
            message: "取消删除",
          });
        });
    },
    handleSizeChange(val) {
      this.pagination.size = val;
      this.getNodeList();
    },
    handleCurrentChange(val) {
      this.pagination.current = val;
      this.getNodeList();
    },
    fn_get_script(rowId) {
      console.log("rowId", rowId);
      getEdgeDeviceInstall({ id: rowId }).then((res) => {
        console.log("res208", res);
        if (res.code == 200) {
          this.installVisible = true;
        } else {
          this.$newNotify.error({
            message: res.message,
          });
        }
      });
    },
    close_install_dialog() {
      this.installVisible = false;
    },
    // 确定安装脚本
    fn_sure_install() {
      this.$refs.installForm.validate((valid) => {
        // console.log("valid",valid);
        if (valid) {
          console.log("安装脚本");
          this.installVisible = false;
        } else {
          this.$newNotify.warning({
            message: "请输入安装路径",
          });
        }
      });
    },
    handleSelectiondel(val) {
      const ids = val.toString();
      this.fn_del(ids, "是否批量删除节点？");
    },
    // 批量删除
    fn_delete_node() {
      this.$refs.iottable.fn_del_selection_data();
    },
    fn_search() {
      if (!this.searchKey.trim()) {
        this.$newNotify.warning({
          message: "请输入节点名称",
        });
        return;
      }
      this.nodeListData.name = this.searchKey.trim();
      this.resetPaginationData();
      this.getNodeList();
    },
    fn_reset() {
      this.nodeListData.name = "";
      this.searchKey = "";
      this.resetPaginationData();
      this.getNodeList();
    },
    resetPaginationData() {
      this.pagination.current = 1;
      this.pagination.size = 10;
    },
    handleSelectionChange(val) {
      // console.log("val",val);
      this.delIdsListLength = val.length;
      console.log("delIdsListLength", this.delIdsListLength);
    },
  },
};
</script>

<style scoped lang="scss">
.edgeNode {
  width: 100%;
  height: calc(100vh - 75px);
  padding: 22px 22px 0px 22px;
  padding-bottom: 20px;
  background-color: #e6e6e6;
  .nodeListBox {
    width: 100%;
    padding: 15px;
    background-color: #fff;
    .top {
      width: 100%;
      height: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 10px;
      .btnBox {
        :first-child {
          margin-right: 10px;
        }
      }
    }
    .nodeTable {
      padding: 0 32px;
      .table-line {
        margin: 0px 12px;
        width: 1px;
        height: 13px;
        border: 1px solid #ededed;
      }

      .table-status {
        .status {
          .red {
            background: #ff4d4f;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
          .green {
            background: #00c250;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
          .yellow {
            background: #e6a23c;
            width: 8px;
            height: 8px;
            border-radius: 4px;
            margin-top: 8px;
            margin-right: 6px;
          }
        }
      }
    }
    .produce-bottom {
      text-align: right;
      margin-top: 14px;
    }
  }

  .installBox {
    .tip {
      font-size: 12px;
    }
    /deep/.el-form-item {
      display: flex;
      width: 320px;
      justify-content: space-between;
      align-items: center;
    }

    /deep/.el-form-item__content {
      width: 200px;
      height: 30px;
    }

    /deep/.el-form-item__label {
      flex: 1;
      text-align: start;
    }
    .footer {
      text-align: center;
      line-height: 60px;

      .iot-button-warning {
        display: inline;
      }
    }

    .installScript {
      width: 100%;
      background-color: #e9edfa;
      &:focus {
        border: none;
      }
    }
  }
}

.btn{
  cursor: pointer;
}
</style>
