<!--
 * @Description: 1.0
 * @Version: 1.0
 * @Autor: hs
 * @Date: 2021-11-08 09:51:45
 * @LastEditors: hs
 * @LastEditTime: 2021-11-23 19:29:21
-->
<template>
  <div class="viewport">
    <top-bar />
    <div class="content flex">
      <side-bar></side-bar>
      <div class="content-box" ref="contentBox" id="contentBox">
        <div class="content-bg">
          <div class="content-header" v-if="this.$route.meta.crumb.length>2">
            <div class="crumb">
              <el-breadcrumb separator="/" class="right-crumb">
                <el-breadcrumb-item
                  :to="toPath(crumb)"
                  v-for="crumb in crumbList"
                  :key="crumb.sort"
                  >{{ crumb.name }}</el-breadcrumb-item
                >
              </el-breadcrumb>
            </div>

            <div class="title">
              <img
                @click="handleGoBack"
                v-if="crumbList.length >= 3"
                src="~@/assets/images/index/goBack.svg"
                alt
              />
              <span>{{ title }}</span>
              <span v-if="statusText" :class="['status', active]">{{statusText}}</span>
            </div>
          </div>
          <div
            class="content-bottom"
            :style="{ minHeight: minHeight }"
            v-if="isRouteAlive"
          >
            <keep-alive>
              <router-view
                v-if="$route.meta.isKeepActive"
                :key="$route.fullPath"
              ></router-view>
            </keep-alive>
            <router-view
              v-if="!$route.meta.isKeepActive"
              :key="$route.fullPath"
            ></router-view>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import sideBar from "@/components/sidebar";
import topBar from "@/components/topbar";
import { mapGetters } from "vuex";
import toVW from "@/util/toVW.js";
export default {
  components: { sideBar, topBar },
  data() {
    return {
      zoom: {},
      statusText: "",
      active: "",
      isRouteAlive: true,
    };
  },
  provide() {
    return {
      reload: this.reload,
    };
  },
  computed: {
    ...mapGetters(["layoutInfo", "userInfo"]),
    minHeight() {
      if (document.body.offsetHeight === 968) {
        return toVW(797);
      } else {
        return toVW(764);
      }
    },
    crumbList() {
      const { meta } = this.$route;
      return meta.crumb || [];
    },
    title() {
      if (this.$route.query.id) {
        if (this.layoutInfo.title) {
          return this.layoutInfo.title;
        } else {
          return this.$route.meta.title;
        }
      } else {
        return this.$route.meta.title;
      }
    },
    status() {
      // console.log(this.$route.query.id,'4444');
      if (this.$route.query.id) {
        let status = this.layoutInfo.status;
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.statusText = ["未激活", "在线", "离线"][status - 4];
        // eslint-disable-next-line vue/no-side-effects-in-computed-properties
        this.active = `active${status - 4}`;
        return this.layoutInfo.status;
      } else {
        return "";
      }
    },
  },
  created() {
    if (!this.userInfo.user_id) {
      let userInfo = localStorage.getItem("userInfo");
      this.$store.dispatch("setUserInfo", JSON.parse(userInfo));
    }
  },
  methods: {
    handleGoBack() {
      if (this.crumbList.length >= 3) {
        this.$store.dispatch("setLayoutInfo", {});
        this.statusText = "";
        this.$router.back();
      } else {
        return;
      }
    },
    toPath(item) {
      // console.log(item);
      if (item.sort == 1) {
        return {
          path: item.url,
        };
      }{
        return undefined;
      }
    },
    reload() {
      this.isRouteAlive = false;
      this.$nextTick(() => {
        this.isRouteAlive = true;
      });
    },
  },
  watch: {
    $route(val) {
      // console.log(val)
      if (
        val.matched[2].path !== "deviceDetail" ||
        val.matched[2].path === "deviceChildDetail" ||
        val.matched[2].path !== "monitorMaintain"
      ) {
        this.statusText = "";
      }
    },
    status(val) {
      // console.log(val,'%%');
      if (val || val==0) {
        if (
          this.$route.name === "deviceDetail" ||
          this.$route.name === "deviceChildDetail" ||
          this.$route.name === "gatewaydetail"
        ) {
          const status = +this.status;
          this.statusText = ["未激活", "在线", "离线"][status - 4];
          this.active = `active${status - 4}`;
        } 
        else if(
          this.$route.name === "eventHanding" ||
          this.$route.name === "alarmeventHanding" ||
          this.$route.name === "todayEventHanding"
        ){
          const status = +this.status;
          this.statusText = ["未处理", "已关闭"][status];
          this.active = `activeB${status}`;
        }
        else{
          this.statusText = "";
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.viewport {
  padding-top: 67px;
  height: 100vh;
  .content {
    height: 100%;
    margin-top: 10px;
    align-items: flex-start;
    .content-box {
      width: 100%;
      height: 100%;
      overflow: auto;
      // padding: 8px;
      box-sizing: border-box;
      .content-bg {
        width: 100%;
        min-height: 100%;
        background: #fff;
        .content-header {
          height: 90px;
          border-bottom: 1px solid #eeeff1;
          // width: 100%;
          // padding-left: 20px;
          margin: 0 32px;
          box-sizing: border-box;
          .crumb {
            display: flex;
            justify-content: space-between;
            height: 40px;
            line-height: 40px;
            align-items: center;
            // margin-top: 20px;
            .right-crumb {
              height: 15px;
              border-bottom: none;
              margin-top: 10px;
              font-size: 12px;
            }
            .el-breadcrumb__item {
              .el-breadcrumb__inner {
                font-weight: 400 !important;
              }
            }
          }
          .title {
            font-size: 28px;
            font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Medium;
            font-weight: 500;
            color: #262626;
            display: flex;
            align-items: center;
            img {
              cursor: pointer;
              margin-right: 12px;
            }
            .status {
              background: rgba(255, 77, 79, 0.1);
              border-radius: 50px;
              padding: 5px 9px;
              font-size: 12px;
              font-family: HarmonyOS Sans SC, HarmonyOS Sans SC-Regular;
              font-weight: 400;
              color: #ff4d4f;
              margin-left: 10px;
            }

            .active0 {
              background: rgba(230, 162, 60, 0.1);
              color: rgba(230, 162, 60, 1);
            }
            .active1 {
              background: rgba(0, 194, 80, 0.1);
              color: rgba(0, 194, 80, 1);
            }
            .active2 {
              background: rgba(255, 77, 79, 0.1);
              color: rgba(255, 77, 79, 1);
            }



            .activeB0{
              background: rgba(255, 77, 79, 0.1);
              color: rgba(255, 77, 79, 1);
            }
            .activeB1{
              background: rgba(81, 81, 81, 0.1);
              color: rgba(81, 81, 81, 1);
            }
          }
        }
      }
    }
    .content-bottom {
      // width: 100%;
      min-width: 1295px;
      // height: 100%;
      // min-height: 730px;
      // padding: 0px 32px 0 32px;
      box-sizing: border-box;
    }
  }
}
</style>
